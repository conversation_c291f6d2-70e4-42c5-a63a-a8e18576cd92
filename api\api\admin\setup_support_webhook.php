<?php
/**
 * api/admin/setup_support_webhook.php
 * Скрипт для установки webhook бота поддержки
 */

require_once __DIR__ . '/support_config.php';
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Настройка Webhook бота поддержки</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h1 class="h3 mb-0">
                            <i class="bi bi-robot"></i>
                            Настройка Webhook бота поддержки
                        </h1>
                    </div>
                    <div class="card-body">
                        <?php
                        echo "<div class='alert alert-info'>";
                        echo "<strong>Бот:</strong> @" . SUPPORT_BOT_USERNAME . "<br>";
                        echo "<strong>Webhook URL:</strong> " . SUPPORT_WEBHOOK_URL;
                        echo "</div>";

                        // Устанавливаем webhook
                        echo "<h5>Установка webhook...</h5>";
                        $result = supportTelegramRequest('setWebhook', [
                            'url' => SUPPORT_WEBHOOK_URL,
                            'drop_pending_updates' => true
                        ]);

                        if ($result && $result['ok']) {
                            echo "<div class='alert alert-success'>";
                            echo "<i class='bi bi-check-circle'></i> ✅ Webhook успешно установлен!";
                            echo "</div>";
                        } else {
                            echo "<div class='alert alert-danger'>";
                            echo "<i class='bi bi-exclamation-triangle'></i> ❌ Ошибка при установке webhook";
                            if (isset($result['description'])) {
                                echo "<br><strong>Ошибка:</strong> " . $result['description'];
                            }
                            echo "</div>";
                        }

                        // Проверяем информацию о боте
                        echo "<h5>Информация о боте:</h5>";
                        $botInfo = supportTelegramRequest('getMe', []);

                        if ($botInfo && $botInfo['ok']) {
                            $bot = $botInfo['result'];
                            echo "<div class='table-responsive'>";
                            echo "<table class='table table-sm'>";
                            echo "<tr><td><strong>ID:</strong></td><td>" . $bot['id'] . "</td></tr>";
                            echo "<tr><td><strong>Имя:</strong></td><td>" . $bot['first_name'] . "</td></tr>";
                            echo "<tr><td><strong>Username:</strong></td><td>@" . $bot['username'] . "</td></tr>";
                            echo "<tr><td><strong>Может присоединяться к группам:</strong></td><td>" . ($bot['can_join_groups'] ? 'Да' : 'Нет') . "</td></tr>";
                            echo "<tr><td><strong>Может читать все сообщения:</strong></td><td>" . ($bot['can_read_all_group_messages'] ? 'Да' : 'Нет') . "</td></tr>";
                            echo "<tr><td><strong>Поддерживает inline:</strong></td><td>" . ($bot['supports_inline_queries'] ? 'Да' : 'Нет') . "</td></tr>";
                            echo "</table>";
                            echo "</div>";
                        }

                        // Проверяем информацию о webhook
                        echo "<h5>Информация о webhook:</h5>";
                        $webhookInfo = supportTelegramRequest('getWebhookInfo', []);

                        if ($webhookInfo && $webhookInfo['ok']) {
                            $webhook = $webhookInfo['result'];
                            echo "<div class='table-responsive'>";
                            echo "<table class='table table-sm'>";
                            echo "<tr><td><strong>URL:</strong></td><td>" . ($webhook['url'] ?: 'Не установлен') . "</td></tr>";
                            echo "<tr><td><strong>Есть пользовательский сертификат:</strong></td><td>" . ($webhook['has_custom_certificate'] ? 'Да' : 'Нет') . "</td></tr>";
                            echo "<tr><td><strong>Количество ожидающих обновлений:</strong></td><td>" . $webhook['pending_update_count'] . "</td></tr>";
                            if (isset($webhook['last_error_date'])) {
                                echo "<tr><td><strong>Последняя ошибка:</strong></td><td>" . date('d.m.Y H:i:s', $webhook['last_error_date']) . "</td></tr>";
                                if (isset($webhook['last_error_message'])) {
                                    echo "<tr><td><strong>Сообщение ошибки:</strong></td><td>" . $webhook['last_error_message'] . "</td></tr>";
                                }
                            }
                            if (isset($webhook['max_connections'])) {
                                echo "<tr><td><strong>Максимум соединений:</strong></td><td>" . $webhook['max_connections'] . "</td></tr>";
                            }
                            echo "</table>";
                            echo "</div>";
                        }
                        ?>

                        <div class="mt-4">
                            <a href="support.php" class="btn btn-primary">
                                <i class="bi bi-arrow-left"></i> Вернуться к поддержке
                            </a>
                            <button onclick="location.reload()" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-clockwise"></i> Обновить
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
