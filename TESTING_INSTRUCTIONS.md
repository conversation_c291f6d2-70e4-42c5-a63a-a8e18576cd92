# 🧪 Инструкции по тестированию счетчиков рекламы

## 🚀 Быстрый старт

### 1. Тестирование в основном приложении

1. Откройте основное приложение: `https://argun-defolt.loc/`
2. Дождитесь полной загрузки всех модулей
3. Обратите внимание на кнопки рекламы - под каждой должен отображаться счетчик
4. Нажмите на любую кнопку рекламы и посмотрите, как уменьшается счетчик

### 2. Тестирование на отдельной странице

1. Откройте тестовую страницу: `https://argun-defolt.loc/test_ad_counters.html`
2. Используйте кнопки управления для тестирования различных сценариев
3. Проверьте переключение языков
4. Протестируйте сброс счетчиков

## 📱 Что должно отображаться

### Русский язык
```
┌─────────────────────────────────┐
│  🔗  Открыть ссылку        +10  │
│      осталось 20 показов        │
└─────────────────────────────────┘

┌─────────────────────────────────┐
│  📺  Смотреть видео         +1  │
│      осталось 20 показов        │
└─────────────────────────────────┘

┌─────────────────────────────────┐
│  🖥️  Кликнуть по баннеру   +10  │
│      осталось 20 показов        │
└─────────────────────────────────┘
```

### Английский язык
```
┌─────────────────────────────────┐
│  🔗  Open Link             +10  │
│      20 ad views left           │
└─────────────────────────────────┘

┌─────────────────────────────────┐
│  📺  Watch Video            +1  │
│      20 ad views left           │
└─────────────────────────────────┘

┌─────────────────────────────────┐
│  🖥️  Tap and Earn          +10  │
│      20 ad views left           │
└─────────────────────────────────┘
```

## 🔍 Сценарии тестирования

### Сценарий 1: Базовая функциональность
1. ✅ Счетчики отображаются при загрузке страницы
2. ✅ Счетчики уменьшаются при нажатии на кнопки рекламы
3. ✅ При достижении 0 показов счетчик меняет цвет на красноватый

### Сценарий 2: Локализация
1. ✅ Переключение на английский язык изменяет текст счетчиков
2. ✅ Переключение обратно на русский восстанавливает русские тексты
3. ✅ Русские склонения работают корректно (1 показ, 2-4 показа, 5+ показов)

### Сценарий 3: Сохранение состояния
1. ✅ Обновление страницы сохраняет текущие значения счетчиков
2. ✅ Закрытие и повторное открытие браузера сохраняет состояние
3. ✅ Счетчики сбрасываются в новый день (можно симулировать изменением даты)

### Сценарий 4: Интеграция с рекламой
1. ✅ Счетчики увеличиваются только при успешном просмотре рекламы
2. ✅ При ошибке показа рекламы счетчик не изменяется
3. ✅ Достижение лимита блокирует соответствующую кнопку

## 🛠️ Отладка

### Консоль браузера
Откройте консоль разработчика (F12) и ищите сообщения:
```
[AdCounters] 📊 Модуль загружен
[AdCounters] ✅ Инициализирован
[AdCounters] 🌐 Локализация найдена
[AdCounters] Обновлен счетчик native_banner: осталось 19 показов
```

### Проверка localStorage
В консоли выполните:
```javascript
// Просмотр всех счетчиков
Object.keys(localStorage).filter(key => key.startsWith('ad_count_'))

// Просмотр конкретного счетчика
localStorage.getItem('ad_count_native_banner_2025-01-02')

// Сброс всех счетчиков
window.adCountersManager.resetAllCounters()
```

### Проверка состояния модуля
```javascript
// Информация о всех лимитах
window.adCountersManager.getAllLimitsInfo()

// Проверка инициализации
window.adCountersManager.isInitialized

// Текущий язык
window.appLocalization.currentLanguage
```

## ❗ Возможные проблемы

### Счетчики не отображаются
- Проверьте, что модуль `ad-counters.js` загружен
- Убедитесь, что элементы с ID `*-counter` существуют в HTML
- Проверьте консоль на ошибки JavaScript

### Неправильная локализация
- Убедитесь, что файлы `locales/ru.json` и `locales/en.json` содержат новые ключи
- Проверьте, что система локализации инициализирована
- Попробуйте принудительно переключить язык

### Счетчики не обновляются
- Проверьте интеграцию с `ads-manager-full.js`
- Убедитесь, что вызывается `incrementCounter()` после успешного просмотра
- Проверьте, что localStorage доступен в браузере

## 📞 Поддержка

При возникновении проблем:
1. Проверьте консоль браузера на ошибки
2. Убедитесь, что все файлы загружены корректно
3. Попробуйте сбросить счетчики и перезагрузить страницу
4. Используйте тестовую страницу для изолированного тестирования
