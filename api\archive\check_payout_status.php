<?php
require_once 'api/config.php';
require_once 'api/NOWPaymentsAPI.php';

$api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);
$payoutId = '5003151124';

try {
    $response = $api->getPayoutStatus($payoutId);
    echo "Текущий статус выплаты $payoutId:\n";
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
    echo "Ошибка при проверке статуса выплаты: " . $e->getMessage();
}
?>