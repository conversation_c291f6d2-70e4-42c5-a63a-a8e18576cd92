<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Отладка счетчиков рекламы</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .debug-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 1px solid #444;
        }
        .button {
            background: #FFC107;
            color: #000;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        .button:hover {
            background: #FFD54F;
        }
        .log-display {
            background: #333;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .counter-display {
            background: #444;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
        }
        .status.success { background: #4CAF50; }
        .status.warning { background: #FF9800; }
        .status.error { background: #F44336; }
    </style>
</head>
<body>
    <h1>🐛 Отладка счетчиков рекламы</h1>

    <div class="debug-section">
        <h2>📊 Текущее состояние счетчиков</h2>
        <div id="counters-state">
            Загрузка...
        </div>
        <button class="button" onclick="updateCountersState()">🔄 Обновить состояние</button>
    </div>

    <div class="debug-section">
        <h2>🧪 Тестирование функций</h2>
        <button class="button" onclick="testIncrement('native_banner')">📈 Тест native_banner</button>
        <button class="button" onclick="testIncrement('rewarded_video')">📈 Тест rewarded_video</button>
        <button class="button" onclick="testIncrement('interstitial')">📈 Тест interstitial</button>
        <br><br>
        <button class="button" onclick="checkLocalStorage()">💾 Проверить localStorage</button>
        <button class="button" onclick="resetCounters()">🔄 Сбросить счетчики</button>
        <button class="button" onclick="clearLog()">🧹 Очистить лог</button>
    </div>

    <div class="debug-section">
        <h2>📝 Лог событий</h2>
        <div class="log-display" id="debug-log">
            Лог событий будет отображаться здесь...
        </div>
    </div>

    <div class="debug-section">
        <h2>🔍 Данные localStorage</h2>
        <div class="log-display" id="localStorage-data">
            Данные localStorage будут отображаться здесь...
        </div>
    </div>

    <script>
        // Мини-версия локализации для тестирования
        window.appLocalization = {
            isLoaded: true,
            currentLanguage: 'ru',
            translations: {
                'ru': {
                    'tasks.ad_views_left': 'осталось {count} показов',
                    'tasks.ad_views_left_single': 'остался {count} показ',
                    'tasks.ad_views_left_few': 'осталось {count} показа'
                },
                'en': {
                    'tasks.ad_views_left': '{count} ad views left'
                }
            },
            get: function(key, params = {}) {
                const translation = this.translations[this.currentLanguage]?.[key] || key;
                return translation.replace(/\{(\w+)\}/g, (match, param) => params[param] || match);
            }
        };

        function logEvent(message) {
            const logDiv = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateCountersState() {
            if (!window.adCountersManager) {
                logEvent('❌ AdCountersManager не найден');
                return;
            }

            const info = window.adCountersManager.getAllLimitsInfo();
            const stateDiv = document.getElementById('counters-state');
            
            let html = '';
            Object.keys(info).forEach(key => {
                if (key !== '_resetInfo') {
                    const data = info[key];
                    const statusClass = data.isLimitReached ? 'error' : (data.remaining < 5 ? 'warning' : 'success');
                    html += `
                        <div class="counter-display">
                            <span><strong>${key}:</strong> ${data.current}/${data.limit} (осталось: ${data.remaining})</span>
                            <span class="status ${statusClass}">${data.isLimitReached ? 'ЛИМИТ' : 'ОК'}</span>
                        </div>
                    `;
                }
            });

            stateDiv.innerHTML = html;
            logEvent('✅ Состояние счетчиков обновлено');
        }

        function testIncrement(adType) {
            logEvent(`🧪 Тестирование incrementCounter для ${adType}`);
            
            if (!window.adCountersManager) {
                logEvent('❌ AdCountersManager не найден');
                return;
            }

            const beforeCount = window.adCountersManager.getTodayAdCount(adType);
            logEvent(`📊 До: ${adType} = ${beforeCount}`);

            const newCount = window.adCountersManager.incrementCounter(adType);
            logEvent(`📊 После: ${adType} = ${newCount}`);

            updateCountersState();
            checkLocalStorage();
        }

        function checkLocalStorage() {
            const localStorageDiv = document.getElementById('localStorage-data');
            let data = 'Ключи счетчиков в localStorage:\n\n';
            
            const today = new Date().toISOString().split('T')[0];
            const keys = [];
            
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith('ad_count_')) {
                    keys.push(key);
                }
            }
            
            if (keys.length === 0) {
                data += 'Нет ключей счетчиков в localStorage\n';
            } else {
                keys.sort().forEach(key => {
                    const value = localStorage.getItem(key);
                    data += `${key} = ${value}\n`;
                });
            }
            
            localStorageDiv.textContent = data;
            logEvent(`💾 Проверено ${keys.length} ключей в localStorage`);
        }

        function resetCounters() {
            if (window.adCountersManager) {
                window.adCountersManager.resetAllCounters();
                updateCountersState();
                checkLocalStorage();
                logEvent('🔄 Все счетчики сброшены');
            }
        }

        function clearLog() {
            document.getElementById('debug-log').textContent = '';
        }

        // Перехватываем console.log для отображения в нашем логе
        const originalConsoleLog = console.log;
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
            ).join(' ');
            
            if (message.includes('[AdCounters]') || message.includes('[AdsManager]')) {
                logEvent(`🔍 ${message}`);
            }
        };

        // Инициализация
        window.addEventListener('load', () => {
            logEvent('🚀 Страница отладки загружена');
            
            const checkModules = setInterval(() => {
                if (window.adCountersManager) {
                    clearInterval(checkModules);
                    logEvent('✅ AdCountersManager найден');
                    window.adCountersManager.init().then(() => {
                        logEvent('✅ AdCountersManager инициализирован');
                        updateCountersState();
                        checkLocalStorage();
                    });
                }
            }, 100);
            
            // Таймаут на случай если модуль не загрузится
            setTimeout(() => {
                if (!window.adCountersManager) {
                    logEvent('❌ AdCountersManager не загрузился за 10 секунд');
                }
            }, 10000);
        });
    </script>

    <!-- Подключаем модуль счетчиков -->
    <script src="js/ad-counters.js"></script>
</body>
</html>
