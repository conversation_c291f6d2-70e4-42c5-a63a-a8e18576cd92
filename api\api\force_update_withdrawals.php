<?php
/**
 * Принудительное обновление статусов всех выплат
 */

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/db_mock.php';
require_once __DIR__ . '/NOWPaymentsAPI.php';

// Проверяем авторизацию администратора для AJAX запросов
if (isset($_GET['json'])) {
    session_start();
    if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'error' => 'Требуется авторизация администратора']);
        exit;
    }
}

// Проверяем, нужен ли JSON ответ
$jsonMode = isset($_GET['json']);

if ($jsonMode) {
    header('Content-Type: application/json');
}

try {
    if (!$jsonMode) {
        echo "Начинаем принудительное обновление статусов выплат...\n";
    }
    
    // Загружаем данные пользователей
    $userData = loadUserData();
    if (!$userData) {
        throw new Exception("Не удалось загрузить данные пользователей");
    }
    
    // Создаем экземпляр API
    $api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);
    
    $totalUpdated = 0;
    $totalChecked = 0;
    
    foreach ($userData as $userId => &$user) {
        if (!isset($user['withdrawals']) || empty($user['withdrawals'])) {
            continue;
        }
        
        if (!$jsonMode) {
            echo "Проверяем пользователя {$userId}...\n";
        }

        foreach ($user['withdrawals'] as $index => &$withdrawal) {
            $payoutId = $withdrawal['payout_id'] ?? null;
            $currentStatus = $withdrawal['status'] ?? 'unknown';

            if (!$payoutId) {
                if (!$jsonMode) {
                    echo "  Выплата без payout_id: {$withdrawal['id']}\n";
                }
                continue;
            }

            $totalChecked++;
            if (!$jsonMode) {
                echo "  Проверяем выплату {$payoutId} (текущий статус: {$currentStatus})...\n";
            }
            
            try {
                // Проверяем статус через NOWPayments API
                $statusResponse = $api->getPayoutStatus($payoutId);
                
                if ($statusResponse && isset($statusResponse['status'])) {
                    $newStatus = strtolower($statusResponse['status']);

                    if (!$jsonMode) {
                        echo "    API вернул статус: {$newStatus}\n";
                    }

                    // Если статус изменился
                    if ($newStatus !== $currentStatus) {
                        $oldStatus = $currentStatus;
                        $withdrawal['status'] = $newStatus;
                        $withdrawal['updated_at'] = date('Y-m-d H:i:s');

                        // Добавляем дополнительную информацию из API
                        if (isset($statusResponse['amount'])) {
                            $withdrawal['actual_amount'] = $statusResponse['amount'];
                        }
                        if (isset($statusResponse['fee'])) {
                            $withdrawal['actual_fee'] = $statusResponse['fee'];
                        }
                        if (isset($statusResponse['hash'])) {
                            $withdrawal['transaction_hash'] = $statusResponse['hash'];
                        }

                        $totalUpdated++;
                        if (!$jsonMode) {
                            echo "    ✅ Статус обновлен: {$oldStatus} -> {$newStatus}\n";
                        }
                    } else {
                        if (!$jsonMode) {
                            echo "    ℹ️ Статус не изменился\n";
                        }
                    }
                } else {
                    if (!$jsonMode) {
                        echo "    ❌ Не удалось получить статус из API\n";
                    }
                }
                
            } catch (Exception $e) {
                if (!$jsonMode) {
                    echo "    ❌ Ошибка проверки статуса {$payoutId}: " . $e->getMessage() . "\n";
                }
            }
        }
    }

    // Сохраняем обновленные данные
    if ($totalUpdated > 0) {
        if (saveUserData($userData)) {
            if (!$jsonMode) {
                echo "\n✅ Данные успешно сохранены\n";
            }
        } else {
            if (!$jsonMode) {
                echo "\n❌ Ошибка сохранения данных\n";
            }
        }
    }

    if (!$jsonMode) {
        echo "\n📊 Итоги:\n";
        echo "Проверено выплат: {$totalChecked}\n";
        echo "Обновлено статусов: {$totalUpdated}\n";
    }
    
    // Возвращаем результат в JSON для AJAX запросов
    if ($jsonMode) {
        echo json_encode([
            'success' => true,
            'checked' => $totalChecked,
            'updated' => $totalUpdated,
            'message' => "Проверено: {$totalChecked}, обновлено: {$totalUpdated}"
        ]);
    }

} catch (Exception $e) {
    if (!$jsonMode) {
        echo "❌ КРИТИЧЕСКАЯ ОШИБКА: " . $e->getMessage() . "\n";
    }

    if ($jsonMode) {
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
}
?>
