<?php
/**
 * Автоматическое обновление статусов выплат (для cron)
 * Запускать каждые 5-10 минут
 */

// Только для командной строки или cron
if (php_sapi_name() !== 'cli' && !isset($_GET['force'])) {
    http_response_code(403);
    die('Доступ запрещен. Используйте командную строку или добавьте ?force=1');
}

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/db_mock.php';
require_once __DIR__ . '/NOWPaymentsAPI.php';

// Логирование
$logFile = __DIR__ . '/cron_withdrawals.log';

function logMessage($message) {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[{$timestamp}] {$message}\n", FILE_APPEND | LOCK_EX);
    echo "[{$timestamp}] {$message}\n";
}

try {
    logMessage("Начинаем автоматическое обновление статусов выплат");
    
    // Загружаем данные пользователей
    $userData = loadUserData();
    if (!$userData) {
        throw new Exception("Не удалось загрузить данные пользователей");
    }
    
    // Создаем экземпляр API
    $api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);
    
    $totalUpdated = 0;
    $totalChecked = 0;
    $errors = 0;
    
    foreach ($userData as $userId => &$user) {
        if (!isset($user['withdrawals']) || empty($user['withdrawals'])) {
            continue;
        }
        
        foreach ($user['withdrawals'] as $index => &$withdrawal) {
            $payoutId = $withdrawal['payout_id'] ?? null;
            $currentStatus = $withdrawal['status'] ?? 'unknown';
            
            // Пропускаем уже завершенные выплаты
            if (in_array($currentStatus, ['finished', 'failed', 'rejected', 'completed', 'cancelled', 'expired'])) {
                continue;
            }
            
            if (!$payoutId) {
                continue;
            }
            
            $totalChecked++;
            
            try {
                // Проверяем статус через NOWPayments API
                $statusResponse = $api->getPayoutStatus($payoutId);
                
                if ($statusResponse && isset($statusResponse['status'])) {
                    $newStatus = strtolower($statusResponse['status']);
                    
                    // Если статус изменился
                    if ($newStatus !== $currentStatus) {
                        $oldStatus = $currentStatus;
                        $withdrawal['status'] = $newStatus;
                        $withdrawal['updated_at'] = date('Y-m-d H:i:s');
                        
                        // Добавляем дополнительную информацию из API
                        if (isset($statusResponse['amount'])) {
                            $withdrawal['actual_amount'] = $statusResponse['amount'];
                        }
                        if (isset($statusResponse['fee'])) {
                            $withdrawal['actual_fee'] = $statusResponse['fee'];
                        }
                        if (isset($statusResponse['hash'])) {
                            $withdrawal['transaction_hash'] = $statusResponse['hash'];
                        }
                        
                        $totalUpdated++;
                        logMessage("Пользователь {$userId}: Статус выплаты {$payoutId} изменен: {$oldStatus} -> {$newStatus}");
                    }
                }
                
            } catch (Exception $e) {
                $errors++;
                logMessage("ОШИБКА: Не удалось проверить статус {$payoutId}: " . $e->getMessage());
            }
        }
    }
    
    // Сохраняем обновленные данные
    if ($totalUpdated > 0) {
        if (saveUserData($userData)) {
            logMessage("Данные успешно сохранены");
        } else {
            logMessage("ОШИБКА: Не удалось сохранить данные");
        }
    }
    
    logMessage("Завершено. Проверено: {$totalChecked}, обновлено: {$totalUpdated}, ошибок: {$errors}");
    
    // Если запущено через веб, возвращаем JSON
    if (isset($_GET['json'])) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'checked' => $totalChecked,
            'updated' => $totalUpdated,
            'errors' => $errors,
            'message' => "Проверено: {$totalChecked}, обновлено: {$totalUpdated}"
        ]);
    }
    
} catch (Exception $e) {
    logMessage("КРИТИЧЕСКАЯ ОШИБКА: " . $e->getMessage());
    
    if (isset($_GET['json'])) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
    
    exit(1);
}
?>
