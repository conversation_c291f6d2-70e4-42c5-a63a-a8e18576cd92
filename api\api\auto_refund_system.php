<?php
/**
 * Автоматическая система мониторинга и возврата средств
 * Запускается через cron каждые 10 минут
 */

require_once 'config.php';
require_once 'functions.php';
require_once 'NOWPaymentsAPI.php';

// Логирование работы системы
function logRefundSystem($message, $level = 'INFO') {
    $timestamp = date('Y-m-d H:i:s');
    error_log("[$timestamp] AUTO_REFUND_SYSTEM $level: $message");
}

logRefundSystem("Запуск системы автоматического возврата средств");

try {
    // Создаем экземпляр API
    $api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);
    
    // Загружаем данные пользователей
    $userData = loadUserData();
    if (!is_array($userData)) {
        throw new Exception("Не удалось загрузить данные пользователей");
    }
    
    $totalChecked = 0;
    $totalUpdated = 0;
    $totalRefunded = 0;
    $totalRefundAmount = 0;
    
    logRefundSystem("Начинаем проверку выплат для " . count($userData) . " пользователей");
    
    foreach ($userData as $userId => &$user) {
        if (!isset($user['withdrawals']) || !is_array($user['withdrawals'])) {
            continue;
        }
        
        foreach ($user['withdrawals'] as $index => &$withdrawal) {
            $payoutId = $withdrawal['payout_id'] ?? $withdrawal['id'] ?? null;
            $currentStatus = strtolower($withdrawal['status'] ?? 'unknown');
            $coinsAmount = $withdrawal['coins_amount'] ?? 0;
            $isRefunded = $withdrawal['refunded'] ?? false;
            
            // Пропускаем выплаты без ID или уже обработанные
            if (!$payoutId || $coinsAmount <= 0) {
                continue;
            }
            
            // Пропускаем финальные статусы, которые не требуют проверки
            if (in_array($currentStatus, ['finished', 'completed', 'confirmed'])) {
                continue;
            }
            
            $totalChecked++;
            
            try {
                // Проверяем актуальный статус через API
                $statusResponse = $api->getPayoutStatus($payoutId);
                
                if ($statusResponse && isset($statusResponse['status'])) {
                    $newStatus = strtolower($statusResponse['status']);
                    
                    // Обновляем статус если изменился
                    if ($newStatus !== $currentStatus) {
                        $withdrawal['status'] = $newStatus;
                        $withdrawal['updated_at'] = date('Y-m-d H:i:s');
                        $totalUpdated++;
                        
                        logRefundSystem("Обновлен статус выплаты $payoutId: $currentStatus → $newStatus (пользователь $userId)");
                        
                        // Сохраняем дополнительные данные из API
                        if (isset($statusResponse['hash'])) {
                            $withdrawal['transaction_hash'] = $statusResponse['hash'];
                        }
                        if (isset($statusResponse['amount'])) {
                            $withdrawal['actual_amount'] = $statusResponse['amount'];
                        }
                        if (isset($statusResponse['fee'])) {
                            $withdrawal['actual_fee'] = $statusResponse['fee'];
                        }
                    }
                    
                    // Проверяем необходимость возврата средств
                    $needsRefund = in_array($newStatus, ['rejected', 'failed', 'cancelled', 'expired']) && !$isRefunded;
                    
                    if ($needsRefund) {
                        // Выполняем возврат средств
                        $oldBalance = $user['balance'] ?? 0;
                        $newBalance = $oldBalance + $coinsAmount;
                        $user['balance'] = $newBalance;
                        
                        // Помечаем как возвращенное
                        $withdrawal['refunded'] = true;
                        $withdrawal['refund_amount'] = $coinsAmount;
                        $withdrawal['refund_date'] = date('Y-m-d H:i:s');
                        $withdrawal['refund_reason'] = "Автоматический возврат за {$newStatus} выплату";
                        
                        $totalRefunded++;
                        $totalRefundAmount += $coinsAmount;
                        
                        logRefundSystem("Возвращено $coinsAmount монет пользователю $userId за {$newStatus} выплату $payoutId", "SUCCESS");
                        
                        // Отправляем уведомление администратору (если настроено)
                        notifyAdminAboutRefund($userId, $payoutId, $coinsAmount, $newStatus);
                    }
                    
                } else {
                    logRefundSystem("Не удалось получить статус выплаты $payoutId", "WARNING");
                }
                
            } catch (Exception $e) {
                logRefundSystem("Ошибка при проверке выплаты $payoutId: " . $e->getMessage(), "ERROR");
            }
            
            // Добавляем небольшую задержку между запросами
            usleep(200000); // 200ms
        }
    }
    
    // Сохраняем изменения
    if ($totalUpdated > 0 || $totalRefunded > 0) {
        if (saveUserData($userData)) {
            logRefundSystem("Изменения сохранены: обновлено статусов $totalUpdated, возвращено $totalRefunded выплат на сумму $totalRefundAmount монет", "SUCCESS");
        } else {
            logRefundSystem("Ошибка сохранения данных!", "ERROR");
        }
    }
    
    // Итоговая статистика
    logRefundSystem("Завершение работы: проверено $totalChecked выплат, обновлено $totalUpdated статусов, возвращено $totalRefunded выплат");
    
    // Возвращаем результат для веб-интерфейса
    if (isset($_GET['json'])) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'checked' => $totalChecked,
            'updated' => $totalUpdated,
            'refunded' => $totalRefunded,
            'refund_amount' => $totalRefundAmount,
            'message' => "Проверено $totalChecked выплат, обновлено $totalUpdated статусов, возвращено $totalRefunded выплат"
        ]);
    } else {
        echo "✅ Система автоматического возврата завершена\n";
        echo "📊 Проверено выплат: $totalChecked\n";
        echo "🔄 Обновлено статусов: $totalUpdated\n";
        echo "💰 Возвращено выплат: $totalRefunded\n";
        echo "💎 Общая сумма возврата: $totalRefundAmount монет\n";
    }
    
} catch (Exception $e) {
    $errorMessage = "Критическая ошибка в системе автовозврата: " . $e->getMessage();
    logRefundSystem($errorMessage, "CRITICAL");
    
    if (isset($_GET['json'])) {
        header('Content-Type: application/json');
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => $errorMessage
        ]);
    } else {
        echo "❌ Ошибка: $errorMessage\n";
    }
}

/**
 * Отправляет уведомление администратору о возврате средств
 */
function notifyAdminAboutRefund($userId, $payoutId, $amount, $status) {
    // Здесь можно добавить отправку email, Telegram уведомления и т.д.
    logRefundSystem("Уведомление: возвращено $amount монет пользователю $userId за $status выплату $payoutId", "NOTIFY");
}
?>
