<?php
/**
 * Тест системы отслеживания статусов выплат
 */

header('Content-Type: text/html; charset=utf-8');

echo "<h1>🔍 Тест системы отслеживания статусов выплат</h1>\n";

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/db_mock.php';

// 1. Проверка файлов
echo "<h2>📁 1. Проверка файлов системы статусов</h2>\n";

$statusFiles = [
    'checkUserWithdrawals.php',
    'forceUpdateWithdrawalStatuses.php',
    'checkActiveWithdrawals.php',
    'getWithdrawalHistory.php'
];

foreach ($statusFiles as $file) {
    if (file_exists(__DIR__ . '/' . $file)) {
        echo "✅ {$file} - найден<br>\n";
    } else {
        echo "❌ {$file} - НЕ НАЙДЕН<br>\n";
    }
}

// 2. Проверка пользователей с выплатами
echo "<h2>👥 2. Анализ пользователей с выплатами</h2>\n";

$userData = loadUserData();
$usersWithWithdrawals = 0;
$totalWithdrawals = 0;
$statusCounts = [];

foreach ($userData as $userId => $user) {
    if (isset($user['withdrawals']) && !empty($user['withdrawals'])) {
        $usersWithWithdrawals++;
        $userWithdrawals = count($user['withdrawals']);
        $totalWithdrawals += $userWithdrawals;
        
        echo "👤 Пользователь {$userId}: {$userWithdrawals} выплат<br>\n";
        
        // Анализируем статусы
        foreach ($user['withdrawals'] as $withdrawal) {
            $status = $withdrawal['status'] ?? 'unknown';
            $statusCounts[$status] = ($statusCounts[$status] ?? 0) + 1;
        }
    }
}

echo "<br><strong>Итого:</strong><br>\n";
echo "👥 Пользователей с выплатами: {$usersWithWithdrawals}<br>\n";
echo "💰 Всего выплат: {$totalWithdrawals}<br>\n";

echo "<br><strong>Статистика по статусам:</strong><br>\n";
foreach ($statusCounts as $status => $count) {
    echo "📊 {$status}: {$count}<br>\n";
}

// 3. Тест API checkUserWithdrawals
echo "<h2>🔍 3. Тест API checkUserWithdrawals</h2>\n";

// Найдем пользователя с выплатами для тестирования
$testUserId = null;
foreach ($userData as $userId => $user) {
    if (isset($user['withdrawals']) && !empty($user['withdrawals'])) {
        $testUserId = $userId;
        break;
    }
}

if ($testUserId) {
    echo "🧪 Тестируем с пользователем: {$testUserId}<br>\n";
    
    // Создаем тестовые данные
    $testInitData = "user=" . urlencode(json_encode(['id' => $testUserId])) . "&auth_date=" . time();
    
    $testData = [
        'initData' => $testInitData
    ];
    
    try {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'http://localhost' . dirname($_SERVER['REQUEST_URI']) . '/checkUserWithdrawals.php');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "HTTP код: {$httpCode}<br>\n";
        
        if ($httpCode === 200) {
            $data = json_decode($response, true);
            if ($data && isset($data['success'])) {
                echo "✅ checkUserWithdrawals работает<br>\n";
                echo "📊 Проверено: " . ($data['total_withdrawals'] ?? 0) . " выплат<br>\n";
                echo "🔄 Обновлено: " . ($data['updated_count'] ?? 0) . " статусов<br>\n";
            } else {
                echo "⚠️ Неожиданный формат ответа<br>\n";
                echo "Ответ: " . htmlspecialchars(substr($response, 0, 200)) . "<br>\n";
            }
        } else {
            echo "❌ Ошибка HTTP {$httpCode}<br>\n";
            echo "Ответ: " . htmlspecialchars(substr($response, 0, 200)) . "<br>\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Ошибка тестирования: " . $e->getMessage() . "<br>\n";
    }
} else {
    echo "⚠️ Нет пользователей с выплатами для тестирования<br>\n";
}

// 4. Тест API checkActiveWithdrawals
echo "<h2>🔒 4. Тест API checkActiveWithdrawals</h2>\n";

if ($testUserId) {
    $testData = [
        'initData' => $testInitData
    ];
    
    try {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'http://localhost' . dirname($_SERVER['REQUEST_URI']) . '/checkActiveWithdrawals.php');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "HTTP код: {$httpCode}<br>\n";
        
        if ($httpCode === 200) {
            $data = json_decode($response, true);
            if ($data && isset($data['success'])) {
                echo "✅ checkActiveWithdrawals работает<br>\n";
                echo "🔒 Форма заблокирована: " . ($data['is_blocked'] ? 'ДА' : 'НЕТ') . "<br>\n";
                echo "📊 Активных выплат: " . ($data['active_withdrawals_count'] ?? 0) . "<br>\n";
                if ($data['is_blocked']) {
                    echo "💬 Причина блокировки: " . htmlspecialchars($data['block_reason'] ?? '') . "<br>\n";
                }
            } else {
                echo "⚠️ Неожиданный формат ответа<br>\n";
            }
        } else {
            echo "❌ Ошибка HTTP {$httpCode}<br>\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Ошибка тестирования: " . $e->getMessage() . "<br>\n";
    }
}

echo "<h2>✅ Тест системы статусов завершен</h2>\n";
echo "<p><a href='../'>← Вернуться к приложению</a></p>\n";
echo "<p><a href='test_withdrawal_fixes.php'>🔧 Основные тесты выплат</a></p>\n";
?>
