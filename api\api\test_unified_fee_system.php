<?php
/**
 * Тест синхронизации единой системы расчета комиссий
 * Проверяет что калькулятор, система выплат и авторасчет показывают одинаковые результаты
 */

require_once 'config.php';
require_once 'FeeCalculator.php';
require_once 'NOWPaymentsAPI.php';

echo "🧪 ТЕСТ СИНХРОНИЗАЦИИ ЕДИНОЙ СИСТЕМЫ КОМИССИЙ\n";
echo str_repeat("=", 60) . "\n\n";

// Тестовые данные
$testCases = [
    ['coins' => 1000, 'currency' => 'eth'],
    ['coins' => 2500, 'currency' => 'btc'],
    ['coins' => 5000, 'currency' => 'ton'],
    ['coins' => 10000, 'currency' => 'usdttrc20'],
    ['coins' => 500, 'currency' => 'eth'],   // Меньше минимума
    ['coins' => 50000, 'currency' => 'btc'], // Большая сумма
];

$feeCalculator = FeeCalculator::getInstance();

foreach ($testCases as $i => $testCase) {
    $coins = $testCase['coins'];
    $currency = $testCase['currency'];
    
    echo "📊 ТЕСТ " . ($i + 1) . ": {$coins} монет → {$currency}\n";
    echo str_repeat("-", 40) . "\n";
    
    // 1. Тест единого калькулятора
    echo "1️⃣ Единый калькулятор комиссий:\n";
    $calculatorResult = $feeCalculator->calculateWithdrawalAmount($coins, $currency);
    
    if ($calculatorResult['success']) {
        echo "   ✅ Успех\n";
        echo "   💰 Финальная сумма: {$calculatorResult['crypto_amount']} {$currency}\n";
        echo "   💵 USD сумма: \${$calculatorResult['usd_amount']}\n";
        echo "   💸 Комиссия NOWPayments: {$calculatorResult['nowpayments_fee']} {$currency}\n";
        echo "   🔧 Метод: {$calculatorResult['calculation_method']}\n";
    } else {
        echo "   ❌ Ошибка: {$calculatorResult['error']}\n";
        echo "   🔧 Код ошибки: {$calculatorResult['error_code']}\n";
    }
    
    // 2. Тест API калькулятора (имитация запроса)
    echo "\n2️⃣ API калькулятора (calculateWithdrawalAmount.php):\n";
    $apiResult = testCalculatorAPI($coins, $currency);
    
    if ($apiResult && $apiResult['success']) {
        echo "   ✅ Успех\n";
        echo "   💰 Финальная сумма: {$apiResult['crypto_amount']} {$currency}\n";
        echo "   💵 USD сумма: \${$apiResult['usd_amount']}\n";
        echo "   💸 Комиссия NOWPayments: {$apiResult['nowpayments_fee']} {$currency}\n";
        echo "   🔧 Метод: {$apiResult['calculation_method']}\n";
    } else {
        echo "   ❌ Ошибка: " . ($apiResult['error'] ?? 'Неизвестная ошибка') . "\n";
    }
    
    // 3. Сравнение результатов
    echo "\n3️⃣ Сравнение результатов:\n";
    if ($calculatorResult['success'] && $apiResult && $apiResult['success']) {
        $diff = abs($calculatorResult['crypto_amount'] - $apiResult['crypto_amount']);
        $tolerance = $calculatorResult['crypto_amount'] * 0.001; // 0.1% допуск
        
        if ($diff <= $tolerance) {
            echo "   ✅ Результаты синхронизированы (разница: " . number_format($diff, 8) . ")\n";
        } else {
            echo "   ⚠️ Расхождение в результатах!\n";
            echo "   📊 Калькулятор: {$calculatorResult['crypto_amount']}\n";
            echo "   📊 API: {$apiResult['crypto_amount']}\n";
            echo "   📊 Разница: " . number_format($diff, 8) . "\n";
        }
        
        // Проверяем USD суммы
        $usdDiff = abs($calculatorResult['usd_amount'] - $apiResult['usd_amount']);
        if ($usdDiff < 0.01) {
            echo "   ✅ USD суммы синхронизированы\n";
        } else {
            echo "   ⚠️ Расхождение в USD суммах: \${$usdDiff}\n";
        }
        
        // Проверяем комиссии
        $feeDiff = abs($calculatorResult['nowpayments_fee'] - $apiResult['nowpayments_fee']);
        if ($feeDiff < 0.00001) {
            echo "   ✅ Комиссии синхронизированы\n";
        } else {
            echo "   ⚠️ Расхождение в комиссиях: " . number_format($feeDiff, 8) . "\n";
        }
        
    } else {
        echo "   ⚠️ Невозможно сравнить - один из тестов завершился с ошибкой\n";
    }
    
    echo "\n" . str_repeat("=", 60) . "\n\n";
}

// 4. Тест минимумов
echo "🔍 ТЕСТ МИНИМАЛЬНЫХ СУММ\n";
echo str_repeat("=", 60) . "\n";

$currencies = ['eth', 'btc', 'ton', 'usdttrc20'];
foreach ($currencies as $currency) {
    echo "💎 {$currency}:\n";
    
    $minData = $feeCalculator->getMinimumCoinsForCurrency($currency);
    if ($minData) {
        echo "   📊 Минимум криптовалюты: {$minData['min_crypto']} {$currency}\n";
        echo "   💵 Минимум USD: \${$minData['min_usd']}\n";
        echo "   🪙 Минимум монет: {$minData['min_coins']}\n";
        
        // Тестируем сумму чуть выше минимума
        $testCoins = $minData['min_coins'] + 100;
        $testResult = $feeCalculator->calculateWithdrawalAmount($testCoins, $currency);
        
        if ($testResult['success']) {
            echo "   ✅ Тест выше минимума ({$testCoins} монет): УСПЕХ\n";
        } else {
            echo "   ❌ Тест выше минимума ({$testCoins} монет): {$testResult['error']}\n";
        }
        
    } else {
        echo "   ❌ Не удалось получить минимум для {$currency}\n";
    }
    echo "\n";
}

// 5. Тест производительности
echo "⚡ ТЕСТ ПРОИЗВОДИТЕЛЬНОСТИ\n";
echo str_repeat("=", 60) . "\n";

$startTime = microtime(true);
$iterations = 10;

for ($i = 0; $i < $iterations; $i++) {
    $feeCalculator->calculateWithdrawalAmount(1000, 'eth');
}

$endTime = microtime(true);
$avgTime = ($endTime - $startTime) / $iterations;

echo "📊 Среднее время расчета: " . number_format($avgTime * 1000, 2) . " мс\n";
echo "🚀 Расчетов в секунду: " . number_format(1 / $avgTime, 0) . "\n";

if ($avgTime < 0.1) {
    echo "✅ Производительность отличная (< 100мс)\n";
} elseif ($avgTime < 0.5) {
    echo "✅ Производительность хорошая (< 500мс)\n";
} else {
    echo "⚠️ Производительность требует оптимизации (> 500мс)\n";
}

echo "\n🎯 ТЕСТИРОВАНИЕ ЗАВЕРШЕНО!\n";

/**
 * Тестирует API калькулятора
 */
function testCalculatorAPI($coins, $currency) {
    // Имитируем POST запрос к calculateWithdrawalAmount.php
    $postData = json_encode([
        'coins_amount' => $coins,
        'currency' => $currency
    ]);
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/json',
            'content' => $postData
        ]
    ]);
    
    $url = 'http://argun-defolt.loc/api/calculateWithdrawalAmount.php';
    $response = @file_get_contents($url, false, $context);
    
    if ($response === false) {
        return ['success' => false, 'error' => 'Не удалось подключиться к API'];
    }
    
    $result = json_decode($response, true);
    if ($result === null) {
        return ['success' => false, 'error' => 'Неверный JSON ответ'];
    }
    
    return $result;
}
?>
