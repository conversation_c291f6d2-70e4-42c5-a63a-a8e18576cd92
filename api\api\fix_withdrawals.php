<?php
/**
 * fix_withdrawals.php
 * Исправление проблем с системой выплат
 */

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/functions.php';
require_once __DIR__ . '/NOWPaymentsAPI.php';

echo "<!DOCTYPE html>\n";
echo "<html><head><meta charset='utf-8'><title>Исправление системы выплат</title>";
echo "<style>
body{font-family:Arial;margin:20px;background:#0a0a0a;color:white;} 
.success{color:#00ff00;} .error{color:#ff4444;} .warning{color:#ffaa00;} .info{color:#00ffff;} 
.container{background:linear-gradient(45deg,#1a1a2e,#16213e,#533483);padding:20px;border-radius:10px;margin:20px 0;}
.withdrawal-item{background:#2a2a2a;padding:15px;margin:10px 0;border-radius:8px;border-left:4px solid #00ffff;}
.status-pending{border-left-color:#ffaa00;} .status-finished{border-left-color:#00ff00;} .status-failed{border-left-color:#ff4444;}
button{background:#00ffff;color:#000;padding:10px 20px;border:none;border-radius:5px;cursor:pointer;margin:5px;}
button:hover{background:#00aaaa;}
</style>";
echo "</head><body>\n";

echo "<div class='container'>\n";
echo "<h1>🔧 Исправление системы выплат UniQPaid</h1>\n";
echo "</div>\n";

$action = $_GET['action'] ?? 'dashboard';

try {
    // Создаем экземпляр API
    $api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);
    
    // Загружаем данные пользователей
    $userData = loadUserData();
    if (!is_array($userData)) {
        throw new Exception("Не удалось загрузить данные пользователей");
    }

    switch ($action) {
        case 'dashboard':
            showDashboard($userData, $api);
            break;
            
        case 'fix_missing_payout_ids':
            fixMissingPayoutIds($userData, $api);
            break;
            
        case 'update_all_statuses':
            updateAllStatuses($userData, $api);
            break;
            
        case 'fix_specific_user':
            $userId = $_GET['user_id'] ?? null;
            if ($userId) {
                fixSpecificUser($userId, $userData, $api);
            } else {
                echo "<p class='error'>❌ Не указан user_id</p>\n";
            }
            break;
            
        case 'refund_failed':
            refundFailedWithdrawals($userData, $api);
            break;
            
        default:
            echo "<p class='error'>❌ Неизвестное действие</p>\n";
    }

} catch (Exception $e) {
    echo "<div class='container'>\n";
    echo "<p class='error'>❌ <strong>Критическая ошибка:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "</div>\n";
}

echo "</body></html>\n";

/**
 * Показывает дашборд с проблемами
 */
function showDashboard($userData, $api) {
    echo "<div class='container'>\n";
    echo "<h2>📊 Анализ проблем системы выплат</h2>\n";
    
    $totalWithdrawals = 0;
    $pendingWithdrawals = 0;
    $missingPayoutIds = 0;
    $problemUsers = [];
    
    // Анализируем все выплаты
    foreach ($userData as $userId => $user) {
        if (!isset($user['withdrawals']) || !is_array($user['withdrawals'])) {
            continue;
        }
        
        $userProblems = [];
        
        foreach ($user['withdrawals'] as $index => $withdrawal) {
            $totalWithdrawals++;
            
            $status = $withdrawal['status'] ?? 'unknown';
            $payoutId = $withdrawal['payout_id'] ?? $withdrawal['id'] ?? null;
            
            if ($status === 'pending') {
                $pendingWithdrawals++;
            }
            
            if (!$payoutId) {
                $missingPayoutIds++;
                $userProblems[] = "Выплата #{$index} без payout_id";
            }
        }
        
        if (!empty($userProblems)) {
            $problemUsers[$userId] = [
                'user' => $user,
                'problems' => $userProblems
            ];
        }
    }
    
    echo "<div class='withdrawal-item'>\n";
    echo "<h3>📈 Статистика</h3>\n";
    echo "<p><strong>Всего выплат:</strong> {$totalWithdrawals}</p>\n";
    echo "<p><strong>В ожидании:</strong> <span class='warning'>{$pendingWithdrawals}</span></p>\n";
    echo "<p><strong>Без payout_id:</strong> <span class='error'>{$missingPayoutIds}</span></p>\n";
    echo "<p><strong>Пользователей с проблемами:</strong> <span class='warning'>" . count($problemUsers) . "</span></p>\n";
    echo "</div>\n";
    
    // Показываем проблемных пользователей
    if (!empty($problemUsers)) {
        echo "<h3>👥 Пользователи с проблемами</h3>\n";
        foreach ($problemUsers as $userId => $data) {
            $user = $data['user'];
            $userName = ($user['first_name'] ?? 'Неизвестно') . ' ' . ($user['last_name'] ?? '');
            
            echo "<div class='withdrawal-item'>\n";
            echo "<h4>👤 {$userName} (ID: {$userId})</h4>\n";
            echo "<p><strong>Баланс:</strong> " . ($user['balance'] ?? 0) . " монет</p>\n";
            echo "<p><strong>Проблемы:</strong></p>\n";
            echo "<ul>\n";
            foreach ($data['problems'] as $problem) {
                echo "<li class='error'>{$problem}</li>\n";
            }
            echo "</ul>\n";
            echo "<button onclick=\"location.href='?action=fix_specific_user&user_id={$userId}'\">🔧 Исправить</button>\n";
            echo "</div>\n";
        }
    }
    
    echo "</div>\n";
    
    // Панель действий
    echo "<div class='container'>\n";
    echo "<h2>⚡ Быстрые действия</h2>\n";
    echo "<button onclick=\"location.href='?action=update_all_statuses'\">🔄 Обновить все статусы</button>\n";
    echo "<button onclick=\"location.href='?action=fix_missing_payout_ids'\">🆔 Исправить отсутствующие payout_id</button>\n";
    echo "<button onclick=\"location.href='?action=refund_failed'\">💰 Вернуть средства за неудачные выплаты</button>\n";
    echo "<button onclick=\"location.href='auto_update_withdrawals.php?web=1'\">🤖 Запустить автообновление</button>\n";
    echo "</div>\n";
}

/**
 * Исправляет отсутствующие payout_id
 */
function fixMissingPayoutIds($userData, $api) {
    echo "<div class='container'>\n";
    echo "<h2>🆔 Исправление отсутствующих payout_id</h2>\n";
    
    $fixed = 0;
    $errors = 0;
    
    foreach ($userData as $userId => &$user) {
        if (!isset($user['withdrawals']) || !is_array($user['withdrawals'])) {
            continue;
        }
        
        foreach ($user['withdrawals'] as $index => &$withdrawal) {
            $payoutId = $withdrawal['payout_id'] ?? null;
            $id = $withdrawal['id'] ?? null;
            
            if (!$payoutId && $id) {
                // Используем id как payout_id
                $withdrawal['payout_id'] = $id;
                $fixed++;
                echo "<p class='success'>✅ Пользователь {$userId}: добавлен payout_id = {$id}</p>\n";
            } elseif (!$payoutId && !$id) {
                // Генерируем временный ID
                $tempId = 'temp_' . $userId . '_' . $index . '_' . time();
                $withdrawal['payout_id'] = $tempId;
                $withdrawal['id'] = $tempId;
                $errors++;
                echo "<p class='warning'>⚠️ Пользователь {$userId}: создан временный ID = {$tempId}</p>\n";
            }
        }
    }
    
    if ($fixed > 0 || $errors > 0) {
        if (saveUserData($userData)) {
            echo "<p class='success'>✅ Сохранено изменений: исправлено {$fixed}, временных ID {$errors}</p>\n";
        } else {
            echo "<p class='error'>❌ Не удалось сохранить изменения</p>\n";
        }
    } else {
        echo "<p class='info'>ℹ️ Все payout_id уже на месте</p>\n";
    }
    
    echo "<button onclick=\"location.href='?action=dashboard'\">← Назад к дашборду</button>\n";
    echo "</div>\n";
}

/**
 * Обновляет все статусы выплат
 */
function updateAllStatuses($userData, $api) {
    echo "<div class='container'>\n";
    echo "<h2>🔄 Обновление всех статусов выплат</h2>\n";
    
    $checked = 0;
    $updated = 0;
    
    foreach ($userData as $userId => &$user) {
        if (!isset($user['withdrawals']) || !is_array($user['withdrawals'])) {
            continue;
        }
        
        foreach ($user['withdrawals'] as $index => &$withdrawal) {
            $payoutId = $withdrawal['payout_id'] ?? null;
            $currentStatus = $withdrawal['status'] ?? 'unknown';
            
            if (!$payoutId || in_array($currentStatus, ['finished', 'completed', 'failed', 'cancelled'])) {
                continue;
            }
            
            $checked++;
            
            try {
                $statusResponse = $api->getPayoutStatus($payoutId);
                
                if ($statusResponse && isset($statusResponse['status'])) {
                    $newStatus = $statusResponse['status'];
                    
                    if ($newStatus !== $currentStatus) {
                        $withdrawal['status'] = $newStatus;
                        $withdrawal['updated_at'] = date('Y-m-d H:i:s');
                        $updated++;
                        
                        echo "<p class='success'>✅ Пользователь {$userId}, выплата {$payoutId}: {$currentStatus} → {$newStatus}</p>\n";
                        
                        // Обрабатываем неудачные выплаты
                        if (in_array($newStatus, ['failed', 'cancelled', 'expired', 'rejected'])) {
                            $refundAmount = $withdrawal['coins_amount'] ?? 0;
                            if ($refundAmount > 0) {
                                $user['balance'] = ($user['balance'] ?? 0) + $refundAmount;
                                echo "<p class='info'>💰 Возвращено {$refundAmount} монет пользователю {$userId}</p>\n";
                            }
                        }
                    }
                }
                
                // Задержка между запросами
                usleep(200000); // 0.2 секунды
                
            } catch (Exception $e) {
                echo "<p class='error'>❌ Ошибка проверки {$payoutId}: " . htmlspecialchars($e->getMessage()) . "</p>\n";
            }
        }
    }
    
    if ($updated > 0) {
        if (saveUserData($userData)) {
            echo "<p class='success'>🎉 Обновлено {$updated} из {$checked} проверенных выплат</p>\n";
        } else {
            echo "<p class='error'>❌ Не удалось сохранить изменения</p>\n";
        }
    } else {
        echo "<p class='info'>ℹ️ Проверено {$checked} выплат, обновлений нет</p>\n";
    }
    
    echo "<button onclick=\"location.href='?action=dashboard'\">← Назад к дашборду</button>\n";
    echo "</div>\n";
}

/**
 * Исправляет проблемы конкретного пользователя
 */
function fixSpecificUser($userId, $userData, $api) {
    echo "<div class='container'>\n";
    echo "<h2>👤 Исправление выплат пользователя {$userId}</h2>\n";
    
    if (!isset($userData[$userId])) {
        echo "<p class='error'>❌ Пользователь не найден</p>\n";
        return;
    }
    
    $user = &$userData[$userId];
    $withdrawals = $user['withdrawals'] ?? [];
    
    if (empty($withdrawals)) {
        echo "<p class='info'>ℹ️ У пользователя нет выплат</p>\n";
        return;
    }
    
    echo "<p><strong>Пользователь:</strong> " . ($user['first_name'] ?? 'Неизвестно') . "</p>\n";
    echo "<p><strong>Баланс:</strong> " . ($user['balance'] ?? 0) . " монет</p>\n";
    echo "<p><strong>Выплат:</strong> " . count($withdrawals) . "</p>\n";
    
    foreach ($withdrawals as $index => &$withdrawal) {
        $payoutId = $withdrawal['payout_id'] ?? $withdrawal['id'] ?? null;
        $status = $withdrawal['status'] ?? 'unknown';
        $amount = $withdrawal['coins_amount'] ?? 0;
        
        echo "<div class='withdrawal-item status-{$status}'>\n";
        echo "<h4>💳 Выплата #{$index}</h4>\n";
        echo "<p><strong>Сумма:</strong> {$amount} монет</p>\n";
        echo "<p><strong>Статус:</strong> {$status}</p>\n";
        echo "<p><strong>Payout ID:</strong> " . ($payoutId ?: 'отсутствует') . "</p>\n";
        
        if ($payoutId && !in_array($status, ['finished', 'completed'])) {
            try {
                $statusResponse = $api->getPayoutStatus($payoutId);
                if ($statusResponse && isset($statusResponse['status'])) {
                    $newStatus = $statusResponse['status'];
                    if ($newStatus !== $status) {
                        $withdrawal['status'] = $newStatus;
                        $withdrawal['updated_at'] = date('Y-m-d H:i:s');
                        echo "<p class='success'>✅ Статус обновлен: {$status} → {$newStatus}</p>\n";
                    } else {
                        echo "<p class='info'>ℹ️ Статус актуален</p>\n";
                    }
                }
            } catch (Exception $e) {
                echo "<p class='error'>❌ Ошибка проверки: " . htmlspecialchars($e->getMessage()) . "</p>\n";
            }
        }
        
        echo "</div>\n";
    }
    
    if (saveUserData($userData)) {
        echo "<p class='success'>✅ Данные пользователя обновлены</p>\n";
    }
    
    echo "<button onclick=\"location.href='?action=dashboard'\">← Назад к дашборду</button>\n";
    echo "</div>\n";
}

/**
 * Возвращает средства за неудачные выплаты
 */
function refundFailedWithdrawals($userData, $api) {
    echo "<div class='container'>\n";
    echo "<h2>💰 Возврат средств за неудачные выплаты</h2>\n";
    
    $refunded = 0;
    $totalRefund = 0;
    
    foreach ($userData as $userId => &$user) {
        if (!isset($user['withdrawals']) || !is_array($user['withdrawals'])) {
            continue;
        }
        
        foreach ($user['withdrawals'] as $index => &$withdrawal) {
            $status = $withdrawal['status'] ?? 'unknown';
            $amount = $withdrawal['coins_amount'] ?? 0;
            $alreadyRefunded = $withdrawal['refunded'] ?? false;
            
            if (in_array($status, ['failed', 'cancelled', 'expired', 'rejected']) && !$alreadyRefunded && $amount > 0) {
                $user['balance'] = ($user['balance'] ?? 0) + $amount;
                $withdrawal['refunded'] = true;
                $withdrawal['refund_amount'] = $amount;
                $withdrawal['refund_date'] = date('Y-m-d H:i:s');
                
                $refunded++;
                $totalRefund += $amount;
                
                echo "<p class='success'>✅ Пользователь {$userId}: возвращено {$amount} монет</p>\n";
            }
        }
    }
    
    if ($refunded > 0) {
        if (saveUserData($userData)) {
            echo "<p class='success'>🎉 Возвращено {$totalRefund} монет {$refunded} пользователям</p>\n";
        } else {
            echo "<p class='error'>❌ Не удалось сохранить изменения</p>\n";
        }
    } else {
        echo "<p class='info'>ℹ️ Нет выплат для возврата средств</p>\n";
    }
    
    echo "<button onclick=\"location.href='?action=dashboard'\">← Назад к дашборду</button>\n";
    echo "</div>\n";
}
?>
