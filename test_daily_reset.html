<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест ежедневного сброса счетчиков</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 1px solid #444;
        }
        .button {
            background: #FFC107;
            color: #000;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        .button:hover {
            background: #FFD54F;
        }
        .info-display {
            background: #333;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.success { background: rgba(76, 175, 80, 0.2); border: 1px solid #4CAF50; }
        .status.warning { background: rgba(255, 152, 0, 0.2); border: 1px solid #FF9800; }
        .status.error { background: rgba(244, 67, 54, 0.2); border: 1px solid #F44336; }
        .timer {
            font-size: 18px;
            font-weight: bold;
            color: #FFC107;
        }
    </style>
</head>
<body>
    <h1>🕒 Тест системы ежедневного сброса счетчиков</h1>

    <div class="test-section">
        <h2>📊 Текущее состояние</h2>
        <div class="info-display" id="current-state">
            Загрузка информации...
        </div>
        <button class="button" onclick="updateCurrentState()">🔄 Обновить состояние</button>
    </div>

    <div class="test-section">
        <h2>⏰ Информация о времени</h2>
        <div class="info-display" id="time-info">
            Загрузка информации о времени...
        </div>
        <div class="timer" id="countdown-timer">
            Время до сброса: --:--:--
        </div>
    </div>

    <div class="test-section">
        <h2>🧪 Тесты</h2>
        <button class="button" onclick="testCurrentDay()">📅 Тест текущего дня</button>
        <button class="button" onclick="testNextDay()">📅 Тест следующего дня</button>
        <button class="button" onclick="testOldDataCleanup()">🗑️ Тест очистки старых данных</button>
        <button class="button" onclick="createTestData()">📝 Создать тестовые данные</button>
        <button class="button" onclick="clearAllData()">🧹 Очистить все данные</button>
    </div>

    <div class="test-section">
        <h2>📋 Результаты тестов</h2>
        <div id="test-results"></div>
    </div>

    <div class="test-section">
        <h2>📝 Лог событий</h2>
        <div class="info-display" id="event-log" style="height: 300px; overflow-y: auto;">
            Лог событий будет отображаться здесь...
        </div>
        <button class="button" onclick="clearLog()">🧹 Очистить лог</button>
    </div>

    <script>
        // Мини-версия локализации для тестирования
        window.appLocalization = {
            isLoaded: true,
            currentLanguage: 'ru',
            translations: {
                'ru': {
                    'tasks.ad_views_left': 'осталось {count} показов',
                    'tasks.ad_views_left_single': 'остался {count} показ',
                    'tasks.ad_views_left_few': 'осталось {count} показа'
                },
                'en': {
                    'tasks.ad_views_left': '{count} ad views left'
                }
            },
            get: function(key, params = {}) {
                const translation = this.translations[this.currentLanguage]?.[key] || key;
                return translation.replace(/\{(\w+)\}/g, (match, param) => params[param] || match);
            }
        };

        let countdownInterval;

        function updateCurrentState() {
            if (!window.adCountersManager) {
                logEvent('❌ AdCountersManager не найден');
                return;
            }

            const info = window.adCountersManager.getAllLimitsInfo();
            const stateDiv = document.getElementById('current-state');
            
            let stateText = `Текущая UTC дата: ${info._resetInfo.currentUTCDate}\n\n`;
            stateText += `Состояние счетчиков:\n`;
            
            Object.keys(info).forEach(key => {
                if (key !== '_resetInfo') {
                    const data = info[key];
                    stateText += `${key}: ${data.current}/${data.limit} (осталось: ${data.remaining})\n`;
                }
            });

            stateDiv.textContent = stateText;
            updateTimeInfo(info._resetInfo);
        }

        function updateTimeInfo(resetInfo) {
            const timeDiv = document.getElementById('time-info');
            const timerDiv = document.getElementById('countdown-timer');
            
            let timeText = `Следующий сброс: ${resetInfo.nextResetTime}\n`;
            timeText += `Время до сброса: ${resetInfo.timeUntilReset.hours}ч ${resetInfo.timeUntilReset.minutes}мин\n`;
            timeText += `Миллисекунд до сброса: ${resetInfo.timeUntilReset.milliseconds}`;
            
            timeDiv.textContent = timeText;
            
            // Обновляем таймер обратного отсчета
            startCountdown(resetInfo.timeUntilReset.milliseconds);
        }

        function startCountdown(milliseconds) {
            if (countdownInterval) {
                clearInterval(countdownInterval);
            }

            let remaining = milliseconds;
            
            countdownInterval = setInterval(() => {
                remaining -= 1000;
                
                if (remaining <= 0) {
                    clearInterval(countdownInterval);
                    document.getElementById('countdown-timer').textContent = 'Время сброса наступило!';
                    logEvent('⏰ Время сброса наступило! Обновляем состояние...');
                    setTimeout(updateCurrentState, 1000);
                    return;
                }

                const hours = Math.floor(remaining / (1000 * 60 * 60));
                const minutes = Math.floor((remaining % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((remaining % (1000 * 60)) / 1000);
                
                document.getElementById('countdown-timer').textContent = 
                    `Время до сброса: ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }, 1000);
        }

        function testCurrentDay() {
            logEvent('🧪 Тест текущего дня...');
            
            if (!window.adCountersManager) {
                addTestResult('❌ AdCountersManager не найден', 'error');
                return;
            }

            const currentDate = window.adCountersManager.getCurrentUTCDate();
            logEvent(`Текущая UTC дата: ${currentDate}`);
            
            // Проверяем формат даты
            const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
            if (dateRegex.test(currentDate)) {
                addTestResult('✅ Формат даты корректный (YYYY-MM-DD)', 'success');
            } else {
                addTestResult('❌ Неверный формат даты', 'error');
            }

            updateCurrentState();
        }

        function testNextDay() {
            logEvent('🧪 Тест следующего дня...');
            
            // Создаем данные для "вчерашнего" дня
            const yesterday = new Date();
            yesterday.setUTCDate(yesterday.getUTCDate() - 1);
            const yesterdayStr = yesterday.getUTCFullYear() + '-' + 
                String(yesterday.getUTCMonth() + 1).padStart(2, '0') + '-' + 
                String(yesterday.getUTCDate()).padStart(2, '0');
            
            // Создаем тестовые данные для вчерашнего дня
            localStorage.setItem(`ad_count_native_banner_${yesterdayStr}`, '15');
            localStorage.setItem(`ad_count_rewarded_video_${yesterdayStr}`, '8');
            localStorage.setItem(`ad_count_interstitial_${yesterdayStr}`, '12');
            
            logEvent(`Созданы тестовые данные для ${yesterdayStr}`);
            
            // Запускаем очистку
            if (window.adCountersManager) {
                window.adCountersManager.cleanupOldCounters();
                updateCurrentState();
                addTestResult('✅ Тест перехода на следующий день выполнен', 'success');
            }
        }

        function testOldDataCleanup() {
            logEvent('🧪 Тест очистки старых данных...');
            
            // Создаем старые данные
            const dates = ['2024-01-01', '2024-06-15', '2024-12-31'];
            dates.forEach(date => {
                localStorage.setItem(`ad_count_native_banner_${date}`, '10');
                localStorage.setItem(`ad_count_rewarded_video_${date}`, '5');
                localStorage.setItem(`ad_count_interstitial_${date}`, '8');
            });
            
            logEvent('Созданы старые тестовые данные');
            
            // Запускаем очистку
            if (window.adCountersManager) {
                window.adCountersManager.cleanupOldCounters();
                addTestResult('✅ Очистка старых данных выполнена', 'success');
            }
        }

        function createTestData() {
            logEvent('📝 Создание тестовых данных...');
            
            if (window.adCountersManager) {
                // Увеличиваем счетчики
                for (let i = 0; i < 5; i++) {
                    window.adCountersManager.incrementCounter('native_banner');
                }
                for (let i = 0; i < 3; i++) {
                    window.adCountersManager.incrementCounter('rewarded_video');
                }
                for (let i = 0; i < 7; i++) {
                    window.adCountersManager.incrementCounter('interstitial');
                }
                
                updateCurrentState();
                addTestResult('✅ Тестовые данные созданы', 'success');
            }
        }

        function clearAllData() {
            logEvent('🧹 Очистка всех данных...');
            
            // Удаляем все ключи счетчиков
            const keysToRemove = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith('ad_count_')) {
                    keysToRemove.push(key);
                }
            }
            
            keysToRemove.forEach(key => localStorage.removeItem(key));
            
            if (window.adCountersManager) {
                window.adCountersManager.updateAllCounters();
            }
            
            updateCurrentState();
            addTestResult(`✅ Удалено ${keysToRemove.length} записей`, 'success');
        }

        function addTestResult(message, type) {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `status ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
            
            logEvent(message);
        }

        function logEvent(message) {
            const logDiv = document.getElementById('event-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('event-log').textContent = '';
            document.getElementById('test-results').innerHTML = '';
        }

        // Инициализация
        window.addEventListener('load', () => {
            logEvent('Страница загружена, ожидание загрузки модулей...');
            
            const checkCounters = setInterval(() => {
                if (window.adCountersManager) {
                    clearInterval(checkCounters);
                    window.adCountersManager.init().then(() => {
                        logEvent('Менеджер счетчиков инициализирован');
                        updateCurrentState();
                    });
                }
            }, 100);
            
            // Обновляем состояние каждые 30 секунд
            setInterval(updateCurrentState, 30000);
        });
    </script>

    <!-- Подключаем модуль счетчиков -->
    <script src="js/ad-counters.js"></script>
</body>
</html>
