<svg width="600" height="400" viewBox="0 0 600 400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 🌞 СУПЕР ЯРКИЙ ЖЁЛТЫЙ ФОН -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFF59D;stop-opacity:1" />
      <stop offset="25%" style="stop-color:#FFEB3B;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="75%" style="stop-color:#FFEB3B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFF59D;stop-opacity:1" />
    </linearGradient>

    <!-- Крутой градиент для текста -->
    <linearGradient id="premiumGoldGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#F57F17;stop-opacity:1" />
      <stop offset="25%" style="stop-color:#FF8F00;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#E65100;stop-opacity:1" />
      <stop offset="75%" style="stop-color:#FF8F00;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F57F17;stop-opacity:1" />
    </linearGradient>

    <!-- Жёлтый градиент для криптовалют -->
    <radialGradient id="yellowCryptoGradient" cx="30%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#FFFF8D;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#FFEB3B;stop-opacity:1" />
      <stop offset="60%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="80%" style="stop-color:#FFC107;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF8F00;stop-opacity:1" />
    </radialGradient>

    <!-- Розовый градиент для дизайнерских элементов -->
    <linearGradient id="pinkGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF80AB;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#E91E63;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#AD1457;stop-opacity:1" />
    </linearGradient>

    <!-- Салатовый градиент для дизайнерских элементов -->
    <linearGradient id="limeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#CCFF90;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#8BC34A;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#689F38;stop-opacity:1" />
    </linearGradient>

    <!-- Яркое жёлтое свечение -->
    <radialGradient id="yellowAura" cx="50%" cy="50%" r="70%">
      <stop offset="0%" style="stop-color:#FFEB3B;stop-opacity:0.8" />
      <stop offset="40%" style="stop-color:#FFD700;stop-opacity:0.6" />
      <stop offset="70%" style="stop-color:#FFC107;stop-opacity:0.4" />
      <stop offset="100%" style="stop-color:#FF8F00;stop-opacity:0.2" />
    </radialGradient>

    <!-- Дополнительные крутые градиенты -->
    <linearGradient id="brightYellowGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFF8D;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFEB3B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFD700;stop-opacity:1" />
    </linearGradient>

    <!-- 🎨 ЧЕТКИЕ ФИЛЬТРЫ БЕЗ РАЗМЫТИЙ -->
    <filter id="sharpGlow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="0" stdDeviation="0" flood-color="#FFD700" flood-opacity="0.8"/>
    </filter>

    <filter id="premiumShadow" x="-30%" y="-30%" width="160%" height="160%">
      <feDropShadow dx="0" dy="2" stdDeviation="0" flood-color="#FFD700" flood-opacity="0.6"/>
      <feDropShadow dx="2" dy="2" stdDeviation="0" flood-color="#FF8F00" flood-opacity="0.3"/>
    </filter>

    <filter id="textShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="1" dy="1" stdDeviation="0" flood-color="#FF8F00" flood-opacity="0.8"/>
    </filter>
  </defs>

  <!-- 🌞 СУПЕР ЯРКИЙ ЖЁЛТЫЙ ФОН -->
  <rect width="600" height="400" fill="url(#bgGradient)"/>

  <!-- Центральное яркое жёлтое свечение -->
  <ellipse cx="300" cy="200" rx="280" ry="180" fill="url(#yellowAura)"/>

  <!-- 🌈 КРУТЫЕ ДИЗАЙНЕРСКИЕ ЭЛЕМЕНТЫ -->
  <!-- Верхние декоративные линии - розовые и салатовые -->
  <rect x="40" y="40" width="140" height="6" fill="url(#pinkGradient)" opacity="0.9"/>
  <rect x="420" y="40" width="140" height="6" fill="url(#limeGradient)" opacity="0.9"/>

  <!-- Нижние декоративные линии -->
  <rect x="40" y="354" width="140" height="6" fill="url(#limeGradient)" opacity="0.9"/>
  <rect x="420" y="354" width="140" height="6" fill="url(#pinkGradient)" opacity="0.9"/>

  <!-- Стильные угловые акценты - розовые и салатовые -->
  <polygon points="0,0 70,0 0,70" fill="url(#pinkGradient)" opacity="0.8"/>
  <polygon points="600,0 530,0 600,70" fill="url(#limeGradient)" opacity="0.8"/>
  <polygon points="0,400 70,400 0,330" fill="url(#limeGradient)" opacity="0.8"/>
  <polygon points="600,400 530,400 600,330" fill="url(#pinkGradient)" opacity="0.8"/>

  <!-- Дополнительные крутые дизайнерские элементы -->
  <circle cx="100" cy="100" r="12" fill="url(#pinkGradient)" opacity="0.9"/>
  <circle cx="500" cy="100" r="12" fill="url(#limeGradient)" opacity="0.9"/>
  <circle cx="100" cy="300" r="12" fill="url(#limeGradient)" opacity="0.9"/>
  <circle cx="500" cy="300" r="12" fill="url(#pinkGradient)" opacity="0.9"/>

  <!-- Геометрические ромбы - розовые и салатовые -->
  <polygon points="150,80 165,95 150,110 135,95" fill="url(#pinkGradient)" opacity="0.8"/>
  <polygon points="450,80 465,95 450,110 435,95" fill="url(#limeGradient)" opacity="0.8"/>
  <polygon points="150,320 165,335 150,350 135,335" fill="url(#limeGradient)" opacity="0.8"/>
  <polygon points="450,320 465,335 450,350 435,335" fill="url(#pinkGradient)" opacity="0.8"/>

  <!-- Дополнительные крутые звёздочки -->
  <polygon points="200,60 210,80 220,60 210,40" fill="url(#pinkGradient)" opacity="0.7"/>
  <polygon points="400,60 410,80 420,60 410,40" fill="url(#limeGradient)" opacity="0.7"/>
  <polygon points="200,340 210,360 220,340 210,320" fill="url(#limeGradient)" opacity="0.7"/>
  <polygon points="400,340 410,360 420,340 410,320" fill="url(#pinkGradient)" opacity="0.7"/>

  <!-- 🌟 ГЛАВНЫЙ ЗАГОЛОВОК UniQPaid -->
  <text x="300" y="70" text-anchor="middle" font-family="Arial, sans-serif" font-size="52" font-weight="bold" fill="url(#premiumGoldGradient)" filter="url(#textShadow)">UniQPaid</text>

  <!-- Подзаголовок -->
  <text x="300" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="22" font-weight="600" fill="#E65100" opacity="0.95">💰 Crypto Rewards • Instant Payouts 🚀</text>

  <!-- 💎 ЦЕНТРАЛЬНАЯ КОМПОЗИЦИЯ С МОНЕТАМИ -->
  <!-- Главная центральная монета -->
  <circle cx="300" cy="200" r="70" fill="url(#luxuryCoinGradient)" stroke="#FFD700" stroke-width="4" filter="url(#premiumShadow)"/>
  <circle cx="300" cy="200" r="55" fill="none" stroke="#1a1a1a" stroke-width="3" opacity="0.6"/>
  <circle cx="300" cy="200" r="40" fill="none" stroke="#FFD700" stroke-width="1" opacity="0.8"/>
  <text x="300" y="220" text-anchor="middle" font-family="Arial, sans-serif" font-size="50" font-weight="bold" fill="#1a1a1a">$</text>

  <!-- 🪙 ЖЁЛТЫЕ КРИПТОВАЛЮТЫ С ИКОНКАМИ -->
  <!-- TON (Telegram) - топ приоритет -->
  <circle cx="170" cy="150" r="38" fill="url(#yellowCryptoGradient)" stroke="#F57F17" stroke-width="5" opacity="0.98"/>
  <circle cx="170" cy="150" r="28" fill="none" stroke="#E65100" stroke-width="3" opacity="0.8"/>
  <!-- TON иконка -->
  <circle cx="170" cy="150" r="20" fill="none" stroke="#E65100" stroke-width="2"/>
  <path d="M156 140l14 14M184 140l-14 14" stroke="#E65100" stroke-width="3" fill="none"/>
  <circle cx="170" cy="150" r="8" fill="#E65100" opacity="0.4"/>
  <text x="170" y="185" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#E65100">TON</text>

  <!-- USDT (самая популярная стейблкоин) -->
  <circle cx="430" cy="150" r="38" fill="url(#yellowCryptoGradient)" stroke="#F57F17" stroke-width="5" opacity="0.98"/>
  <circle cx="430" cy="150" r="28" fill="none" stroke="#E65100" stroke-width="3" opacity="0.8"/>
  <!-- USDT иконка -->
  <circle cx="430" cy="150" r="20" fill="none" stroke="#E65100" stroke-width="2"/>
  <rect x="420" y="140" width="20" height="6" fill="#E65100"/>
  <rect x="422" y="146" width="16" height="12" fill="none" stroke="#E65100" stroke-width="3"/>
  <text x="430" y="185" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#E65100">USDT</text>

  <!-- Bitcoin (классика) -->
  <circle cx="170" cy="250" r="38" fill="url(#yellowCryptoGradient)" stroke="#F57F17" stroke-width="5" opacity="0.98"/>
  <circle cx="170" cy="250" r="28" fill="none" stroke="#E65100" stroke-width="3" opacity="0.8"/>
  <!-- Bitcoin иконка -->
  <circle cx="170" cy="250" r="20" fill="none" stroke="#E65100" stroke-width="2"/>
  <path d="M160 240h10M160 250h12M160 260h10M160 240h6a3 3 0 0 1 0 6M160 250h8a3 3 0 0 1 0 6" fill="none" stroke="#E65100" stroke-width="3"/>
  <text x="170" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#E65100">BTC</text>

  <!-- Ethereum -->
  <circle cx="430" cy="250" r="38" fill="url(#yellowCryptoGradient)" stroke="#F57F17" stroke-width="5" opacity="0.98"/>
  <circle cx="430" cy="250" r="28" fill="none" stroke="#E65100" stroke-width="3" opacity="0.8"/>
  <!-- Ethereum иконка -->
  <polygon points="430,232 420,250 430,258 440,250" fill="#E65100" opacity="0.9"/>
  <polygon points="430,258 420,250 430,268 440,250" fill="#E65100" opacity="0.7"/>
  <text x="430" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#E65100">ETH</text>

  <!-- 🔗 КРУТЫЕ СОЕДИНИТЕЛЬНЫЕ ЛИНИИ -->
  <line x1="208" y1="170" x2="262" y2="185" stroke="url(#pinkGradient)" stroke-width="4" opacity="0.9"/>
  <line x1="392" y1="170" x2="338" y2="185" stroke="url(#limeGradient)" stroke-width="4" opacity="0.9"/>
  <line x1="208" y1="230" x2="262" y2="215" stroke="url(#limeGradient)" stroke-width="4" opacity="0.9"/>
  <line x1="392" y1="230" x2="338" y2="215" stroke="url(#pinkGradient)" stroke-width="4" opacity="0.9"/>

  <!-- Дополнительные крутые линии -->
  <line x1="170" y1="110" x2="170" y2="120" stroke="url(#pinkGradient)" stroke-width="3" opacity="0.8"/>
  <line x1="430" y1="110" x2="430" y2="120" stroke="url(#limeGradient)" stroke-width="3" opacity="0.8"/>
  <line x1="170" y1="280" x2="170" y2="290" stroke="url(#limeGradient)" stroke-width="3" opacity="0.8"/>
  <line x1="430" y1="280" x2="430" y2="290" stroke="url(#pinkGradient)" stroke-width="3" opacity="0.8"/>

  <!-- 🎯 СУПЕР КРУТЫЕ ИНФОРМАЦИОННЫЕ БЛОКИ -->
  <!-- Левый блок - розовый -->
  <rect x="25" y="315" width="160" height="70" rx="20" fill="url(#pinkGradient)" opacity="0.95" filter="url(#premiumShadow)"/>
  <rect x="28" y="318" width="154" height="64" rx="17" fill="none" stroke="#FFEB3B" stroke-width="3" opacity="0.9"/>
  <circle cx="55" cy="345" r="10" fill="#FFEB3B" opacity="0.8"/>
  <text x="105" y="345" text-anchor="middle" font-family="Arial, sans-serif" font-size="19" font-weight="bold" fill="#FFEB3B">Watch Ads</text>
  <text x="105" y="365" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#FFEB3B">Earn Coins</text>

  <!-- Правый блок - салатовый -->
  <rect x="415" y="315" width="160" height="70" rx="20" fill="url(#limeGradient)" opacity="0.95" filter="url(#premiumShadow)"/>
  <rect x="418" y="318" width="154" height="64" rx="17" fill="none" stroke="#FFEB3B" stroke-width="3" opacity="0.9"/>
  <circle cx="545" cy="345" r="10" fill="#FFEB3B" opacity="0.8"/>
  <text x="495" y="345" text-anchor="middle" font-family="Arial, sans-serif" font-size="19" font-weight="bold" fill="#FFEB3B">Instant</text>
  <text x="495" y="365" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#FFEB3B">Payouts</text>

  <!-- Центральный блок - жёлтый -->
  <rect x="220" y="315" width="160" height="70" rx="20" fill="url(#brightYellowGradient)" opacity="0.95" filter="url(#premiumShadow)"/>
  <rect x="223" y="318" width="154" height="64" rx="17" fill="none" stroke="#E65100" stroke-width="3" opacity="0.9"/>
  <circle cx="250" cy="345" r="10" fill="#E65100" opacity="0.8"/>
  <text x="300" y="345" text-anchor="middle" font-family="Arial, sans-serif" font-size="19" font-weight="bold" fill="#E65100">Referrals</text>
  <text x="300" y="365" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#E65100">10% Bonus</text>

  <!-- 💫 СУПЕР КРУТЫЕ ДОПОЛНИТЕЛЬНЫЕ ЭЛЕМЕНТЫ -->
  <!-- Боковые декоративные круги - розовые и салатовые -->
  <circle cx="65" cy="200" r="12" fill="url(#pinkGradient)" opacity="0.9"/>
  <circle cx="535" cy="200" r="12" fill="url(#limeGradient)" opacity="0.9"/>

  <!-- Верхние и нижние акценты -->
  <circle cx="300" cy="125" r="8" fill="url(#pinkGradient)" opacity="0.9"/>
  <circle cx="300" cy="295" r="8" fill="url(#limeGradient)" opacity="0.9"/>

  <!-- Стильные геометрические линии -->
  <rect x="45" y="115" width="50" height="6" fill="url(#pinkGradient)" opacity="0.9"/>
  <rect x="505" y="115" width="50" height="6" fill="url(#limeGradient)" opacity="0.9"/>
  <rect x="45" y="279" width="50" height="6" fill="url(#limeGradient)" opacity="0.9"/>
  <rect x="505" y="279" width="50" height="6" fill="url(#pinkGradient)" opacity="0.9"/>

  <!-- Дополнительные крутые звездочки -->
  <polygon points="200,115 208,135 216,115 208,95" fill="url(#pinkGradient)" opacity="0.8"/>
  <polygon points="400,115 408,135 416,115 408,95" fill="url(#limeGradient)" opacity="0.8"/>
  <polygon points="200,285 208,305 216,285 208,265" fill="url(#limeGradient)" opacity="0.8"/>
  <polygon points="400,285 408,305 416,285 408,265" fill="url(#pinkGradient)" opacity="0.8"/>

  <!-- Центральные декоративные элементы -->
  <circle cx="240" cy="200" r="6" fill="url(#pinkGradient)" opacity="0.8"/>
  <circle cx="360" cy="200" r="6" fill="url(#limeGradient)" opacity="0.8"/>

  <!-- Дополнительные крутые треугольники -->
  <polygon points="120,180 130,200 120,220" fill="url(#pinkGradient)" opacity="0.7"/>
  <polygon points="480,180 470,200 480,220" fill="url(#limeGradient)" opacity="0.7"/>

  <!-- Центральная главная монета -->
  <circle cx="300" cy="200" r="75" fill="url(#yellowCryptoGradient)" stroke="url(#pinkGradient)" stroke-width="6" filter="url(#premiumShadow)"/>
  <circle cx="300" cy="200" r="60" fill="none" stroke="url(#limeGradient)" stroke-width="4" opacity="0.8"/>
  <circle cx="300" cy="200" r="45" fill="none" stroke="#E65100" stroke-width="2" opacity="0.9"/>
  <text x="300" y="220" text-anchor="middle" font-family="Arial, sans-serif" font-size="55" font-weight="bold" fill="#E65100">$</text>

  <!-- 🚀 ПРИЗЫВ К ДЕЙСТВИЮ -->
  <text x="300" y="395" text-anchor="middle" font-family="Arial, sans-serif" font-size="22" font-weight="700" fill="url(#premiumGoldGradient)" filter="url(#textShadow)">🚀 Start Earning Now! 💰</text>
</svg>
