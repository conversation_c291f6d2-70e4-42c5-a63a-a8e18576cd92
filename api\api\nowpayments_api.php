<?php
/**
 * api/nowpayments_api.php
 * Класс для работы с NOWPayments API
 */

class NOWPaymentsAPI {
    private $apiKey;
    private $apiUrl;
    private $ipnSecret;
    
    /**
     * Конструктор класса
     * 
     * @param string $apiKey API ключ NOWPayments
     * @param string $ipnSecret Секретный ключ для IPN (опционально)
     * @param string $apiUrl Базовый URL API (опционально)
     */
    public function __construct($apiKey, $ipnSecret = null, $apiUrl = 'https://api.nowpayments.io/v1') {
        $this->apiKey = $apiKey;
        $this->apiUrl = $apiUrl;
        $this->ipnSecret = $ipnSecret;
    }
    
    /**
     * Выполняет HTTP запрос к API
     * 
     * @param string $endpoint Конечная точка API
     * @param string $method HTTP метод (GET, POST, etc.)
     * @param array $data Данные для отправки (для POST, PUT)
     * @return array Ответ API в виде ассоциативного массива
     * @throws Exception В случае ошибки
     */
    private function makeRequest($endpoint, $method = 'GET', $data = null) {
        $url = $this->apiUrl . $endpoint;
        
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'x-api-key: ' . $this->apiKey,
            'Content-Type: application/json'
        ]);
        
        if ($method === 'POST' || $method === 'PUT') {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
            if ($data !== null) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        if (curl_errno($ch)) {
            throw new Exception('Curl error: ' . curl_error($ch));
        }
        
        curl_close($ch);
        
        $responseData = json_decode($response, true);
        
        if ($httpCode >= 400) {
            $errorMessage = isset($responseData['message']) ? $responseData['message'] : 'Unknown API error';
            throw new Exception('API Error (' . $httpCode . '): ' . $errorMessage);
        }
        
        return $responseData;
    }
    
    /**
     * Получает статус API
     * 
     * @return array Статус API
     */
    public function getStatus() {
        return $this->makeRequest('/status');
    }
    
    /**
     * Получает доступные криптовалюты
     * 
     * @return array Список доступных криптовалют
     */
    public function getCurrencies() {
        return $this->makeRequest('/currencies');
    }
    
    /**
     * Получает минимальную сумму для платежа
     * 
     * @param string $currency_from Исходная валюта
     * @param string $currency_to Целевая валюта
     * @return array Минимальная сумма
     */
    public function getMinAmount($currency_from, $currency_to) {
        return $this->makeRequest('/min-amount/' . $currency_from . '_' . $currency_to);
    }
    
    /**
     * Создает массовую выплату
     * 
     * @param array $withdrawals Массив выплат
     * @return array Результат создания выплаты
     */
    public function createMassWithdrawal($withdrawals) {
        return $this->makeRequest('/mass-withdrawal', 'POST', [
            'withdrawals' => $withdrawals
        ]);
    }
    
    /**
     * Получает информацию о массовой выплате
     * 
     * @param string $id ID массовой выплаты
     * @return array Информация о выплате
     */
    public function getMassWithdrawal($id) {
        return $this->makeRequest('/mass-withdrawal/' . $id);
    }
    
    /**
     * Получает список всех массовых выплат
     * 
     * @param int $limit Лимит записей (опционально)
     * @param int $page Номер страницы (опционально)
     * @return array Список выплат
     */
    public function getMassWithdrawals($limit = 10, $page = 0) {
        return $this->makeRequest('/mass-withdrawals?limit=' . $limit . '&page=' . $page);
    }
    
    /**
     * Конвертирует сумму из одной валюты в другую
     * 
     * @param float $amount Сумма для конвертации
     * @param string $currency_from Исходная валюта
     * @param string $currency_to Целевая валюта
     * @return array Результат конвертации
     */
    public function getEstimateAmount($amount, $currency_from, $currency_to) {
        return $this->makeRequest('/estimate?' . http_build_query([
            'amount' => $amount,
            'currency_from' => $currency_from,
            'currency_to' => $currency_to
        ]));
    }
    
    /**
     * Проверяет подпись IPN уведомления
     * 
     * @param string $ipnData Данные IPN уведомления
     * @param string $signature Подпись из заголовка X-NOWPayments-Sig
     * @return bool Результат проверки
     */
    public function verifyIPNSignature($ipnData, $signature) {
        if (!$this->ipnSecret) {
            throw new Exception('IPN secret key is not set');
        }
        
        $calculatedSignature = hash_hmac('sha512', $ipnData, $this->ipnSecret);
        return hash_equals($calculatedSignature, $signature);
    }
}
?>
