<?php
/**
 * remove_webhook.php
 * Скрипт для удаления webhook
 */

require_once __DIR__ . '/config.php';

echo "=== Удаление Webhook ===\n\n";

// Проверяем текущий статус
echo "1. Текущий статус webhook:\n";
$webhookInfo = telegramRequest('getWebhookInfo');
if ($webhookInfo && !empty($webhookInfo['url'])) {
    echo "Текущий URL: " . $webhookInfo['url'] . "\n";
    echo "Ожидающие обновления: " . ($webhookInfo['pending_update_count'] ?: 0) . "\n\n";
} else {
    echo "Webhook не установлен\n\n";
    exit(0);
}

// Удаляем webhook
echo "2. Удаление webhook:\n";
$deleteResult = telegramRequest('deleteWebhook', ['drop_pending_updates' => true]);
if ($deleteResult) {
    echo "✅ Webhook успешно удален\n";
} else {
    echo "❌ Ошибка удаления webhook\n";
    exit(1);
}

// Проверяем результат
echo "\n3. Проверка результата:\n";
$webhookInfo = telegramRequest('getWebhookInfo');
if ($webhookInfo && empty($webhookInfo['url'])) {
    echo "✅ Webhook удален успешно\n";
    echo "Бот переведен в режим long polling\n";
} else {
    echo "❌ Webhook все еще активен\n";
}

echo "\n=== Удаление завершено ===\n";

// Логируем удаление
botLog("INFO: Webhook удален");
?>
