# Настройка комиссий NOWPayments для решения проблемы минимальных сумм

## Проблема
При создании выплат через NOWPayments API возникает ошибка:
```
Withdrawal amount less than a minimum. you can fix it by set withdrawal fee paid by receiver in https://account.nowpayments.io/store-settings#details
```

## Решение

### 1. Войдите в панель управления NOWPayments
- Перейдите по ссылке: https://account.nowpayments.io/
- Войдите в свой аккаунт

### 2. Откройте настройки магазина
- Перейдите в раздел "Settings" → "Payments"
- Или напрямую по ссылке: https://account.nowpayments.io/store-settings#details

### 3. Найдите настройку "Payment details"
- В разделе "Payment details" найдите опцию "Withdrawal fee paid by receiver"
- Эта настройка определяет, кто платит комиссию за выплату

### 4. Включите опцию "Withdrawal fee paid by receiver"
- Установите галочку напротив "Withdrawal fee paid by receiver"
- Это означает, что комиссия будет списываться с суммы получателя, а не добавляться к сумме отправителя

### 5. Сохраните настройки
- Нажмите кнопку "Save" или "Update"

## Как это работает

### До изменения настройки:
- Пользователь запрашивает выплату 10 USDT
- Система пытается отправить 10 USDT + комиссия (например, 2 USDT)
- Итого нужно 12 USDT на балансе
- Если на балансе только 10 USDT → ошибка "amount less than minimum"

### После изменения настройки:
- Пользователь запрашивает выплату 10 USDT
- Система отправляет 10 USDT, комиссия (2 USDT) списывается из этой суммы
- Пользователь получает 8 USDT (10 - 2 = 8)
- Нужно только 10 USDT на балансе → выплата проходит успешно

## Обновления в коде

Наш код теперь автоматически:

1. **Проверяет минимальные суммы** для каждой валюты
2. **Получает оценку комиссий** перед созданием выплаты
3. **Создает выплаты с флагом `fee_paid_by_user: true`**
4. **Показывает детальную информацию** о комиссиях пользователю
5. **Обрабатывает ошибки** с понятными сообщениями

## Тестирование

Для тестирования новой функциональности:

1. Откройте файл: `api/test_fee_handling.php`
2. Запустите его в браузере
3. Проверьте:
   - Минимальные суммы для разных валют
   - Оценки комиссий
   - Создание тестовых выплат

## Рекомендуемые комиссии

Для разных валют рекомендуются следующие настройки:

- **Bitcoin (BTC)**: 0.0001 - 0.0005 BTC
- **Ethereum (ETH)**: 0.001 - 0.01 ETH  
- **USDT (TRC20)**: 1 - 5 USDT
- **Litecoin (LTC)**: 0.001 - 0.01 LTC
- **TRON (TRX)**: 10 - 50 TRX

## Важные моменты

1. **Информирование пользователей**: Обязательно сообщайте пользователям, что комиссия будет списана с их выплаты
2. **Прозрачность**: Показывайте размер комиссии до создания выплаты
3. **Минимальные суммы**: Учитывайте, что после списания комиссии пользователь должен получить разумную сумму
4. **Мониторинг**: Регулярно проверяйте размеры комиссий, так как они могут изменяться

## Поддержка

Если проблемы продолжаются:

1. Проверьте, что настройка сохранена в панели NOWPayments
2. Подождите 5-10 минут для применения изменений
3. Обратитесь в поддержку NOWPayments: <EMAIL>
4. Используйте тестовый файл для диагностики проблем
