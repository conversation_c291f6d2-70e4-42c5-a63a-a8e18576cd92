<?php
/**
 * Простая версия тестового уведомления для отладки
 */

header('Content-Type: application/json');
ini_set('display_errors', 1);
error_reporting(E_ALL);

try {
    echo json_encode([
        'success' => true,
        'message' => 'Простой тест работает!',
        'debug' => [
            'time' => date('Y-m-d H:i:s'),
            'method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown',
            'session_started' => session_status() === PHP_SESSION_ACTIVE
        ]
    ]);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
