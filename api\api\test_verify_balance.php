<?php
// Простой тест функции verifyBalance

require_once 'config.php';
require_once 'db_mock.php';
require_once 'security.php';

echo "🔧 ТЕСТ ФУНКЦИИ verifyBalance()\n";
echo "===============================\n\n";

// Загружаем данные пользователей
$userData = loadUserData();

// Тестируем пользователя 405669007
$testUserId = 405669007;
$testAmount = 972;

if (!isset($userData[$testUserId])) {
    echo "❌ Пользователь {$testUserId} не найден\n";
    exit;
}

$user = $userData[$testUserId];
echo "👤 Пользователь: {$testUserId}\n";
echo "💰 Баланс: {$user['balance']} монет\n";
echo "🎯 Тестируемая сумма: {$testAmount} монет\n\n";

echo "--- ПОШАГОВАЯ ПРОВЕРКА ---\n";

// 1. Проверяем существование пользователя и баланса
if (!isset($userData[$testUserId]) || !isset($userData[$testUserId]['balance'])) {
    echo "❌ Шаг 1: Пользователь не найден или нет баланса\n";
    exit;
} else {
    echo "✅ Шаг 1: Пользователь найден, баланс существует\n";
}

$balance = $userData[$testUserId]['balance'];
echo "   💰 Баланс: {$balance}\n";

// 2. Проверяем достаточность средств
if ($balance < $testAmount) {
    echo "❌ Шаг 2: Недостаточно средств ({$balance} < {$testAmount})\n";
    exit;
} else {
    echo "✅ Шаг 2: Достаточно средств ({$balance} >= {$testAmount})\n";
}

echo "\n--- ВЫЗОВ ФУНКЦИИ verifyBalance() ---\n";

// Включаем отображение всех ошибок
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Вызываем функцию
$result = verifyBalance($testUserId, $testAmount, $userData);

echo "🎯 Результат: " . ($result ? "✅ TRUE (разрешено)" : "❌ FALSE (заблокировано)") . "\n";

echo "\n✨ Тест завершен\n";
?>
