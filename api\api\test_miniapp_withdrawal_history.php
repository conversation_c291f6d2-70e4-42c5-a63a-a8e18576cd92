<?php
/**
 * Тест истории выплат как в мини-приложении
 */

header('Content-Type: text/plain; charset=utf-8');

echo "=== ТЕСТ ИСТОРИИ ВЫПЛАТ МИНИ-ПРИЛОЖЕНИЯ ===\n\n";

// Тестируем разные сценарии initData

$testCases = [
    [
        'name' => 'Пустой initData',
        'data' => ['initData' => '']
    ],
    [
        'name' => 'Отсутствует initData',
        'data' => []
    ],
    [
        'name' => 'Null initData',
        'data' => ['initData' => null]
    ],
    [
        'name' => 'Правильный initData (тестовый)',
        'data' => [
            'initData' => "user=" . urlencode(json_encode([
                'id' => 5880288830,
                'first_name' => 'Альтер',
                'last_name' => 'Эго',
                'username' => 'alter_mega_ego',
                'language_code' => 'ru'
            ])) . "&auth_date=" . time() . "&hash=test"
        ]
    ],
    [
        'name' => 'Минимальный initData',
        'data' => [
            'initData' => "user=" . urlencode(json_encode([
                'id' => 5880288830,
                'first_name' => 'Test'
            ])) . "&hash=test"
        ]
    ]
];

foreach ($testCases as $i => $testCase) {
    echo ($i + 1) . ". ТЕСТ: " . $testCase['name'] . "\n";
    echo str_repeat("-", 50) . "\n";
    
    $postData = json_encode($testCase['data']);
    echo "Отправляемые данные: " . $postData . "\n";
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/json',
            'content' => $postData
        ]
    ]);
    
    try {
        $response = file_get_contents('http://argun-defolt.loc/api/getWithdrawalHistory.php', false, $context);
        
        echo "Ответ API: ";
        if ($response === false) {
            echo "❌ Ошибка запроса\n";
        } else {
            echo $response . "\n";
            
            $decoded = json_decode($response, true);
            if ($decoded) {
                if (isset($decoded['withdrawals'])) {
                    echo "✅ Количество выплат: " . count($decoded['withdrawals']) . "\n";
                } elseif (isset($decoded['error'])) {
                    echo "❌ Ошибка: " . $decoded['error'] . "\n";
                }
            }
        }
        
    } catch (Exception $e) {
        echo "❌ Исключение: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

// Проверяем логи
echo "=== ПРОВЕРКА ЛОГОВ ===\n";
$logFile = __DIR__ . '/error.log';
if (file_exists($logFile)) {
    $logs = file_get_contents($logFile);
    $lines = explode("\n", $logs);
    $recentLogs = array_slice($lines, -20); // Последние 20 строк
    
    echo "Последние записи в логе:\n";
    foreach ($recentLogs as $line) {
        if (strpos($line, 'getWithdrawalHistory') !== false) {
            echo $line . "\n";
        }
    }
} else {
    echo "Лог файл не найден\n";
}

echo "\n=== ТЕСТ ЗАВЕРШЕН ===\n";
?>
