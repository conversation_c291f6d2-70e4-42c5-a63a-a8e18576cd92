/* ======================================== */
/* GEOMETRIC DESIGN SYSTEM - INSPIRED BY MODERN CORPORATE STYLE */
/* ======================================== */

/* --- НОВАЯ ЦВЕТОВАЯ ПАЛИТРА --- */
:root {
  /* Основные цвета из дизайна */
  --primary-dark: #2a2a2a;        /* Темно-серый основной */
  --primary-black: #1a1a1a;       /* Черный для контраста */
  --accent-orange: #ff6b35;       /* Яркий оранжевый */
  --accent-orange-light: #ff8c5a; /* Светлый оранжевый */
  --accent-orange-dark: #e55a2b;  /* Темный оранжевый */
  
  /* Дополнительные цвета */
  --text-primary: #ffffff;        /* Белый текст */
  --text-secondary: #b0b0b0;      /* Серый текст */
  --text-muted: #808080;          /* Приглушенный текст */
  
  /* Фоновые цвета */
  --bg-primary: #2a2a2a;          /* Основной фон */
  --bg-secondary: #333333;        /* Вторичный фон */
  --bg-card: #3a3a3a;             /* Фон карточек */
  --bg-overlay: rgba(42, 42, 42, 0.95); /* Оверлей */
  
  /* Границы и тени */
  --border-color: #4a4a4a;        /* Цвет границ */
  --shadow-primary: 0 4px 20px rgba(255, 140, 53, 0.3);
  --shadow-secondary: 0 2px 10px rgba(0, 0, 0, 0.5);

  /* Градиенты */
  --gradient-orange: linear-gradient(135deg, #ff8c35 0%, #ffb068 100%);
  --gradient-dark: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
  --gradient-geometric: linear-gradient(45deg, #ff8c35 0%, #e57a02 50%, #ffb068 100%);
}

/* --- ПСЕВДОЭЛЕМЕНТ С ФОНОВЫМ ИЗОБРАЖЕНИЕМ --- */
body {
  /* 🎨 ТЕМНЫЙ ФОН: Основной фон для псевдоэлемента с изображением */
  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
  position: relative;
  overflow-x: hidden;
}

/* 🖼️ ПОЛУПРОЗРАЧНОЕ ФОНОВОЕ ИЗОБРАЖЕНИЕ В ПСЕВДОЭЛЕМЕНТЕ */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('../images/gpt-fon.png') center center / cover no-repeat;
  opacity: 0.5; /* 🎯 ПОЛУПРОЗРАЧНОСТЬ 50% */
  z-index: -1;
  pointer-events: none;
}

/* Скрываем остальные псевдоэлементы */
body::after {
  display: none !important;
}

@keyframes geometricFloat {
  0%, 100% { transform: rotate(15deg) translateY(0px); }
  50% { transform: rotate(15deg) translateY(-20px); }
}

/* --- ГЛИТЧ ЭФФЕКТЫ В РАЗНЫХ МЕСТАХ --- */
.glitch-line {
  position: absolute;
  width: 100%;
  height: 2px;
  background: var(--gradient-orange);
  opacity: 0;
  animation: glitchMove 3s linear infinite;
}

.glitch-line:nth-child(1) { animation-delay: 0s; top: 20%; }
.glitch-line:nth-child(2) { animation-delay: 1s; top: 60%; }
.glitch-line:nth-child(3) { animation-delay: 2s; top: 80%; }

@keyframes glitchMove {
  0% { 
    opacity: 0; 
    transform: translateX(-100%) scaleX(0); 
  }
  10% { 
    opacity: 1; 
    transform: translateX(-100%) scaleX(1); 
  }
  90% { 
    opacity: 1; 
    transform: translateX(100%) scaleX(1); 
  }
  100% { 
    opacity: 0; 
    transform: translateX(100%) scaleX(0); 
  }
}

/* --- ОСНОВНЫЕ СТИЛИ КОМПОНЕНТОВ --- */
.app-container {
  background: transparent;
  color: var(--text-primary);
  position: relative;
  z-index: 1;
}

/* Карточки */
.card, .earn-block, .friends-block {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  box-shadow: var(--shadow-secondary);
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: var(--gradient-orange);
}

/* Кнопки */
.action-button, .nav-button {
  background: var(--gradient-orange);
  color: var(--text-primary);
  border: none;
  border-radius: 8px;
  box-shadow: var(--shadow-primary);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.action-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.action-button:hover::before {
  left: 100%;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(255, 140, 53, 0.4);
}

/* Заголовки */
h1, h2, h3 {
  color: var(--text-primary);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* Навигация */
.app-navigation {
  background: var(--bg-secondary);
  border-top: 2px solid var(--accent-orange);
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
}

.nav-button.active {
  background: var(--gradient-orange);
  color: var(--text-primary);
}

/* Статус сообщения */
.status-message {
  background: var(--bg-card);
  border-left: 4px solid var(--accent-orange);
  color: var(--text-primary);
}

/* Поля ввода */
input, select, textarea {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  border-radius: 6px;
}

input:focus, select:focus, textarea:focus {
  border-color: var(--accent-orange);
  box-shadow: 0 0 0 2px rgba(255, 140, 53, 0.2);
}

/* Валютные табы */
.currency-tab {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  transition: all 0.3s ease;
}

.currency-tab.active {
  background: var(--gradient-orange);
  color: var(--text-primary);
  border-color: var(--accent-orange);
}

.currency-tab:hover {
  border-color: var(--accent-orange);
  color: var(--text-primary);
}
