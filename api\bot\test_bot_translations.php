<?php
/**
 * test_bot_translations.php
 * Тестирование системы переводов бота
 */

require_once __DIR__ . '/config.php';

// Подключаем только нужные функции из webhook.php
function loadBotTextsFromFile() {
    static $texts = null;

    if ($texts === null) {
        $textsFile = __DIR__ . '/bot_texts.json';
        if (file_exists($textsFile)) {
            $content = file_get_contents($textsFile);
            $texts = json_decode($content, true);
        } else {
            $texts = false;
        }
    }

    return $texts;
}

function getBotTextFromFile($key, $lang = 'ru', $params = []) {
    $texts = loadBotTextsFromFile();

    if (!$texts || !isset($texts[$lang])) {
        return null;
    }

    // Разбираем путь к тексту (например: "messages.welcome_title")
    $keys = explode('.', $key);
    $value = $texts[$lang];

    foreach ($keys as $k) {
        if (!isset($value[$k])) {
            return null;
        }
        $value = $value[$k];
    }

    // Заменяем параметры в тексте
    if (!empty($params)) {
        foreach ($params as $param => $replacement) {
            $value = str_replace('{' . $param . '}', $replacement, $value);
        }
    }

    return $value;
}

function detectUserLanguage($telegramUser) {
    // Получаем язык из Telegram
    $telegramLanguage = $telegramUser['language_code'] ?? 'en';

    // Список русскоязычных стран/языков
    $russianLanguages = ['ru', 'be', 'kk', 'ky', 'uz', 'uk'];

    // Если язык русскоязычный, возвращаем русский
    if (in_array($telegramLanguage, $russianLanguages)) {
        return 'ru';
    }

    // Для всех остальных - английский
    return 'en';
}

echo "=== ТЕСТИРОВАНИЕ СИСТЕМЫ ПЕРЕВОДОВ БОТА ===\n\n";

/**
 * Тест 1: Проверка загрузки файла bot_texts.json
 */
echo "1. Тестирование загрузки bot_texts.json:\n";
$texts = loadBotTextsFromFile();
if ($texts) {
    echo "✅ Файл bot_texts.json загружен успешно\n";
    echo "   - Найдено языков: " . count($texts) . "\n";
    echo "   - Доступные языки: " . implode(', ', array_keys($texts)) . "\n";
    
    // Проверяем структуру для русского языка
    if (isset($texts['ru'])) {
        echo "   - Секций в русском: " . count($texts['ru']) . "\n";
        echo "   - Секции: " . implode(', ', array_keys($texts['ru'])) . "\n";
    }
    
    // Проверяем структуру для английского языка
    if (isset($texts['en'])) {
        echo "   - Секций в английском: " . count($texts['en']) . "\n";
        echo "   - Секции: " . implode(', ', array_keys($texts['en'])) . "\n";
    }
} else {
    echo "❌ Ошибка загрузки bot_texts.json\n";
}
echo "\n";

/**
 * Тест 2: Проверка функции определения языка
 */
echo "2. Тестирование определения языка пользователя:\n";

// Тестовые данные пользователей
$testUsers = [
    ['language_code' => 'ru', 'expected' => 'ru', 'description' => 'Русский пользователь'],
    ['language_code' => 'en', 'expected' => 'en', 'description' => 'Английский пользователь'],
    ['language_code' => 'be', 'expected' => 'ru', 'description' => 'Белорусский пользователь'],
    ['language_code' => 'kk', 'expected' => 'ru', 'description' => 'Казахский пользователь'],
    ['language_code' => 'uk', 'expected' => 'ru', 'description' => 'Украинский пользователь'],
    ['language_code' => 'de', 'expected' => 'en', 'description' => 'Немецкий пользователь'],
    ['language_code' => 'fr', 'expected' => 'en', 'description' => 'Французский пользователь'],
    ['language_code' => 'es', 'expected' => 'en', 'description' => 'Испанский пользователь'],
];

foreach ($testUsers as $testUser) {
    $detected = detectUserLanguage($testUser);
    $status = ($detected === $testUser['expected']) ? '✅' : '❌';
    echo "   {$status} {$testUser['description']}: {$testUser['language_code']} -> {$detected} (ожидался: {$testUser['expected']})\n";
}
echo "\n";

/**
 * Тест 3: Проверка получения текстов из файла
 */
echo "3. Тестирование получения текстов:\n";

$testKeys = [
    'welcome.title',
    'welcome.subtitle', 
    'buttons.launch_app',
    'buttons.open_app',
    'help.title',
    'balance.title',
    'commands.start',
    'commands.help'
];

foreach (['ru', 'en'] as $lang) {
    echo "   Язык: {$lang}\n";
    foreach ($testKeys as $key) {
        $text = getBotTextFromFile($key, $lang);
        $status = $text ? '✅' : '❌';
        $preview = $text ? substr($text, 0, 50) . (strlen($text) > 50 ? '...' : '') : 'НЕ НАЙДЕН';
        echo "     {$status} {$key}: {$preview}\n";
    }
    echo "\n";
}

/**
 * Тест 4: Проверка применения настроек к боту (симуляция)
 */
echo "4. Тестирование структуры для применения к боту:\n";

if ($texts) {
    // Проверяем наличие ключей для Menu Button
    $menuButtonRu = $texts['ru']['bot_interface']['menu_button_text'] ?? null;
    $menuButtonEn = $texts['en']['bot_interface']['menu_button_text'] ?? null;
    
    echo "   Menu Button:\n";
    echo "     " . ($menuButtonRu ? '✅' : '❌') . " RU: " . ($menuButtonRu ?: 'НЕ НАЙДЕН') . "\n";
    echo "     " . ($menuButtonEn ? '✅' : '❌') . " EN: " . ($menuButtonEn ?: 'НЕ НАЙДЕН') . "\n";
    
    // Проверяем команды
    echo "   Commands:\n";
    $commandsRu = $texts['ru']['commands'] ?? [];
    $commandsEn = $texts['en']['commands'] ?? [];
    
    $requiredCommands = ['start', 'balance', 'stats', 'help'];
    foreach ($requiredCommands as $cmd) {
        $hasRu = isset($commandsRu[$cmd]);
        $hasEn = isset($commandsEn[$cmd]);
        echo "     /{$cmd}: " . ($hasRu ? '✅' : '❌') . " RU, " . ($hasEn ? '✅' : '❌') . " EN\n";
    }
    
    // Проверяем описания
    echo "   Descriptions:\n";
    $descRu = $texts['ru']['bot_interface']['description'] ?? $texts['ru']['bot_interface']['description_full'] ?? null;
    $descEn = $texts['en']['bot_interface']['description'] ?? $texts['en']['bot_interface']['description_full'] ?? null;
    
    echo "     " . ($descRu ? '✅' : '❌') . " RU Description: " . ($descRu ? 'НАЙДЕНО' : 'НЕ НАЙДЕНО') . "\n";
    echo "     " . ($descEn ? '✅' : '❌') . " EN Description: " . ($descEn ? 'НАЙДЕНО' : 'НЕ НАЙДЕНО') . "\n";
}
echo "\n";

/**
 * Тест 5: Проверка функции telegramRequest
 */
echo "5. Тестирование подключения к Telegram API:\n";
$botInfo = telegramRequest('getMe');
if ($botInfo) {
    echo "✅ Подключение к Telegram API работает\n";
    echo "   - Имя бота: " . ($botInfo['first_name'] ?? 'Неизвестно') . "\n";
    echo "   - Username: @" . ($botInfo['username'] ?? 'Неизвестно') . "\n";
    echo "   - ID: " . ($botInfo['id'] ?? 'Неизвестно') . "\n";
} else {
    echo "❌ Ошибка подключения к Telegram API\n";
    echo "   Проверьте токен бота в config.php\n";
}
echo "\n";

/**
 * Тест 6: Проверка текущих настроек бота
 */
echo "6. Проверка текущих настроек бота:\n";

// Проверяем webhook
$webhookInfo = telegramRequest('getWebhookInfo');
if ($webhookInfo) {
    echo "   Webhook:\n";
    echo "     URL: " . ($webhookInfo['url'] ?? 'НЕ УСТАНОВЛЕН') . "\n";
    echo "     Статус: " . (empty($webhookInfo['url']) ? '❌ НЕ АКТИВЕН' : '✅ АКТИВЕН') . "\n";
}

// Проверяем команды
$commands = telegramRequest('getMyCommands');
if ($commands) {
    echo "   Команды (по умолчанию): " . count($commands) . " команд\n";
    foreach ($commands as $cmd) {
        echo "     /{$cmd['command']}: {$cmd['description']}\n";
    }
}

// Проверяем команды для русского языка
$commandsRu = telegramRequest('getMyCommands', ['language_code' => 'ru']);
if ($commandsRu) {
    echo "   Команды (русский): " . count($commandsRu) . " команд\n";
}

// Проверяем команды для английского языка
$commandsEn = telegramRequest('getMyCommands', ['language_code' => 'en']);
if ($commandsEn) {
    echo "   Команды (английский): " . count($commandsEn) . " команд\n";
}

/**
 * Тест 7: Симуляция применения настроек
 */
echo "\n7. Симуляция применения настроек к боту:\n";

if ($texts) {
    echo "   Проверка данных для setChatMenuButton:\n";

    // Русский Menu Button
    $textRu = $texts['ru']['bot_interface']['menu_button_text'] ?? '🚀 Запустить';
    $menuButtonRu = [
        'type' => 'web_app',
        'text' => $textRu,
        'web_app' => ['url' => WEBAPP_URL]
    ];
    echo "     RU Menu Button: " . json_encode($menuButtonRu, JSON_UNESCAPED_UNICODE) . "\n";

    // Английский Menu Button
    $textEn = $texts['en']['bot_interface']['menu_button_text'] ?? '🚀 Launch';
    $menuButtonEn = [
        'type' => 'web_app',
        'text' => $textEn,
        'web_app' => ['url' => WEBAPP_URL]
    ];
    echo "     EN Menu Button: " . json_encode($menuButtonEn, JSON_UNESCAPED_UNICODE) . "\n";

    echo "   Проверка данных для setMyCommands:\n";

    // Команды для русского языка
    $commandsRu = [
        ['command' => 'start', 'description' => $texts['ru']['commands']['start'] ?? '🚀 Запустить приложение и начать зарабатывать'],
        ['command' => 'balance', 'description' => $texts['ru']['commands']['balance'] ?? '💰 Посмотреть баланс и историю выплат'],
        ['command' => 'stats', 'description' => $texts['ru']['commands']['stats'] ?? '📊 Статистика заработка и рефералов'],
        ['command' => 'help', 'description' => $texts['ru']['commands']['help'] ?? '❓ Помощь и инструкции по использованию']
    ];
    echo "     RU Commands: " . json_encode($commandsRu, JSON_UNESCAPED_UNICODE) . "\n";

    // Команды для английского языка
    $commandsEn = [
        ['command' => 'start', 'description' => $texts['en']['commands']['start'] ?? '🚀 Launch app and start earning'],
        ['command' => 'balance', 'description' => $texts['en']['commands']['balance'] ?? '💰 Check balance and withdrawal history'],
        ['command' => 'stats', 'description' => $texts['en']['commands']['stats'] ?? '📊 Earnings and referrals statistics'],
        ['command' => 'help', 'description' => $texts['en']['commands']['help'] ?? '❓ Help and usage instructions']
    ];
    echo "     EN Commands: " . json_encode($commandsEn, JSON_UNESCAPED_UNICODE) . "\n";
}

echo "\n=== ТЕСТИРОВАНИЕ ЗАВЕРШЕНО ===\n";
?>
