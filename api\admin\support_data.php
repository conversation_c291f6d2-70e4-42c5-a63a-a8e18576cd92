<?php
/**
 * api/admin/support_data.php
 * Функции для работы с данными поддержки
 */

require_once __DIR__ . '/support_config.php';

/**
 * Получить все чаты поддержки
 */
function getSupportChats() {
    if (!file_exists(SUPPORT_CHATS_FILE)) {
        return [];
    }
    
    $data = file_get_contents(SUPPORT_CHATS_FILE);
    return json_decode($data, true) ?: [];
}

/**
 * Сохранить чаты поддержки
 */
function saveSupportChats($chats) {
    $data = json_encode($chats, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    return file_put_contents(SUPPORT_CHATS_FILE, $data, LOCK_EX) !== false;
}

/**
 * Получить все сообщения поддержки
 */
function getSupportMessages() {
    if (!file_exists(SUPPORT_MESSAGES_FILE)) {
        return [];
    }
    
    $data = file_get_contents(SUPPORT_MESSAGES_FILE);
    return json_decode($data, true) ?: [];
}

/**
 * Сохранить сообщения поддержки
 */
function saveSupportMessages($messages) {
    $data = json_encode($messages, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    return file_put_contents(SUPPORT_MESSAGES_FILE, $data, LOCK_EX) !== false;
}

/**
 * Найти или создать чат поддержки для пользователя
 */
function findOrCreateSupportChat($userId, $username = null, $firstName = null, $lastName = null, $language = null) {
    $chats = getSupportChats();

    // Ищем существующий чат
    foreach ($chats as $chatId => $chat) {
        if ($chat['user_id'] == $userId) {
            // Обновляем информацию о пользователе
            $chats[$chatId]['username'] = $username;
            $chats[$chatId]['first_name'] = $firstName;
            $chats[$chatId]['last_name'] = $lastName;
            if ($language) {
                $chats[$chatId]['language'] = $language;
            }
            $chats[$chatId]['updated_at'] = date('Y-m-d H:i:s');
            saveSupportChats($chats);
            return $chatId;
        }
    }

    // Создаем новый чат
    $chatId = 'chat_' . $userId . '_' . time();
    $chats[$chatId] = [
        'user_id' => $userId,
        'username' => $username,
        'first_name' => $firstName,
        'last_name' => $lastName,
        'language' => $language ?: 'en',
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ];

    saveSupportChats($chats);
    return $chatId;
}

/**
 * Добавить сообщение в чат поддержки
 */
function addSupportMessage($chatId, $messageId, $fromUser, $text) {
    $messages = getSupportMessages();
    
    $messageData = [
        'chat_id' => $chatId,
        'message_id' => $messageId,
        'from_user' => $fromUser, // true - от пользователя, false - от поддержки
        'text' => $text,
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    $messages[] = $messageData;
    
    return saveSupportMessages($messages);
}

/**
 * Получить сообщения для конкретного чата
 */
function getChatMessages($chatId) {
    $messages = getSupportMessages();
    $chatMessages = [];
    
    foreach ($messages as $message) {
        if ($message['chat_id'] === $chatId) {
            $chatMessages[] = $message;
        }
    }
    
    // Сортируем по времени создания
    usort($chatMessages, function($a, $b) {
        return strtotime($a['created_at']) - strtotime($b['created_at']);
    });
    
    return $chatMessages;
}

/**
 * Получить информацию о чате
 */
function getSupportChatInfo($chatId) {
    $chats = getSupportChats();
    return isset($chats[$chatId]) ? $chats[$chatId] : null;
}

/**
 * Получить последнее сообщение для чата
 */
function getLastChatMessage($chatId) {
    $messages = getChatMessages($chatId);
    return !empty($messages) ? end($messages) : null;
}

/**
 * Получить количество непрочитанных сообщений от пользователя
 */
function getUnreadMessagesCount($chatId) {
    $messages = getChatMessages($chatId);
    $count = 0;
    
    // Считаем сообщения от пользователя после последнего сообщения от поддержки
    $lastSupportMessageTime = null;
    
    // Находим время последнего сообщения от поддержки
    for ($i = count($messages) - 1; $i >= 0; $i--) {
        if (!$messages[$i]['from_user']) {
            $lastSupportMessageTime = $messages[$i]['created_at'];
            break;
        }
    }
    
    // Считаем сообщения от пользователя после этого времени
    foreach ($messages as $message) {
        if ($message['from_user'] && 
            ($lastSupportMessageTime === null || $message['created_at'] > $lastSupportMessageTime)) {
            $count++;
        }
    }
    
    return $count;
}
?>
