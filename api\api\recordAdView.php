<?php
/**
 * api/recordAdView.php
 * API эндпоинт для записи просмотра рекламы и начисления награды (включая реф. бонус).
 */

// Включаем логирование для этого скрипта тоже (если нужно)
// ini_set('display_errors', 1); error_reporting(E_ALL);

header('Content-Type: application/json');

// --- Подключение зависимостей с проверкой ---
if (!(@require_once __DIR__ . '/config.php')) { http_response_code(500); error_log('FATAL: config.php not found in recordAdView.php'); echo json_encode(['error'=>'Ошибка сервера: CFG']); exit; }
if (!(@require_once __DIR__ . '/validate_initdata.php')) { http_response_code(500); error_log('FATAL: validate_initdata.php not found in recordAdView.php'); echo json_encode(['error'=>'Ошибка сервера: VID']); exit; }
if (!(@require_once __DIR__ . '/db_mock.php')) { http_response_code(500); error_log('FATAL: db_mock.php not found in recordAdView.php'); echo json_encode(['error'=>'Ошибка сервера: DBM']); exit; }
if (!(@require_once __DIR__ . '/security.php')) { http_response_code(500); error_log('FATAL: security.php not found in recordAdView.php'); echo json_encode(['error'=>'Ошибка сервера: SEC']); exit; }
// --- Конец проверки зависимостей ---


// 1. Получение IP для логирования (без проверки лимитов)
$clientIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
// ИСПРАВЛЕНИЕ: Убираем проверку IP rate limiting для кеша
// if (!checkIpRateLimit($clientIp)) {
//     error_log("recordAdView WARNING: IP {$clientIp} заблокирован из-за превышения лимита запросов");
//     http_response_code(429);
//     echo json_encode(['error' => 'Слишком много запросов. Попробуйте позже']);
//     exit;
// }

// 2. Получение и декодирование входных данных
$inputJSON = file_get_contents('php://input');
error_log("recordAdView DEBUG: Получен JSON: " . $inputJSON);

$input = json_decode($inputJSON, true);
error_log("recordAdView DEBUG: Декодированные данные: " . print_r($input, true));

if ($input === null || !isset($input['initData']) || empty($input['initData'])) {
    error_log("recordAdView ERROR: Нет данных или неверный JSON");
    http_response_code(400); echo json_encode(['error' => 'Ошибка запроса: Нет данных']); exit;
}
$initData = $input['initData'];
error_log("recordAdView INFO: Получен initData (длина: " . strlen($initData) . ") от IP: {$clientIp}");

// 3. Валидация initData
$validatedData = validateTelegramInitData($initData);
if ($validatedData === false) {
    // Ошибка уже залогирована внутри validateTelegramInitData
    http_response_code(403); echo json_encode(['error' => 'Ошибка: Неверные данные']); exit;
}
$userId = intval($validatedData['user']['id']);
error_log("recordAdView INFO: initData валидирован для user $userId от IP: {$clientIp}");

// 4. Загрузка данных ВСЕХ пользователей
$userData = loadUserData();
if (!is_array($userData)) {
     error_log("recordAdView ERROR: loadUserData вернул не массив."); http_response_code(500); echo json_encode(['error' => 'Ошибка сервера: LD1']); exit;
}
error_log("recordAdView INFO: Данные пользователей загружены для обработки награды user $userId.");

// 4. Проверка лимитов и начисление основной награды
$adType = $input['adType'] ?? 'default';
$rewardAmount = 10; // Значение по умолчанию для обратной совместимости

if ($adType === 'native_banner' && defined('AD_REWARD_NATIVE_BANNER')) {
    $rewardAmount = AD_REWARD_NATIVE_BANNER;
} elseif ($adType === 'interstitial' && defined('AD_REWARD_INTERSTITIAL')) {
    $rewardAmount = AD_REWARD_INTERSTITIAL;
} elseif ($adType === 'rewarded_video' && defined('AD_REWARD_REWARDED_VIDEO')) {
    $rewardAmount = AD_REWARD_REWARDED_VIDEO;
}

// Проверяем, не заблокирован ли пользователь
if (isset($userData[$userId]['blocked']) && $userData[$userId]['blocked']) {
    error_log("recordAdView WARNING: Попытка начисления монет заблокированному пользователю $userId");
    http_response_code(403);
    echo json_encode(['error' => 'Ваш аккаунт заблокирован из-за подозрительной активности']);
    exit;
}

// Проверяем лимит просмотров рекламы по типу
if (!checkAdViewLimitByType($userId, $adType, $userData)) {
    error_log("recordAdView WARNING: Превышен лимит просмотров рекламы типа $adType для пользователя $userId");

    // Увеличиваем счетчик подозрительной активности
    incrementSuspiciousActivity($userId, $userData, "ad_view_limit_exceeded_type_{$adType}");

    http_response_code(429);
    echo json_encode(['error' => "Превышен лимит просмотров рекламы типа '$adType'. Попробуйте позже"]);
    exit;
}

// Также проверяем общий лимит для дополнительной защиты
if (!checkAdViewLimit($userId, $userData)) {
    error_log("recordAdView WARNING: Превышен общий лимит просмотров рекламы для пользователя $userId");

    // Увеличиваем счетчик подозрительной активности
    incrementSuspiciousActivity($userId, $userData, 'ad_view_general_limit_exceeded');

    http_response_code(429);
    echo json_encode(['error' => 'Превышен общий лимит просмотров рекламы. Попробуйте позже']);
    exit;
}

// Логируем событие просмотра рекламы
logAuditEvent('ad_view', $userId, ['reward' => $rewardAmount, 'type' => $input['adType'] ?? 'unknown']);

// Начисляем награду
$newBalanceUser = increaseUserBalance($userId, $rewardAmount, $userData);
if ($newBalanceUser === false) {
    // Ошибка залогирована в increaseUserBalance
    http_response_code(500);
    echo json_encode(['error' => 'Ошибка: Не удалось обновить баланс']);
    exit;
}

// Обновляем общую сумму заработанных монет
if (!isset($userData[$userId]['total_earned'])) {
    $userData[$userId]['total_earned'] = $rewardAmount;
} else {
    $userData[$userId]['total_earned'] += $rewardAmount;
}

error_log("recordAdView INFO: Основная награда $rewardAmount начислена user $userId. Новый баланс: $newBalanceUser. Всего заработано: {$userData[$userId]['total_earned']}");

// 5. Начисление реферального бонуса
$referrerId = getUserReferrerId($userId, $userData);
if ($referrerId !== null) {
    error_log("recordAdView INFO: Найден реферер $referrerId для user $userId.");
    // Проверяем, существует ли реферер в данных (мог быть удален)
    if (isset($userData[$referrerId])) {
        $bonusPercent = defined('REFERRAL_BONUS_PERCENT') ? REFERRAL_BONUS_PERCENT : 0.10;
        $bonusAmount = floor($rewardAmount * $bonusPercent); // Округляем вниз

        if ($bonusAmount > 0) {
            $newBalanceReferrer = increaseUserBalance($referrerId, $bonusAmount, $userData);
            if ($newBalanceReferrer !== false) {
                // Обновляем статистику реферальных начислений (ТОЛЬКО бонусы от рефералов)
                if (!isset($userData[$referrerId]['referral_earnings'])) {
                    $userData[$referrerId]['referral_earnings'] = 0;
                }
                $userData[$referrerId]['referral_earnings'] += $bonusAmount;

                error_log("recordAdView INFO: Успешно начислен реф. бонус $bonusAmount пользователю $referrerId. Новый баланс реферера: $newBalanceReferrer, Всего заработано на рефералах: " . $userData[$referrerId]['referral_earnings']);
            } else {
                // Ошибка уже залогирована в increaseUserBalance
                error_log("recordAdView ERROR: Не удалось начислить реф. бонус $bonusAmount пользователю $referrerId (возможно, из-за ошибки в increaseUserBalance).");
                // Не прерываем выполнение, т.к. основная награда начислена
            }
        } else {
            error_log("recordAdView INFO: Реф. бонус для $referrerId равен 0 (награда: $rewardAmount, процент: $bonusPercent).");
        }
    } else {
         error_log("recordAdView WARNING: Реферер $referrerId для пользователя $userId не найден в актуальных данных (возможно, был удален).");
    }
}

// 6. Сохранение ВСЕХ измененных данных
if (!saveUserData($userData)) {
    error_log("recordAdView CRITICAL ERROR: Не удалось сохранить данные после начислений для $userId (и возможно реферера $referrerId)");
    http_response_code(500); echo json_encode(['error' => 'Ошибка сервера: Не удалось сохранить данные']); exit;
}
error_log("recordAdView INFO: Все данные успешно сохранены после обработки награды для $userId.");

// 7. Успешный ответ с полными данными для ЕДИНОЙ СИСТЕМЫ
http_response_code(200); // OK
echo json_encode([
    'success' => true,
    'newBalance' => $newBalanceUser,
    'reward' => $rewardAmount,
    'adType' => $adType,
    'timestamp' => time(),
    'message' => "Награда успешно начислена"
]);
error_log("recordAdView INFO: Успешный ответ отправлен для user $userId с наградой $rewardAmount.");
exit;
?>