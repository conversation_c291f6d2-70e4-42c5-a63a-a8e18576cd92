<?php
/**
 * api/admin/save_notification_settings.php
 * API для сохранения настроек уведомлений
 */

header('Content-Type: application/json');

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../error.log');
error_reporting(E_ALL);

// Подключаем файл с функциями аутентификации и авторизации
require_once __DIR__ . '/auth.php';

// Проверяем аутентификацию
session_start();
if (!isAuthenticated()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Не авторизован']);
    exit;
}

// Проверяем метод запроса
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Метод не разрешен']);
    exit;
}

// Подключение зависимостей
require_once __DIR__ . '/../config.php';

try {
    // Получаем данные из POST запроса
    $notificationsEnabled = isset($_POST['notifications_enabled']) ? ($_POST['notifications_enabled'] === '1') : false;
    $messageTemplate = isset($_POST['message_template']) ? trim($_POST['message_template']) : '';
    $botUsername = isset($_POST['bot_username']) ? trim($_POST['bot_username']) : '';
    $inactiveHours = isset($_POST['inactive_hours']) ? intval($_POST['inactive_hours']) : 24;
    
    // Валидация данных
    $errors = [];
    
    if (empty($messageTemplate)) {
        $errors[] = 'Шаблон сообщения не может быть пустым';
    }
    
    if (empty($botUsername)) {
        $errors[] = 'Username бота не может быть пустым';
    } elseif (!preg_match('/^[a-zA-Z0-9_]+$/', $botUsername)) {
        $errors[] = 'Username бота может содержать только буквы, цифры и подчеркивания';
    }
    
    if ($inactiveHours < 1 || $inactiveHours > 168) {
        $errors[] = 'Часы неактивности должны быть от 1 до 168 (неделя)';
    }
    
    if (!empty($errors)) {
        echo json_encode(['success' => false, 'error' => implode('; ', $errors)]);
        exit;
    }
    
    // Подготавливаем настройки для сохранения
    $settings = [
        'notifications_enabled' => ['value' => $notificationsEnabled ? '1' : '0', 'enabled' => $notificationsEnabled],
        'message_template' => ['value' => $messageTemplate, 'enabled' => true],
        'bot_username' => ['value' => $botUsername, 'enabled' => true],
        'inactive_hours' => ['value' => strval($inactiveHours), 'enabled' => true]
    ];

    // Сохраняем настройки в JSON файл
    $settingsFile = __DIR__ . '/../cron/notification_settings.json';

    // Создаем директорию если не существует
    $dir = dirname($settingsFile);
    if (!file_exists($dir)) {
        if (!mkdir($dir, 0755, true)) {
            throw new Exception('Не удалось создать директорию: ' . $dir);
        }
    }

    // Проверяем права на запись
    if (!is_writable($dir)) {
        throw new Exception('Нет прав на запись в директорию: ' . $dir);
    }

    $json = json_encode($settings, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    if ($json === false) {
        throw new Exception('Ошибка кодирования JSON: ' . json_last_error_msg());
    }

    $result = file_put_contents($settingsFile, $json, LOCK_EX);

    if ($result === false) {
        throw new Exception('Не удалось записать файл: ' . $settingsFile);
    }
    
    // Логируем изменения
    $logMessage = sprintf(
        "[%s] Admin settings updated: notifications=%s, inactive_hours=%d, bot=@%s",
        date('Y-m-d H:i:s'),
        $notificationsEnabled ? 'enabled' : 'disabled',
        $inactiveHours,
        $botUsername
    );
    error_log($logMessage);
    
    echo json_encode([
        'success' => true, 
        'message' => 'Настройки успешно сохранены',
        'settings' => [
            'notifications_enabled' => $notificationsEnabled,
            'message_template' => $messageTemplate,
            'bot_username' => $botUsername,
            'inactive_hours' => $inactiveHours
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Error saving notification settings: " . $e->getMessage());
    echo json_encode(['success' => false, 'error' => 'Ошибка сохранения настроек: ' . $e->getMessage()]);
}
?>
