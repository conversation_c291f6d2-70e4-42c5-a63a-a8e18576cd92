<?php
// Генератор детального CSS на основе настроек дизайна

// Путь к файлу настроек
$designSettingsFile = __DIR__ . '/../../design_settings.json';
$cssFile = __DIR__ . '/../../css/custom.css';

// Функция загрузки настроек
function loadDesignSettings($file) {
    if (!file_exists($file)) {
        return null;
    }
    
    $json = file_get_contents($file);
    return json_decode($json, true);
}

// Загружаем настройки
$settings = loadDesignSettings($designSettingsFile);

if (!$settings || !isset($settings['colors'])) {
    echo "Ошибка: не удалось загрузить настройки дизайна\n";
    exit(1);
}

$colors = $settings['colors'];

// Начинаем генерацию CSS
$css = "/* АВТОМАТИЧЕСКИ СГЕНЕРИРОВАННЫЙ CSS ИЗ ДЕТАЛЬНЫХ НАСТРОЕК ДИЗАЙНА */\n";
$css .= "/* НЕ РЕДАКТИРУЙТЕ ЭТОТ ФАЙЛ ВРУЧНУЮ - ОН БУДЕТ ПЕРЕЗАПИСАН */\n";
$css .= "/* Для изменения дизайна используйте админку: api/admin/detailed_design.php */\n\n";

// CSS переменные
$css .= ":root {\n";

// Глобальные переменные
if (isset($colors['global'])) {
    foreach ($colors['global'] as $key => $value) {
        $css .= "  --global-" . str_replace('_', '-', $key) . ": {$value} !important;\n";
    }
}

// Переменные для каждой секции
$sections = ['header', 'navigation', 'cards', 'buttons', 'calculator', 'history', 'forms'];
foreach ($sections as $section) {
    if (isset($colors[$section])) {
        foreach ($colors[$section] as $key => $value) {
            $css .= "  --{$section}-" . str_replace('_', '-', $key) . ": {$value} !important;\n";
        }
    }
}

$css .= "}\n\n";

// Глобальные стили
$css .= "/* ГЛОБАЛЬНЫЕ СТИЛИ */\n";
$css .= "body {\n";
$css .= "  background-color: var(--global-body-bg) !important;\n";
$css .= "  color: var(--global-text-primary) !important;\n";
$css .= "}\n\n";

$css .= "* {\n";
$css .= "  border-color: var(--global-border-color) !important;\n";
$css .= "}\n\n";

// Хедер
$css .= "/* ХЕДЕР */\n";
$css .= ".header, .app-header, .top-bar {\n";
$css .= "  background-color: var(--header-bg) !important;\n";
$css .= "  color: var(--header-text) !important;\n";
$css .= "}\n\n";

$css .= ".balance-info, .clickable-balance {\n";
$css .= "  background-color: var(--header-balance-bg) !important;\n";
$css .= "  color: var(--header-balance-text) !important;\n";
$css .= "  border-color: var(--header-balance-bg) !important;\n";
$css .= "}\n\n";

$css .= ".balance-info:hover, .clickable-balance:hover {\n";
$css .= "  background-color: var(--header-balance-hover) !important;\n";
$css .= "}\n\n";

$css .= ".avatar, .user-avatar {\n";
$css .= "  border-color: var(--header-avatar-border) !important;\n";
$css .= "  background-color: var(--header-avatar-bg) !important;\n";
$css .= "}\n\n";

// Навигация
$css .= "/* НАВИГАЦИЯ */\n";
$css .= ".navigation, .nav-bar, .bottom-nav {\n";
$css .= "  background-color: var(--navigation-bg) !important;\n";
$css .= "  border-color: var(--navigation-border) !important;\n";
$css .= "}\n\n";

$css .= ".nav-button, .nav-item {\n";
$css .= "  color: var(--navigation-text-inactive) !important;\n";
$css .= "}\n\n";

$css .= ".nav-button.active, .nav-item.active {\n";
$css .= "  background-color: var(--navigation-active-bg) !important;\n";
$css .= "  color: var(--navigation-active-text) !important;\n";
$css .= "}\n\n";

$css .= ".nav-button:hover, .nav-item:hover {\n";
$css .= "  background-color: var(--navigation-hover-bg) !important;\n";
$css .= "  color: var(--navigation-text) !important;\n";
$css .= "}\n\n";

// Карточки
$css .= "/* КАРТОЧКИ И БЛОКИ */\n";
$css .= ".card, .block, .section {\n";
$css .= "  background-color: var(--cards-bg) !important;\n";
$css .= "  border-color: var(--cards-border) !important;\n";
$css .= "}\n\n";

$css .= ".card:hover, .block:hover {\n";
$css .= "  background-color: var(--cards-hover-bg) !important;\n";
$css .= "}\n\n";

$css .= ".card-title, .block-title, .section-title {\n";
$css .= "  color: var(--cards-title-text) !important;\n";
$css .= "}\n\n";

$css .= ".card-content, .block-content, .section-content {\n";
$css .= "  color: var(--cards-content-text) !important;\n";
$css .= "}\n\n";

// Кнопки
$css .= "/* КНОПКИ */\n";
$css .= ".btn-primary, .primary-btn, .action-btn {\n";
$css .= "  background-color: var(--buttons-primary-bg) !important;\n";
$css .= "  color: var(--buttons-primary-text) !important;\n";
$css .= "  border-color: var(--buttons-primary-bg) !important;\n";
$css .= "}\n\n";

$css .= ".btn-primary:hover, .primary-btn:hover, .action-btn:hover {\n";
$css .= "  background-color: var(--buttons-primary-hover) !important;\n";
$css .= "  border-color: var(--buttons-primary-hover) !important;\n";
$css .= "}\n\n";

$css .= ".btn-secondary, .secondary-btn {\n";
$css .= "  background-color: var(--buttons-secondary-bg) !important;\n";
$css .= "  color: var(--buttons-secondary-text) !important;\n";
$css .= "  border-color: var(--buttons-secondary-bg) !important;\n";
$css .= "}\n\n";

$css .= ".btn-secondary:hover, .secondary-btn:hover {\n";
$css .= "  background-color: var(--buttons-secondary-hover) !important;\n";
$css .= "  border-color: var(--buttons-secondary-hover) !important;\n";
$css .= "}\n\n";

$css .= ".btn-success, .success-btn {\n";
$css .= "  background-color: var(--buttons-success-bg) !important;\n";
$css .= "  color: var(--buttons-success-text) !important;\n";
$css .= "  border-color: var(--buttons-success-bg) !important;\n";
$css .= "}\n\n";

$css .= ".btn-success:hover, .success-btn:hover {\n";
$css .= "  background-color: var(--buttons-success-hover) !important;\n";
$css .= "  border-color: var(--buttons-success-hover) !important;\n";
$css .= "}\n\n";

$css .= ".btn-danger, .danger-btn {\n";
$css .= "  background-color: var(--buttons-danger-bg) !important;\n";
$css .= "  color: var(--buttons-danger-text) !important;\n";
$css .= "  border-color: var(--buttons-danger-bg) !important;\n";
$css .= "}\n\n";

$css .= ".btn-danger:hover, .danger-btn:hover {\n";
$css .= "  background-color: var(--buttons-danger-hover) !important;\n";
$css .= "  border-color: var(--buttons-danger-hover) !important;\n";
$css .= "}\n\n";

// Калькулятор
$css .= "/* КАЛЬКУЛЯТОР ВАЛЮТ */\n";
$css .= ".currency-tab {\n";
$css .= "  background-color: var(--calculator-tab-bg) !important;\n";
$css .= "  color: var(--calculator-tab-text) !important;\n";
$css .= "  border-color: var(--calculator-input-border) !important;\n";
$css .= "}\n\n";

$css .= ".currency-tab.active {\n";
$css .= "  background-color: var(--calculator-tab-active-bg) !important;\n";
$css .= "  color: var(--calculator-tab-active-text) !important;\n";
$css .= "  border-color: var(--calculator-tab-active-bg) !important;\n";
$css .= "}\n\n";

$css .= ".currency-tab:hover {\n";
$css .= "  background-color: var(--calculator-tab-hover) !important;\n";
$css .= "}\n\n";

$css .= ".calculator input, .calc-input {\n";
$css .= "  background-color: var(--calculator-input-bg) !important;\n";
$css .= "  color: var(--calculator-input-text) !important;\n";
$css .= "  border-color: var(--calculator-input-border) !important;\n";
$css .= "}\n\n";

$css .= ".calculator input:focus, .calc-input:focus {\n";
$css .= "  border-color: var(--calculator-input-focus) !important;\n";
$css .= "  box-shadow: 0 0 0 0.2rem rgba(255, 107, 53, 0.25) !important;\n";
$css .= "}\n\n";

// История
$css .= "/* ИСТОРИЯ ВЫПЛАТ */\n";
$css .= ".history-item, .payment-item {\n";
$css .= "  background-color: var(--history-item-bg) !important;\n";
$css .= "  border-color: var(--history-item-border) !important;\n";
$css .= "  color: var(--history-item-text) !important;\n";
$css .= "}\n\n";

$css .= ".status-pending {\n";
$css .= "  color: var(--history-status-pending) !important;\n";
$css .= "}\n\n";

$css .= ".status-completed {\n";
$css .= "  color: var(--history-status-completed) !important;\n";
$css .= "}\n\n";

$css .= ".status-failed {\n";
$css .= "  color: var(--history-status-failed) !important;\n";
$css .= "}\n\n";

$css .= ".amount, .payment-amount {\n";
$css .= "  color: var(--history-amount-text) !important;\n";
$css .= "}\n\n";

// Формы
$css .= "/* ФОРМЫ И ПОЛЯ ВВОДА */\n";
$css .= "input, textarea, select {\n";
$css .= "  background-color: var(--forms-input-bg) !important;\n";
$css .= "  color: var(--forms-input-text) !important;\n";
$css .= "  border-color: var(--forms-input-border) !important;\n";
$css .= "}\n\n";

$css .= "input:focus, textarea:focus, select:focus {\n";
$css .= "  border-color: var(--forms-input-focus) !important;\n";
$css .= "  box-shadow: 0 0 0 0.2rem rgba(255, 107, 53, 0.25) !important;\n";
$css .= "}\n\n";

$css .= "label {\n";
$css .= "  color: var(--forms-label-text) !important;\n";
$css .= "}\n\n";

$css .= "::placeholder {\n";
$css .= "  color: var(--forms-placeholder-text) !important;\n";
$css .= "}\n\n";

// Сохраняем CSS файл
if (file_put_contents($cssFile, $css) !== false) {
    echo "CSS файл успешно сгенерирован: {$cssFile}\n";
    exit(0);
} else {
    echo "Ошибка при сохранении CSS файла: {$cssFile}\n";
    exit(1);
}
?>
