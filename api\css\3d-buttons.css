/* ======================================== */
/* 3D VOLUMETRIC BUTTONS - Physical Button Effects */
/* ======================================== */

/* --- CSS VARIABLES FOR 3D BUTTONS --- */
:root {
  /* 3D Button Colors */
  --btn-3d-primary: var(--accent-primary, #FFA500);
  --btn-3d-primary-dark: #e5940a;
  --btn-3d-primary-darker: #cc8400;
  --btn-3d-shadow: rgba(0, 0, 0, 0.25);
  --btn-3d-shadow-hover: rgba(0, 0, 0, 0.35);
  
  /* 3D Button Dimensions */
  --btn-3d-depth: 6px;
  --btn-3d-depth-pressed: 2px;
  --btn-3d-radius: 12px;
  --btn-3d-shadow-blur: 4px;
  
  /* Animation Timings */
  --btn-3d-transition-default: transform 600ms cubic-bezier(0.3, 0.7, 0.4, 1);
  --btn-3d-transition-hover: transform 250ms cubic-bezier(0.3, 0.7, 0.4, 1.5);
  --btn-3d-transition-active: transform 34ms;
}

/* --- BASE 3D BUTTON STRUCTURE --- */
.btn-3d {
  position: relative;
  border: none;
  background: transparent;
  padding: 0;
  cursor: pointer;
  outline-offset: 4px;
  transition: filter 250ms;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  font-family: inherit;
  display: inline-block;
  text-decoration: none;
}

/* Remove focus outline for mouse users, keep for keyboard users */
.btn-3d:focus:not(:focus-visible) {
  outline: none;
}

/* --- 3D BUTTON LAYERS --- */

/* Shadow Layer - Bottom layer that creates depth */
.btn-3d .btn-shadow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: var(--btn-3d-radius);
  background: var(--btn-3d-shadow);
  will-change: transform;
  transform: translateY(2px);
  transition: var(--btn-3d-transition-default);
  filter: blur(var(--btn-3d-shadow-blur));
}

/* Edge Layer - Middle layer that creates the side effect */
.btn-3d .btn-edge {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: var(--btn-3d-radius);
  background: linear-gradient(
    to left,
    var(--btn-3d-primary-darker) 0%,
    var(--btn-3d-primary-dark) 8%,
    var(--btn-3d-primary-dark) 92%,
    var(--btn-3d-primary-darker) 100%
  );
}

/* Front Layer - Top layer with content */
.btn-3d .btn-front {
  display: block;
  position: relative;
  padding: 12px 24px;
  border-radius: var(--btn-3d-radius);
  font-size: 1rem;
  font-weight: 600;
  color: white;
  background: var(--btn-3d-primary);
  will-change: transform;
  transform: translateY(-4px);
  transition: var(--btn-3d-transition-default);
  text-align: center;
  line-height: 1.2;
}

/* --- HOVER EFFECTS --- */
.btn-3d:hover {
  filter: brightness(110%);
}

.btn-3d:hover .btn-front {
  transform: translateY(-6px);
  transition: var(--btn-3d-transition-hover);
}

.btn-3d:hover .btn-shadow {
  transform: translateY(4px);
  transition: var(--btn-3d-transition-hover);
}

/* --- ACTIVE/PRESSED EFFECTS --- */
.btn-3d:active .btn-front {
  transform: translateY(-2px);
  transition: var(--btn-3d-transition-active);
}

.btn-3d:active .btn-shadow {
  transform: translateY(1px);
  transition: var(--btn-3d-transition-active);
}

/* --- SIZE VARIANTS --- */

/* Large Button */
.btn-3d.btn-large .btn-front {
  padding: 16px 32px;
  font-size: 1.25rem;
}

/* Small Button */
.btn-3d.btn-small .btn-front {
  padding: 8px 16px;
  font-size: 0.875rem;
}

/* Extra Small Button */
.btn-3d.btn-xs .btn-front {
  padding: 6px 12px;
  font-size: 0.75rem;
}

/* --- COLOR VARIANTS --- */

/* Success Button (Green) */
.btn-3d.btn-success {
  --btn-3d-primary: #4CAF50;
  --btn-3d-primary-dark: #45a049;
  --btn-3d-primary-darker: #3d8b40;
}

/* Danger Button (Red) */
.btn-3d.btn-danger {
  --btn-3d-primary: #f44336;
  --btn-3d-primary-dark: #da190b;
  --btn-3d-primary-darker: #c1170a;
}

/* Warning Button (Yellow) */
.btn-3d.btn-warning {
  --btn-3d-primary: #ff9800;
  --btn-3d-primary-dark: #e68900;
  --btn-3d-primary-darker: #cc7a00;
}

/* Info Button (Blue) */
.btn-3d.btn-info {
  --btn-3d-primary: #2196f3;
  --btn-3d-primary-dark: #1976d2;
  --btn-3d-primary-darker: #1565c0;
}

/* Secondary Button (Gray) */
.btn-3d.btn-secondary {
  --btn-3d-primary: #6c757d;
  --btn-3d-primary-dark: #5a6268;
  --btn-3d-primary-darker: #495057;
}

/* --- SPECIAL EFFECTS --- */

/* Glowing Button */
.btn-3d.btn-glow .btn-front {
  box-shadow: 0 0 20px rgba(255, 165, 0, 0.4);
}

.btn-3d.btn-glow:hover .btn-front {
  box-shadow: 0 0 30px rgba(255, 165, 0, 0.6);
}

/* Pulsing Button */
.btn-3d.btn-pulse .btn-front {
  animation: btn-pulse 2s infinite;
}

@keyframes btn-pulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(255, 165, 0, 0.7);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(255, 165, 0, 0);
  }
}

/* --- ICON SUPPORT --- */
.btn-3d .btn-icon {
  margin-right: 8px;
  font-size: 1.1em;
  vertical-align: middle;
}

.btn-3d .btn-icon-only {
  margin: 0;
}

/* Icon-only buttons */
.btn-3d.btn-icon-only .btn-front {
  padding: 12px;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* --- DISABLED STATE --- */
.btn-3d:disabled,
.btn-3d.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.btn-3d:disabled .btn-front,
.btn-3d.disabled .btn-front {
  transform: translateY(-1px);
}

.btn-3d:disabled .btn-shadow,
.btn-3d.disabled .btn-shadow {
  transform: translateY(0px);
}

/* --- MOBILE OPTIMIZATIONS --- */
@media (max-width: 768px) {
  .btn-3d {
    --btn-3d-depth: 4px;
    --btn-3d-depth-pressed: 1px;
  }
  
  .btn-3d .btn-front {
    transform: translateY(-3px);
  }
  
  .btn-3d:hover .btn-front {
    transform: translateY(-4px);
  }
  
  .btn-3d:active .btn-front {
    transform: translateY(-1px);
  }
}

/* --- ACCESSIBILITY IMPROVEMENTS --- */
@media (prefers-reduced-motion: reduce) {
  .btn-3d,
  .btn-3d .btn-front,
  .btn-3d .btn-shadow {
    transition: none;
  }
  
  .btn-3d.btn-pulse .btn-front {
    animation: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .btn-3d .btn-front {
    border: 2px solid currentColor;
  }
}

/* --- INTEGRATION WITH EXISTING THEME --- */

/* Интеграция с киберпанк темой */
.btn-3d.cyberpunk-style {
  --btn-3d-primary: var(--accent-primary, #ff8c35);
  --btn-3d-shadow: rgba(255, 140, 53, 0.3);
}

.btn-3d.cyberpunk-style .btn-front {
  background: linear-gradient(135deg, var(--btn-3d-primary) 0%, var(--btn-3d-primary-dark) 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.btn-3d.cyberpunk-style:hover .btn-front {
  background: linear-gradient(135deg, var(--btn-3d-primary) 0%, var(--btn-3d-primary-dark) 100%);
  box-shadow: 0 0 20px rgba(255, 140, 53, 0.4);
}

/* Стили для навигационных кнопок */
.nav-button.btn-3d {
  --btn-3d-depth: 3px;
  --btn-3d-radius: 8px;
}

.nav-button.btn-3d .btn-front {
  padding: 8px 12px;
  font-size: 0.875rem;
}

/* Стили для кнопок валют */
.currency-tab.btn-3d {
  --btn-3d-depth: 2px;
  --btn-3d-radius: 6px;
  width: 100%;
}

.currency-tab.btn-3d .btn-front {
  padding: 8px 12px;
  font-size: 0.75rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

/* Специальные эффекты для основных кнопок действий */
.btn-3d#openLinkButton {
  --btn-3d-primary: #2196f3;
  --btn-3d-primary-dark: #1976d2;
  --btn-3d-primary-darker: #1565c0;
}

.btn-3d#watchVideoButton {
  --btn-3d-primary: #4CAF50;
  --btn-3d-primary-dark: #45a049;
  --btn-3d-primary-darker: #3d8b40;
}

.btn-3d#openAdButton {
  --btn-3d-primary: var(--accent-primary, #FFA500);
  --btn-3d-primary-dark: #e5940a;
  --btn-3d-primary-darker: #cc8400;
}

.btn-3d#request-withdrawal-button {
  --btn-3d-primary: #ff9800;
  --btn-3d-primary-dark: #e68900;
  --btn-3d-primary-darker: #cc7a00;
}

/* Эффекты для кнопок с иконками */
.btn-3d .btn-icon {
  transition: transform 0.2s ease;
}

.btn-3d:hover .btn-icon {
  transform: scale(1.1);
}

.btn-3d:active .btn-icon {
  transform: scale(0.95);
}

/* Адаптация под существующие классы */
.btn-3d.action-button {
  width: 100%;
  margin-bottom: 12px;
}

.btn-3d.copy-button {
  min-width: 48px;
  height: 48px;
}

/* Эффекты для состояний загрузки */
.btn-3d.loading .btn-front {
  position: relative;
  color: transparent;
}

.btn-3d.loading .btn-front::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: btn3dSpin 1s linear infinite;
}

@keyframes btn3dSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Эффекты успеха */
.btn-3d.success .btn-front {
  background: #4CAF50;
  animation: btn3dSuccess 0.6s ease-out;
}

@keyframes btn3dSuccess {
  0% { transform: translateY(-4px) scale(1); }
  50% { transform: translateY(-6px) scale(1.05); }
  100% { transform: translateY(-4px) scale(1); }
}

/* Эффекты ошибки */
.btn-3d.error .btn-front {
  background: #f44336;
  animation: btn3dError 0.6s ease-out;
}

@keyframes btn3dError {
  0%, 100% { transform: translateY(-4px) translateX(0); }
  25% { transform: translateY(-4px) translateX(-4px); }
  75% { transform: translateY(-4px) translateX(4px); }
}
