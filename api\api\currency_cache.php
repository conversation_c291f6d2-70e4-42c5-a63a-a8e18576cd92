<?php
/**
 * Система кеширования курсов валют
 */

require_once __DIR__ . '/config.php';

define('CURRENCY_CACHE_FILE', __DIR__ . '/currency_cache.json');

/**
 * Загружает кешированные курсы валют
 */
function loadCurrencyCache() {
    if (!file_exists(CURRENCY_CACHE_FILE)) {
        error_log("currency_cache INFO: Файл кеша не найден, создаем пустой");
        return createEmptyCache();
    }
    
    $content = file_get_contents(CURRENCY_CACHE_FILE);
    if ($content === false) {
        error_log("currency_cache ERROR: Не удалось прочитать файл кеша");
        return createEmptyCache();
    }
    
    $data = json_decode($content, true);
    if ($data === null) {
        error_log("currency_cache ERROR: Ошибка парсинга JSON кеша");
        return createEmptyCache();
    }
    
    return $data;
}

/**
 * Сохраняет кешированные курсы валют
 */
function saveCurrencyCache($data) {
    $json = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    if ($json === false) {
        error_log("currency_cache ERROR: Ошибка кодирования JSON");
        return false;
    }
    
    $result = file_put_contents(CURRENCY_CACHE_FILE, $json, LOCK_EX);
    if ($result === false) {
        error_log("currency_cache ERROR: Не удалось сохранить файл кеша");
        return false;
    }
    
    @chmod(CURRENCY_CACHE_FILE, 0664);
    error_log("currency_cache INFO: Кеш сохранен (" . strlen($json) . " байт)");
    return true;
}

/**
 * Создает пустой кеш с fallback данными
 */
function createEmptyCache() {
    error_log("currency_cache WARNING: Создается пустой кэш. Немедленный запуск обновления...");
    
    // Создаем пустую структуру, чтобы избежать ошибок
    $emptyCache = [
        'last_updated' => 0,
        'update_interval' => 900,
        'currencies' => []
    ];
    saveCurrencyCache($emptyCache);

    // Немедленно запускаем обновление в фоне, чтобы не блокировать основной процесс
    // и получить актуальные данные
    require_once __DIR__ . '/update_cache.php';
    force_update_currency_cache();

    // Возвращаем свежеобновленные данные
    return loadCurrencyCache();
}

/**
 * Получает курс валюты из кеша
 */
function getCachedCurrencyRate($currency) {
    $cache = loadCurrencyCache();

    if (!isset($cache['currencies'][$currency])) {
        error_log("currency_cache WARNING: Валюта {$currency} не найдена в кеше");
        return null;
    }

    return $cache['currencies'][$currency];
}

/**
 * Алиас для getCachedCurrencyRate для совместимости
 */
function getCachedCurrency($currency) {
    return getCachedCurrencyRate($currency);
}

/**
 * Проверяет, нужно ли обновить кеш
 */
function isCacheExpired() {
    $cache = loadCurrencyCache();
    $lastUpdated = $cache['last_updated'] ?? 0;
    $interval = $cache['update_interval'] ?? 3600;
    
    return (time() - $lastUpdated) > $interval;
}

/**
 * Обновляет курс конкретной валюты
 */
function updateCurrencyInCache($currency, $rateUsd, $minAmount = null, $networkFee = null, $status = 'available') {
    $cache = loadCurrencyCache();
    
    if (!isset($cache['currencies'][$currency])) {
        $cache['currencies'][$currency] = [];
    }
    
    $cache['currencies'][$currency]['rate_usd'] = $rateUsd;
    $cache['currencies'][$currency]['last_updated'] = time();
    $cache['currencies'][$currency]['status'] = $status;
    
    if ($minAmount !== null) {
        $cache['currencies'][$currency]['min_amount'] = $minAmount;
    }
    
    if ($networkFee !== null) {
        $cache['currencies'][$currency]['network_fee'] = $networkFee;
    }
    
    return saveCurrencyCache($cache);
}

/**
 * Помечает кеш как обновленный
 */
function markCacheAsUpdated() {
    $cache = loadCurrencyCache();
    $cache['last_updated'] = time();
    return saveCurrencyCache($cache);
}

/**
 * Получает все валюты из кеша
 */
function getAllCachedCurrencies() {
    $cache = loadCurrencyCache();
    return $cache['currencies'] ?? [];
}

/**
 * Рассчитывает количество криптовалюты используя кешированные курсы
 */
function calculateCryptoAmountFromCache($usdAmount, $currency) {
    $currencyData = getCachedCurrencyRate($currency);
    
    if (!$currencyData) {
        return [
            'success' => false,
            'error' => "Валюта {$currency} не поддерживается",
            'error_code' => 'CURRENCY_NOT_SUPPORTED'
        ];
    }
    
    $rate = $currencyData['rate_usd'];
    $minAmount = $currencyData['min_amount'];
    $cryptoAmount = $usdAmount / $rate;
    
    // Проверяем минимум
    if ($cryptoAmount < $minAmount) {
        return [
            'success' => false,
            'error' => "Минимальная сумма для {$currency}: {$minAmount}",
            'error_code' => 'AMOUNT_TOO_LOW',
            'minimum_required' => $minAmount,
            'calculated_amount' => $cryptoAmount
        ];
    }
    
    return [
        'success' => true,
        'crypto_amount' => number_format($cryptoAmount, 8, '.', ''),
        'usd_amount' => $usdAmount,
        'currency' => strtoupper($currency),
        'rate_used' => $rate,
        'calculation_method' => 'cached',
        'cache_age' => time() - ($currencyData['last_updated'] ?? 0),
        'network_fee' => $currencyData['network_fee'] ?? 0,
        'status' => $currencyData['status'] ?? 'unknown'
    ];
}

?>
