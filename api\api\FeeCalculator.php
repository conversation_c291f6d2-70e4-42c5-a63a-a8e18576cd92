<?php
/**
 * Централизованный калькулятор комиссий с поддержкой гибридного режима
 * - Для ETH/BTC: динамические комиссии с автообновлением при ошибках
 * - Для TON/USDT: фиксированные максимальные комиссии
 */

require_once 'config.php';
require_once 'NOWPaymentsAPI.php';

class FeeCalculator {
    private $api;
    private static $instance = null;
    
    // Кэш для комиссий
    private $feeCache = [];
    private $cacheTimeout = 300; // 5 минут
    
    // Фиксированные максимальные комиссии (Вариант 2)
    private const FIXED_MAX_FEES = [
        'ton' => 0.0075,      // TON: 0.0075 TON ($0.02)
        'usdttrc20' => 4.33   // USDT: 4.33 USDT
    ];
    
    /**
     * Получает минимальную сумму для валюты из кэша
     */
    public function getMinAmount($currency) {
        $cacheFile = __DIR__ . '/currency_min_amounts.json';
        if (file_exists($cacheFile)) {
            $data = json_decode(file_get_contents($cacheFile), true);
            if (isset($data[$currency])) {
                return $data[$currency];
            }
        }
        
        // Возвращаем безопасные значения по умолчанию
        $defaults = [
            'eth' => 0.0001,
            'btc' => 0.000005,
            'ton' => 0.5,
            'usdttrc20' => 8.58
        ];
        
        return $defaults[$currency] ?? 0;
    }
    
    private function __construct() {
        $this->api = new NOWPaymentsAPI(
            NOWPAYMENTS_API_KEY,
            NOWPAYMENTS_PUBLIC_KEY,
            NOWPAYMENTS_IPN_SECRET,
            NOWPAYMENTS_API_URL
        );
    }
    
    /**
     * Получить единственный экземпляр калькулятора (Singleton)
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * 🚀 НОВЫЙ АЛГОРИТМ для калькулятора и авторасчёта
     * Показывает пользователю сумму ПОСЛЕ вычета комиссии (то что он реально получит)
     * Отправляет на выплату сумму С комиссией для успешного прохождения в NOWPayments
     *
     * @param int $coinsAmount Количество монет пользователя
     * @param string $currency Целевая криптовалюта
     * @return array Результат расчета
     */
    public function calculateWithdrawalAmount($coinsAmount, $currency) { // Это для отображения в калькуляторе
        try {
            error_log("FeeCalculator: 🚀 НОВЫЙ АЛГОРИТМ - Расчет для {$coinsAmount} монет -> {$currency}");

            // 1. Используем кэшированные данные для быстрого расчета
            require_once __DIR__ . '/currency_cache.php';
            $cachedData = getCachedCurrencyRate($currency);

            if ($cachedData) {
                return $this->calculateFromCachedData($coinsAmount, $currency, $cachedData);
            }
            // Fallback на API если кэш недоступен или устарел (для калькулятора это допустимо)
            error_log("FeeCalculator: Кэш недоступен для {$currency}, используем API для расчета калькулятора");
            return $this->calculateFromAPI($coinsAmount, $currency);
        } catch (Exception $e) {
            error_log("FeeCalculator ERROR: " . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Ошибка расчета: ' . $e->getMessage(),
                'error_code' => 'CALCULATION_ERROR'
            ];
        }
    }

    /**
     * 🚀 Расчет из API (для калькулятора, если кэш недоступен)
     * Показывает пользователю сумму ПОСЛЕ вычета комиссии (то что он реально получит)
     */
    private function calculateFromAPI($coinsAmount, $currency) {
        error_log("FeeCalculator: 🚀 Используем API для расчета калькулятора для {$currency}");

        // Конвертируем монеты в USD
        $usdAmount = $coinsAmount * CONVERSION_RATE;

        // Получаем минимальную сумму NOWPayments для этой валюты
        $minAmount = $this->api->getMinWithdrawalAmount($currency);
        if (!$minAmount) {
            throw new Exception("Не удалось получить минимальную сумму для {$currency}");
        }

        // Получаем курс конвертации
        $estimate = $this->api->getEstimateAmount($usdAmount, 'usd', $currency);
        if (!isset($estimate['estimated_amount'])) {
            throw new Exception('Не удалось получить курс конвертации');
        }
        $grossCryptoAmount = $estimate['estimated_amount']; // Сумма ДО вычета комиссии

        // Получаем комиссию
        $feeData = $this->getNOWPaymentsFee($currency, $grossCryptoAmount);
        $fee = $feeData['fee'] ?? 0;

        // Рассчитываем сумму ПОСЛЕ вычета комиссии (то что получит пользователь)
        $netCryptoAmount = $grossCryptoAmount - $fee;

        // Проверяем минимум
        if ($netCryptoAmount < $minAmount) {
            $minCoinsNeeded = $this->calculateMinimumCoinsForAmount($minAmount + $fee, $currency);
            return [
                'success' => false,
                'error' => "После вычета комиссии сумма (" . number_format($netCryptoAmount, 8, '.', '') . ") меньше минимума ({$minAmount})",
                'error_code' => 'AMOUNT_TOO_LOW_AFTER_FEE',
                'minimum_required' => $minAmount,
                'calculated_amount' => $netCryptoAmount,
                'minimum_coins_needed' => $minCoinsNeeded,
                'coins_shortage' => max(0, $minCoinsNeeded - $coinsAmount),
                'fee_amount' => $fee
            ];
        }

        // Успешный расчет
        return [
            'success' => true,
            'user_crypto_amount' => $netCryptoAmount, // ТО ЧТО ПОЛУЧИТ ПОЛЬЗОВАТЕЛЬ
            'nowpayments_crypto_amount' => $grossCryptoAmount, // ТО ЧТО ОТПРАВЛЯЕМ В NOWPAYMENTS
            'crypto_amount' => $netCryptoAmount, // Для совместимости с интерфейсом
            'usd_amount' => $usdAmount,
            'nowpayments_fee' => $fee, // Комиссия в крипте
            'currency' => $currency,
            'coins_amount' => $coinsAmount,
            'conversion_rate' => CONVERSION_RATE,
            'calculated_at' => date('Y-m-d H:i:s'),
            'calculation_method' => 'api_live',
            'cache_source' => false
        ];
    }

    /**
     * 🚀 Расчет из кэшированных данных (быстро и эффективно)
     * Показывает пользователю сумму ПОСЛЕ вычета комиссии
     */
    private function calculateFromCachedData($coinsAmount, $currency, $cachedData) {
        try {
            error_log("FeeCalculator: 🚀 Используем кэшированные данные для {$currency}");

            $rate = $cachedData['rate_usd'] ?? 0;
            $minAmount = $cachedData['min_amount'] ?? 0;
            $networkFeeUsd = $cachedData['network_fee'] ?? 0; // Комиссия в USD из кэша

            if ($rate <= 0) {
                throw new Exception("Неверный курс в кэше для {$currency}");
            }

            // 1. Конвертируем монеты в USD
            $totalUsdAmount = $coinsAmount * CONVERSION_RATE;

            // 2. Вычитаем комиссию сети (в USD) из общей суммы в USD
            $netUsdAmount = $totalUsdAmount - $networkFeeUsd;

            if ($netUsdAmount < 0) {
                $netUsdAmount = 0; // Не может быть отрицательной
            }

            // 3. Конвертируем чистую сумму USD в криптовалюту (это то, что получит пользователь)
            $userCryptoAmount = $netUsdAmount / $rate;

            // 4. Рассчитываем "грязную" сумму в криптовалюте (то, что отправляем в NOWPayments)
            $grossCryptoAmount = $totalUsdAmount / $rate;

            // 5. Проверяем, не стала ли сумма после вычета комиссии меньше минимальной
            if ($userCryptoAmount < $minAmount) {
                // Рассчитываем, сколько монет нужно, чтобы получить минимальную сумму
                $minUsdForCrypto = $minAmount * $rate; // Сколько USD стоит минималка
                $totalMinUsdNeeded = $minUsdForCrypto + $networkFeeUsd; // Добавляем комиссию
                $minCoinsNeeded = ceil($totalMinUsdNeeded / CONVERSION_RATE);

                return [
                    'success' => false,
                    'error' => "После вычета комиссии сумма (" . self::formatCryptoAmount($userCryptoAmount, $currency) . ") меньше минимума ({$minAmount})",
                    'error_code' => 'AMOUNT_TOO_LOW_AFTER_FEE',
                    'minimum_required' => $minAmount,
                    'calculated_amount' => $userCryptoAmount,
                    'minimum_coins_needed' => $minCoinsNeeded,
                    'coins_shortage' => max(0, $minCoinsNeeded - $coinsAmount),
                    'fee_amount_usd' => $networkFeeUsd // Комиссия в USD
                ];
            }

            // 6. Успешный расчет
            return [
                'success' => true,
                'user_crypto_amount' => self::formatCryptoAmount($userCryptoAmount, $currency),       // ТО ЧТО ПОЛУЧИТ ПОЛЬЗОВАТЕЛЬ
                'nowpayments_crypto_amount' => self::formatCryptoAmount($grossCryptoAmount, $currency), // ТО ЧТО ОТПРАВЛЯЕМ В NOWPAYMENTS
                'crypto_amount' => self::formatCryptoAmount($userCryptoAmount, $currency),             // Для совместимости с интерфейсом
                'usd_amount' => $totalUsdAmount,
                'nowpayments_fee_usd' => $networkFeeUsd, // Комиссия в USD
                'currency' => $currency,
                'coins_amount' => $coinsAmount,
                'conversion_rate' => CONVERSION_RATE,
                'calculated_at' => date('Y-m-d H:i:s'),
                'calculation_method' => 'cached_data_revised',
                'cache_source' => true
            ];

        } catch (Exception $e) {
            error_log("FeeCalculator ERROR in calculateFromCachedData: " . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Ошибка расчета из кэша: ' . $e->getMessage(),
                'error_code' => 'CACHE_CALCULATION_ERROR'
            ];
        }
    }

    /**
     * calculateRequiredCoins - СТАРАЯ ЛОГИКА (для справки): Рассчитать сколько монет нужно для желаемой суммы
     * @param int $coinsAmount Количество монет пользователя
     * @param string $currency Целевая криптовалюта
     * @return array Результат расчета
     */
    public function calculateRequiredCoins($coinsAmount, $currency) {
        try {
            error_log("FeeCalculator: Расчет для {$coinsAmount} монет -> {$currency}");
            
            // НОВЫЙ ПОДХОД: Рассчитываем от желаемой суммы пользователя

            // 1. Конвертируем монеты в USD
            $usdAmount = $coinsAmount * CONVERSION_RATE;

            // 2. Получаем приблизительную сумму в криптовалюте (это то что хочет получить пользователь)
            $estimate = $this->api->getEstimateAmount($usdAmount, 'usd', $currency);

            if (!isset($estimate['estimated_amount'])) {
                throw new Exception('Не удалось получить курс конвертации');
            }

            $desiredCryptoAmount = $estimate['estimated_amount']; // То что хочет получить пользователь

            // 3. Получаем комиссию для этой суммы
            $feeData = $this->getNOWPaymentsFee($currency, $desiredCryptoAmount);
            $nowPaymentsFee = $feeData['fee'];

            // 4. Рассчитываем сколько нужно отправить в NOWPayments (желаемая сумма + комиссия)
            $cryptoAmountForNowPayments = $desiredCryptoAmount + $nowPaymentsFee;

            // 5. Рассчитываем сколько монет РЕАЛЬНО нужно для этой суммы
            $estimateForTotal = $this->api->getEstimateAmount($cryptoAmountForNowPayments, $currency, 'usd');
            if (!isset($estimateForTotal['estimated_amount'])) {
                throw new Exception('Не удалось рассчитать итоговую стоимость');
            }

            $totalUsdNeeded = $estimateForTotal['estimated_amount'];
            $totalCoinsNeeded = ceil($totalUsdNeeded / CONVERSION_RATE);

            // 6. Проверяем достаточность монет пользователя
            if ($totalCoinsNeeded > $coinsAmount) {
                $shortage = $totalCoinsNeeded - $coinsAmount;
                return [
                    'success' => false,
                    'error' => "Недостаточно монет! Нужно: {$totalCoinsNeeded} (не хватает: {$shortage})",
                    'error_code' => 'INSUFFICIENT_COINS',
                    'coins_needed' => $totalCoinsNeeded,
                    'coins_shortage' => $shortage,
                    'desired_crypto_amount' => $desiredCryptoAmount,
                    'nowpayments_fee' => $nowPaymentsFee
                ];
            }

            // 7. Проверяем минимум
            $minAmount = $this->api->getMinWithdrawalAmount($currency);
            if ($minAmount && $desiredCryptoAmount < $minAmount) {
                return [
                    'success' => false,
                    'error' => "Минимальная сумма для выплаты {$currency}: {$minAmount}",
                    'error_code' => 'AMOUNT_TOO_LOW',
                    'minimum_required' => $minAmount,
                    'calculated_amount' => $desiredCryptoAmount
                ];
            }

            error_log("FeeCalculator: УСПЕХ - Пользователь получит {$desiredCryptoAmount} {$currency}, NOWPayments получит {$cryptoAmountForNowPayments} {$currency}, комиссия {$nowPaymentsFee} {$currency}");
            
            return [
                'success' => true,
                'crypto_amount' => $desiredCryptoAmount, // ТО ЧТО ПОЛУЧИТ ПОЛЬЗОВАТЕЛЬ (показываем в калькуляторе и авторасчете)
                'crypto_amount_gross' => $cryptoAmountForNowPayments, // ТО ЧТО ОТПРАВЛЯЕМ В NOWPAYMENTS
                'crypto_amount_for_nowpayments' => $cryptoAmountForNowPayments, // Для совместимости
                'usd_amount' => $usdAmount, // Исходная сумма в USD
                'usd_total_needed' => $totalUsdNeeded, // Сколько USD реально нужно
                'coins_needed' => $totalCoinsNeeded, // Сколько монет реально нужно
                'nowpayments_fee' => $nowPaymentsFee,
                'fee_details' => $feeData,
                'currency' => $currency,
                'coins_amount' => $coinsAmount,
                'conversion_rate' => CONVERSION_RATE,
                'calculated_at' => date('Y-m-d H:i:s'),
                'calculation_method' => 'user_focused_calculator'
            ];
            
        } catch (Exception $e) {
            error_log("FeeCalculator ERROR: " . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Ошибка расчета: ' . $e->getMessage(),
                'error_code' => 'CALCULATION_ERROR'
            ];
        }
    }
    
    /**
     * 🎯 calculatePerfectNOWPaymentsAmount: Рассчитать точную сумму для отправки в NOWPayments
     * чтобы пользователь получил РОВНО желаемую сумму
     *
     * @param float $desiredUserAmount Сумма которую должен получить пользователь
     * @param string $currency Валюта
     * @return array Результат расчета
     */
    public function calculatePerfectNOWPaymentsAmount($desiredUserAmount, $currency) {
        try {
            error_log("FeeCalculator: 🎯 Расчет идеальной суммы для NOWPayments, пользователь должен получить {$desiredUserAmount} {$currency}");

            $maxIterations = 5;
            $tolerance = 0.00001; // Допустимая погрешность

            // Начинаем с приблизительной оценки
            $currentNOWPaymentsAmount = $desiredUserAmount;

            for ($i = 0; $i < $maxIterations; $i++) {
                // Получаем комиссию для текущей суммы
                $feeData = $this->getNOWPaymentsFee($currency, $currentNOWPaymentsAmount);
                $fee = $feeData['fee'];

                // Рассчитываем что получит пользователь
                $actualUserAmount = $currentNOWPaymentsAmount - $fee;

                error_log("FeeCalculator: Итерация {$i}: NOWPayments={$currentNOWPaymentsAmount}, комиссия={$fee}, пользователь получит={$actualUserAmount}, нужно={$desiredUserAmount}");

                // Проверяем точность
                $difference = abs($actualUserAmount - $desiredUserAmount);
                if ($difference <= $tolerance) {
                    error_log("FeeCalculator: ✅ Найдена идеальная сумма за {$i} итераций");
                    return [
                        'success' => true,
                        'nowpayments_amount' => $currentNOWPaymentsAmount,
                        'actual_fee' => $fee,
                        'user_will_receive' => $actualUserAmount,
                        'iterations' => $i + 1
                    ];
                }

                // Корректируем сумму для следующей итерации
                if ($actualUserAmount < $desiredUserAmount) {
                    // Пользователь получит меньше - увеличиваем сумму для NOWPayments
                    $deficit = $desiredUserAmount - $actualUserAmount;
                    $currentNOWPaymentsAmount += $deficit * 1.1; // +10% запас
                } else {
                    // Пользователь получит больше - уменьшаем сумму для NOWPayments
                    $excess = $actualUserAmount - $desiredUserAmount;
                    $currentNOWPaymentsAmount -= $excess * 0.9; // -10% для безопасности
                }
            }

            // Если не удалось найти точное значение, возвращаем последний результат
            $feeData = $this->getNOWPaymentsFee($currency, $currentNOWPaymentsAmount);
            $fee = $feeData['fee'];
            $actualUserAmount = $currentNOWPaymentsAmount - $fee;

            error_log("FeeCalculator: ⚠️ Не удалось найти идеальную сумму за {$maxIterations} итераций, используем приблизительную");

            return [
                'success' => true,
                'nowpayments_amount' => $currentNOWPaymentsAmount,
                'actual_fee' => $fee,
                'user_will_receive' => $actualUserAmount,
                'iterations' => $maxIterations,
                'approximate' => true
            ];

        } catch (Exception $e) {
            error_log("FeeCalculator ERROR in calculatePerfectNOWPaymentsAmount: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * calculateMinimumCoinsForAmount: Рассчитать минимальное количество монет для получения определенной суммы
     *
     * @param float $targetAmount Целевая сумма в криптовалюте
     * @param string $currency Валюта
     * @return int Минимальное количество монет
     */
    private function calculateMinimumCoinsForAmount($targetAmount, $currency) {
        try {
            // Рассчитываем сколько нужно отправить в NOWPayments для получения целевой суммы
            $result = $this->calculatePerfectNOWPaymentsAmount($targetAmount, $currency);

            if (!$result['success']) {
                // Fallback расчет с запасом
                $estimateUSD = $this->convertCryptoToUSD($targetAmount * 1.3, $currency); // +30% запас
                return ceil($estimateUSD / CONVERSION_RATE);
            }

            // Конвертируем сумму для NOWPayments в USD
            $usdNeeded = $this->convertCryptoToUSD($result['nowpayments_amount'], $currency);

            // Конвертируем USD в монеты
            return ceil($usdNeeded / CONVERSION_RATE);

        } catch (Exception $e) {
            error_log("FeeCalculator ERROR in calculateMinimumCoinsForAmount: " . $e->getMessage());
            // Fallback расчет
            $estimateUSD = $this->convertCryptoToUSD($targetAmount * 1.5, $currency); // +50% запас
            return ceil($estimateUSD / CONVERSION_RATE);
        }
    }

    /**
     * convertCryptoToUSD: Конвертировать криптовалюту в USD
     *
     * @param float $cryptoAmount Сумма в криптовалюте
     * @param string $currency Валюта
     * @return float Сумма в USD
     */
    private function convertCryptoToUSD($cryptoAmount, $currency) {
        try {
            $estimate = $this->api->getEstimateAmount($cryptoAmount, $currency, 'usd');

            if (isset($estimate['estimated_amount'])) {
                return $estimate['estimated_amount'];
            }

            // Fallback курсы
            $fallbackRates = [
                'eth' => 2670,
                'btc' => 97000,
                'ton' => 6.7,
                'usdttrc20' => 1.0,
                'trx' => 0.25
            ];

            $rate = $fallbackRates[strtolower($currency)] ?? 1;
            return $cryptoAmount * $rate;

        } catch (Exception $e) {
            error_log("FeeCalculator ERROR in convertCryptoToUSD: " . $e->getMessage());
            return $cryptoAmount; // Fallback 1:1
        }
    }

    /**
     * getNOWPaymentsFee: Получить комиссию NOWPayments с учетом режима работы
     */
    private function getNOWPaymentsFee($currency, $amount) {
        // Для TON/USDT всегда используем фиксированные комиссии (Вариант 2)
        if (isset(self::FIXED_MAX_FEES[$currency])) {
            return [
                'fee' => self::FIXED_MAX_FEES[$currency],
                'currency' => strtoupper($currency),
                'amount' => $amount,
                'source' => 'fixed_max_fee'
            ];
        }
        
        $cacheKey = $currency . '_' . round($amount, 8);
        $now = time();
        
        // Проверяем кэш
        if (isset($this->feeCache[$cacheKey]) &&
            ($now - $this->feeCache[$cacheKey]['timestamp']) < $this->cacheTimeout) {
            return $this->feeCache[$cacheKey]['data'];
        }
        
        // Получаем свежие данные
        $feeEstimate = $this->api->getWithdrawalFeeEstimate($currency, $amount);
        
        $feeData = [
            'fee' => 0,
            'currency' => strtoupper($currency),
            'amount' => $amount,
            'source' => 'fallback'
        ];
        
        if ($feeEstimate && isset($feeEstimate['fee'])) {
            $feeData = [
                'fee' => $feeEstimate['fee'],
                'currency' => strtoupper($currency),
                'amount' => $amount,
                'source' => 'nowpayments_api',
                'raw_response' => $feeEstimate
            ];
        } else {
            // Fallback комиссии для ETH/BTC
            $fallbackFees = [
                'eth' => 0.00016,    // 0.00016 ETH ($0.39)
                'btc' => 0.00000960  // 0.00000960 BTC ($1.03)
            ];
            
            $feeData['fee'] = $fallbackFees[strtolower($currency)] ?? ($amount * 0.01);
            $feeData['source'] = 'fallback_estimate';
        }
        
        // Кэшируем результат
        $this->feeCache[$cacheKey] = [
            'data' => $feeData,
            'timestamp' => $now
        ];
        
        return $feeData;
    }
    
    /**
     * emergencyUpdateFee: Экстренное обновление комиссии при ошибке выплаты
     */
    public function emergencyUpdateFee($currency) {
        // Очищаем кэш для этой валюты
        foreach ($this->feeCache as $key => $value) {
            if (strpos($key, $currency.'_') === 0) {
                unset($this->feeCache[$key]);
            }
        }
        
        error_log("FeeCalculator: Экстренное обновление комиссий для $currency");
        
        // Возвращаем обновленную комиссию
        return $this->getNOWPaymentsFee($currency, self::MIN_AMOUNTS[$currency] * 2);
    }
    
    /**
     * getMinWithdrawalAmount: Получить минимальную сумму выплаты с учетом комиссии
     */
    public function getMinWithdrawalAmount($currency) {
        if (isset(self::FIXED_MAX_FEES[$currency])) {
            // Для TON/USDT: min_nowpayments + fixed_fee
            return self::MIN_AMOUNTS[$currency] + self::FIXED_MAX_FEES[$currency];
        }
        
        // Для ETH/BTC: min_nowpayments + current_fee
        $currentFee = $this->getNOWPaymentsFee($currency, self::MIN_AMOUNTS[$currency])['fee'];
        return self::MIN_AMOUNTS[$currency] + $currentFee;
    }
    
    /**
     * checkSufficientBalance: Проверить достаточность баланса для вывода
     */
    public function checkSufficientBalance($coinsAmount, $userBalance) {
        if ($coinsAmount > $userBalance) {
            $needed = $coinsAmount - $userBalance;
            return [
                'sufficient' => false,
                'needed' => $needed,
                'message' => "Недостаточно средств! Нужно: {$needed} монет"
            ];
        }
        
        return ['sufficient' => true];
    }
    
    /**
     * getMinimumCoinsForCurrency: Получить минимальную сумму в монетах для валюты С УЧЕТОМ КОМИССИИ
     * Это решает проблему когда пользователь вводит сумму, но после вычета комиссии она меньше минимальной
     */
    public function getMinimumCoinsForCurrency($currency) {
        try {
            $minCrypto = $this->api->getMinWithdrawalAmount($currency);
            if ($minCrypto === null) {
                return null;
            }

            error_log("FeeCalculator: Расчет минимума для {$currency}, минимум NOWPayments: {$minCrypto}");

            // НОВАЯ ЛОГИКА: Рассчитываем с учетом комиссии методом итераций
            // Начинаем с приблизительной оценки и уточняем

            // 1. Приблизительная оценка: минимум + 50% запас на комиссию
            $estimate = $this->api->getEstimateAmount($minCrypto * 1.5, $currency, 'usd');
            if (!isset($estimate['estimated_amount'])) {
                return null;
            }

            $startUsd = $estimate['estimated_amount'];
            $startCoins = ceil($startUsd / CONVERSION_RATE);

            // 2. Итеративный поиск точного минимума
            $minCoinsRequired = $this->findMinimumCoinsWithFees($currency, $minCrypto, $startCoins);

            return [
                'min_crypto_required' => $minCrypto, // Минимум NOWPayments
                'min_coins_naive' => ceil(($minCrypto * 1.2) / CONVERSION_RATE), // Старый расчет для сравнения
                'min_coins_with_fees' => $minCoinsRequired, // ПРАВИЛЬНЫЙ минимум с учетом комиссии
                'currency' => $currency,
                'calculation_method' => 'iterative_with_fees'
            ];

        } catch (Exception $e) {
            error_log("FeeCalculator ERROR in getMinimumCoinsForCurrency: " . $e->getMessage());
            return null;
        }
    }

    /**
     * findMinimumCoinsWithFees: Найти минимальное количество монет методом итераций
     * чтобы после вычета комиссии получилась сумма не меньше минимальной
     */
    private function findMinimumCoinsWithFees($currency, $targetMinCrypto, $startCoins) {
        $maxIterations = 10;
        $tolerance = 0.00001; // Допустимая погрешность

        $currentCoins = $startCoins;

        for ($i = 0; $i < $maxIterations; $i++) {
            // Рассчитываем что получится с текущим количеством монет
            $result = $this->calculateWithdrawalAmount($currentCoins, $currency);

            if (!$result['success']) {
                // Если ошибка, увеличиваем количество монет
                $currentCoins = ceil($currentCoins * 1.2);
                continue;
            }

            $finalCryptoAmount = $result['crypto_amount'];

            error_log("FeeCalculator: Итерация {$i}: {$currentCoins} монет -> {$finalCryptoAmount} {$currency} (нужно >= {$targetMinCrypto})");

            // Проверяем достаточность
            if ($finalCryptoAmount >= $targetMinCrypto) {
                // Достаточно! Но может быть можно меньше?
                if ($finalCryptoAmount - $targetMinCrypto < $tolerance) {
                    // Очень близко к минимуму - это оптимально
                    return $currentCoins;
                } else {
                    // Слишком много, попробуем меньше
                    $reduction = ($finalCryptoAmount - $targetMinCrypto) / $finalCryptoAmount;
                    $newCoins = ceil($currentCoins * (1 - $reduction * 0.5));

                    if ($newCoins >= $currentCoins) {
                        // Не удалось уменьшить, возвращаем текущее значение
                        return $currentCoins;
                    }

                    $currentCoins = max($newCoins, 1);
                }
            } else {
                // Недостаточно, увеличиваем
                $deficit = $targetMinCrypto - $finalCryptoAmount;
                $increase = $deficit / $finalCryptoAmount;
                $currentCoins = ceil($currentCoins * (1 + $increase + 0.1)); // +10% запас
            }
        }

        error_log("FeeCalculator: Не удалось найти точный минимум за {$maxIterations} итераций, возвращаем {$currentCoins}");
        return $currentCoins;
    }
    
    /**
     * formatCryptoAmount: Форматировать сумму криптовалюты для отображения
     * ИСПРАВЛЕНО: Все криптовалюты до 8 знаков после запятой
     */
    public function formatCryptoAmount($amount, $currency) {
        // ИСПРАВЛЕНИЕ: Все криптовалюты округляем до 8 знаков для точности
        switch (strtolower($currency)) {
            case 'btc':
                return number_format($amount, 8, '.', '');
            case 'eth':
                return number_format($amount, 8, '.', ''); // Было 6, стало 8
            case 'ton':
                return number_format($amount, 8, '.', ''); // Было 4, стало 8
            case 'usdttrc20':
                return number_format($amount, 8, '.', ''); // Было 4, стало 8
            default:
                return number_format($amount, 8, '.', ''); // Все остальные тоже 8 знаков
        }
    }
    
    /**
     * emergencyUpdateFeeAndGetError: АВАРИЙНОЕ ОБНОВЛЕНИЕ КЭША ПРИ ОШИБКЕ ВЫПЛАТЫ
     * Вызывается, когда NOWPayments вернул ошибку о недостаточной сумме из-за изменения комиссии
     */
    public function emergencyUpdateFeeAndGetError($currency) {
        error_log("FeeCalculator: 🚨 Аварийное обновление кэша для {$currency}...");

        // Принудительно запускаем скрипт обновления кэша
        // Это самый надежный способ, так как он не зависит от настроек exec/include
        require_once 'update_cache.php';
        force_update_currency_cache();

        error_log("FeeCalculator: 🚨 Кэш для {$currency} принудительно обновлен.");

        // Возвращаем специальную ошибку для фронтенда
        return [
            'success' => false,
            'error' => 'Комиссия сервиса выплат изменилась! Пожалуйста, проверьте и пересчитайте сумму вывода.',
            'error_code' => 'FEE_CHANGED_RECALCULATE' // Уникальный код ошибки
        ];
    }


    /**
     * clearCache: Очистить кэш комиссий
     */
    public function clearCache() {
        $this->feeCache = [];
    }
}
?>
