<?php
/**
 * config.php
 * Конфигурация Telegram бота
 */

// Токен бота @uniqpaid_paid_bot
define('BOT_TOKEN', '8146437206:AAET9K-yAOukAr9gKuntU9IQds8DAEiAbnA');

// URL для webhook (замените на ваш домен)
define('WEBHOOK_URL', 'https://app.uniqpaid.com/test3/bot/webhook.php');

// URL мини-приложения
define('WEBAPP_URL', 'https://app.uniqpaid.com/test3/');

// Настройки базы данных (используем тот же файл, что и мини-приложение)
define('USER_DATA_FILE', __DIR__ . '/../api/user_data.json');

// Настройки монет
define('COINS_PER_VIEW', 10);
define('COIN_VALUE_USD', 0.001); // 1 монета = 0.001 USD

// Настройки реферальной программы
define('REFERRAL_BONUS_PERCENT', 10); // 10% от заработка рефералов

// Имя бота для ссылок
define('BOT_USERNAME', 'uniqpaid_paid_bot'); // Имя вашего бота

// API Telegram
define('TELEGRAM_API_URL', 'https://api.telegram.org/bot' . BOT_TOKEN . '/');

// Логирование
define('LOG_FILE', __DIR__ . '/bot.log');

/**
 * Логирование сообщений бота
 */
function botLog($message) {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] {$message}\n";
    file_put_contents(LOG_FILE, $logMessage, FILE_APPEND | LOCK_EX);
}

/**
 * Отправка запроса к Telegram API с улучшенной диагностикой
 */
function telegramRequest($method, $data = []) {
    $url = TELEGRAM_API_URL . $method;

    botLog("INFO: Отправка запроса к Telegram API: {$method}");

    // Используем cURL для лучшего контроля и диагностики
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // Временно отключено для тестирования
    curl_setopt($ch, CURLOPT_USERAGENT, 'UniQPaid Telegram Bot/1.0');
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/x-www-form-urlencoded'
    ]);

    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    $info = curl_getinfo($ch);
    curl_close($ch);

    // Логируем детальную информацию
    botLog("INFO: HTTP Code: {$httpCode}, Time: " . round($info['total_time'], 3) . "s");

    if ($result === false || $error) {
        botLog("ERROR: cURL ошибка при запросе {$method}: {$error}");
        return false;
    }

    if ($httpCode !== 200) {
        botLog("ERROR: HTTP ошибка {$httpCode} при запросе {$method}");
        return false;
    }

    $response = json_decode($result, true);

    if (!$response) {
        botLog("ERROR: Неверный JSON ответ от Telegram API: " . substr($result, 0, 200));
        return false;
    }

    if (!$response['ok']) {
        $errorMsg = $response['description'] ?? 'Неизвестная ошибка';
        $errorCode = $response['error_code'] ?? 0;
        botLog("ERROR: Telegram API вернул ошибку {$errorCode}: {$errorMsg}");
        return false;
    }

    botLog("SUCCESS: Запрос {$method} выполнен успешно");
    return $response['result'];
}

/**
 * Отправка сообщения пользователю
 */
function sendMessage($chatId, $text, $replyMarkup = null) {
    $data = [
        'chat_id' => $chatId,
        'text' => $text,
        'parse_mode' => 'HTML'
    ];

    if ($replyMarkup) {
        $data['reply_markup'] = json_encode($replyMarkup);
    }

    return telegramRequest('sendMessage', $data);
}

/**
 * Отправка фото с сообщением
 */
function sendPhoto($chatId, $photo, $caption = '', $replyMarkup = null) {
    $data = [
        'chat_id' => $chatId,
        'photo' => $photo,
        'caption' => $caption,
        'parse_mode' => 'HTML'
    ];

    if ($replyMarkup) {
        $data['reply_markup'] = json_encode($replyMarkup);
    }

    return telegramRequest('sendPhoto', $data);
}

/**
 * Отправка фото файлом с сообщением
 */
function sendPhotoFile($chatId, $photoFile, $caption = '', $replyMarkup = null) {
    $url = TELEGRAM_API_URL . 'sendPhoto';

    $postData = [
        'chat_id' => $chatId,
        'photo' => $photoFile,
        'caption' => $caption,
        'parse_mode' => 'HTML'
    ];

    if ($replyMarkup) {
        $postData['reply_markup'] = json_encode($replyMarkup);
    }

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: multipart/form-data'
    ]);

    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($result === false || $httpCode !== 200) {
        botLog("ERROR: Не удалось отправить фото файлом к Telegram API");
        return false;
    }

    $response = json_decode($result, true);

    if (!$response['ok']) {
        botLog("ERROR: Telegram API вернул ошибку при отправке фото: " . json_encode($response));
        return false;
    }

    return $response['result'];
}

/**
 * Установка webhook
 */
function setWebhook() {
    $data = [
        'url' => WEBHOOK_URL,
        'allowed_updates' => ['message', 'callback_query']
    ];

    return telegramRequest('setWebhook', $data);
}

/**
 * Удаление webhook
 */
function deleteWebhook() {
    return telegramRequest('deleteWebhook');
}
?>
