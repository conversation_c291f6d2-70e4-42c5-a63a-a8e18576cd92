<?php
/**
 * Тест fallback механизма для получения истории выплат
 */

header('Content-Type: text/plain; charset=utf-8');

echo "=== ТЕСТ FALLBACK МЕХАНИЗМА ===\n\n";

// Тестируем fallback данные как в JavaScript
$testUserId = 5880288830;

$fallbackCases = [
    [
        'name' => 'Fallback с initDataUnsafe',
        'user' => [
            'id' => $testUserId,
            'first_name' => 'Альтер',
            'last_name' => 'Эго',
            'username' => 'alter_mega_ego',
            'language_code' => 'ru'
        ]
    ],
    [
        'name' => 'Fallback с минимальными данными',
        'user' => [
            'id' => $testUserId,
            'first_name' => 'User'
        ]
    ],
    [
        'name' => 'Fallback только с ID',
        'user' => [
            'id' => $testUserId
        ]
    ]
];

foreach ($fallbackCases as $i => $testCase) {
    echo ($i + 1) . ". ТЕСТ: " . $testCase['name'] . "\n";
    echo str_repeat("-", 50) . "\n";
    
    // Создаем fallback initData как в JavaScript
    $fakeInitData = "user=" . urlencode(json_encode($testCase['user'])) . "&auth_date=" . time() . "&hash=fallback";
    
    $requestData = ['initData' => $fakeInitData];
    $postData = json_encode($requestData);
    
    echo "Fallback initData: " . $fakeInitData . "\n";
    echo "Отправляемые данные: " . $postData . "\n";
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/json',
            'content' => $postData
        ]
    ]);
    
    try {
        $response = file_get_contents('http://argun-defolt.loc/api/getWithdrawalHistory.php', false, $context);
        
        if ($response === false) {
            echo "❌ Ошибка запроса\n";
        } else {
            echo "Ответ API: " . $response . "\n";
            
            $decoded = json_decode($response, true);
            if ($decoded) {
                if (isset($decoded['withdrawals'])) {
                    echo "✅ Количество выплат: " . count($decoded['withdrawals']) . "\n";
                    if (count($decoded['withdrawals']) > 0) {
                        echo "Первая выплата: " . $decoded['withdrawals'][0]['coins_amount'] . " монет (" . $decoded['withdrawals'][0]['status'] . ")\n";
                    }
                } elseif (isset($decoded['error'])) {
                    echo "❌ Ошибка: " . $decoded['error'] . "\n";
                }
            }
        }
        
    } catch (Exception $e) {
        echo "❌ Исключение: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

// Тестируем прямой вызов функции getUserWithdrawals
echo "=== ПРЯМОЙ ТЕСТ ФУНКЦИИ ===\n";
require_once __DIR__ . '/functions.php';

$withdrawals = getUserWithdrawals($testUserId);
echo "Функция getUserWithdrawals({$testUserId}) вернула: " . count($withdrawals) . " выплат\n";

if (count($withdrawals) > 0) {
    echo "Выплаты:\n";
    foreach ($withdrawals as $i => $withdrawal) {
        echo "  " . ($i + 1) . ". " . ($withdrawal['coins_amount'] ?? 'N/A') . " монет (" . ($withdrawal['status'] ?? 'N/A') . ") - " . ($withdrawal['currency'] ?? 'N/A') . "\n";
    }
}

echo "\n=== ТЕСТ ЗАВЕРШЕН ===\n";
?>
