<?php
/**
 * setup.php
 * Установка webhook для Telegram бота
 */

require_once __DIR__ . '/config.php';

echo "=== УСТАНОВКА TELEGRAM БОТА ===\n\n";

// Проверяем токен
echo "1. Проверка токена бота...\n";
$botInfo = telegramRequest('getMe');

if (!$botInfo) {
    echo "❌ ОШИБКА: Неверный токен бота!\n";
    echo "Проверьте токен в файле bot/config.php\n";
    exit;
}

echo "✅ Токен корректен!\n";
echo "   Имя бота: " . $botInfo['first_name'] . "\n";
echo "   Username: @" . $botInfo['username'] . "\n";
echo "   ID: " . $botInfo['id'] . "\n\n";

// Устанавливаем webhook
echo "2. Установка webhook...\n";
echo "   URL: " . WEBHOOK_URL . "\n";

$result = setWebhook();

if ($result) {
    echo "✅ Webhook установлен успешно!\n\n";
} else {
    echo "❌ ОШИБКА: Не удалось установить webhook!\n";
    echo "Проверьте:\n";
    echo "- URL доступен из интернета\n";
    echo "- SSL сертификат корректен\n";
    echo "- Нет ошибок в webhook.php\n\n";
    exit;
}

// Проверяем webhook
echo "3. Проверка webhook...\n";
$webhookInfo = telegramRequest('getWebhookInfo');

if ($webhookInfo) {
    echo "✅ Информация о webhook:\n";
    echo "   URL: " . ($webhookInfo['url'] ?? 'не установлен') . "\n";
    echo "   Статус: " . ($webhookInfo['has_custom_certificate'] ? 'с сертификатом' : 'без сертификата') . "\n";
    echo "   Ожидающих обновлений: " . ($webhookInfo['pending_update_count'] ?? 0) . "\n";
    
    if (isset($webhookInfo['last_error_date'])) {
        echo "   Последняя ошибка: " . date('Y-m-d H:i:s', $webhookInfo['last_error_date']) . "\n";
        echo "   Текст ошибки: " . ($webhookInfo['last_error_message'] ?? 'неизвестно') . "\n";
    }
    echo "\n";
}

// Устанавливаем команды бота
echo "4. Установка команд бота...\n";

$commands = [
    [
        'command' => 'start',
        'description' => '🚀 Запустить бота и открыть приложение'
    ],
    [
        'command' => 'balance',
        'description' => '💰 Показать текущий баланс'
    ],
    [
        'command' => 'stats',
        'description' => '📊 Показать статистику заработка'
    ],
    [
        'command' => 'help',
        'description' => '❓ Помощь по использованию'
    ]
];

$commandsResult = telegramRequest('setMyCommands', ['commands' => json_encode($commands)]);

if ($commandsResult) {
    echo "✅ Команды бота установлены!\n\n";
} else {
    echo "❌ Не удалось установить команды бота\n\n";
}

// Создаем файл логов
echo "5. Создание файла логов...\n";
if (!file_exists(LOG_FILE)) {
    file_put_contents(LOG_FILE, "");
    echo "✅ Файл логов создан: " . LOG_FILE . "\n\n";
} else {
    echo "✅ Файл логов уже существует: " . LOG_FILE . "\n\n";
}

echo "🎉 УСТАНОВКА ЗАВЕРШЕНА!\n\n";
echo "📋 ЧТО ДАЛЬШЕ:\n";
echo "1. Убедитесь, что webhook доступен по URL: " . WEBHOOK_URL . "\n";
echo "2. Отправьте боту сообщение /start в Telegram\n";
echo "3. Проверьте логи в файле: " . LOG_FILE . "\n";
echo "4. Настройте имя бота в config.php (BOT_USERNAME)\n\n";

echo "🔗 ССЫЛКА НА БОТА:\n";
echo "https://t.me/" . $botInfo['username'] . "\n\n";

echo "📝 ВАЖНО:\n";
echo "- Замените домен в WEBHOOK_URL и WEBAPP_URL на ваш\n";
echo "- Замените BOT_USERNAME на имя вашего бота\n";
echo "- Убедитесь, что SSL сертификат корректен\n";
echo "- Проверьте права доступа к файлам\n\n";

echo "=== ГОТОВО! ===\n";
?>
