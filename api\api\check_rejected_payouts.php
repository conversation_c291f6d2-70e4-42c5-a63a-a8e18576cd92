<?php
/**
 * Проверка отклонённых выплат и диагностика проблемы
 */

require_once 'config.php';
require_once 'NOWPaymentsAPI.php';

echo "🔍 ДИАГНОСТИКА ОТКЛОНЁННЫХ ВЫПЛАТ\n\n";

// Создаем экземпляр API
$api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);

// ID отклонённых выплат из скриншота
$rejectedPayouts = [
    '5003737622',
    '5003737455'
];

echo "📊 Проверяем статус отклонённых выплат:\n\n";

foreach ($rejectedPayouts as $payoutId) {
    echo "🔍 Проверяем выплату ID: {$payoutId}\n";
    
    $status = $api->getPayoutStatus($payoutId);
    
    if ($status) {
        echo "📋 Детали выплаты:\n";
        echo "- Статус: " . ($status['status'] ?? 'N/A') . "\n";
        echo "- Сумма: " . ($status['amount'] ?? 'N/A') . " " . ($status['currency'] ?? 'N/A') . "\n";
        echo "- Адрес: " . ($status['address'] ?? 'N/A') . "\n";
        echo "- Создана: " . ($status['created_at'] ?? 'N/A') . "\n";
        
        if (isset($status['error']) && $status['error']) {
            echo "❌ Ошибка: {$status['error']}\n";
        }
        
        if (isset($status['fee'])) {
            echo "💳 Комиссия: {$status['fee']}\n";
        }
        
        if (isset($status['fee_paid_by'])) {
            echo "💰 Комиссия оплачена: {$status['fee_paid_by']}\n";
        }
        
        echo "🔧 Полный ответ:\n";
        echo json_encode($status, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
    } else {
        echo "❌ Не удалось получить статус выплаты\n";
    }
    
    echo str_repeat("-", 50) . "\n\n";
}

echo "🏪 Проверяем настройки магазина:\n";

// Проверяем настройки аккаунта
$balance = $api->getAccountBalance();
if ($balance) {
    echo "💰 Баланс аккаунта:\n";
    foreach ($balance as $currency => $data) {
        if (is_array($data) && isset($data['amount'])) {
            echo "- {$currency}: {$data['amount']}\n";
        }
    }
} else {
    echo "❌ Не удалось получить баланс\n";
}

echo "\n🔧 Проверяем минимальные суммы:\n";

$currencies = ['usdttrc20', 'btc', 'eth', 'trx'];
foreach ($currencies as $currency) {
    $minAmount = $api->getMinWithdrawalAmount($currency);
    if ($minAmount) {
        echo "- {$currency}: минимум {$minAmount}\n";
    } else {
        echo "- {$currency}: не удалось получить минимум\n";
    }
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "🎯 АНАЛИЗ ПРОБЛЕМЫ\n";
echo str_repeat("=", 60) . "\n\n";

echo "❌ Статус 'Rejected_not_checked' означает:\n";
echo "1. Выплата была отклонена системой NOWPayments\n";
echo "2. Возможные причины:\n";
echo "   - Недостаточно средств для покрытия комиссии\n";
echo "   - Неправильные настройки магазина\n";
echo "   - Сумма меньше минимальной с учётом комиссии\n";
echo "   - Не включена опция 'Withdrawal fee paid by receiver'\n\n";

echo "🔧 РЕШЕНИЯ:\n\n";

echo "1️⃣ Проверить настройки магазина:\n";
echo "   - Войти: https://account.nowpayments.io/store-settings#details\n";
echo "   - Включить: 'Withdrawal fee paid by receiver'\n";
echo "   - Сохранить настройки\n\n";

echo "2️⃣ Проверить минимальные суммы:\n";
echo "   - Убедиться что сумма больше минимальной\n";
echo "   - Учесть комиссию при расчёте\n\n";

echo "3️⃣ Попробовать с большей суммой:\n";
echo "   - Тестировать с суммой 0.1 USDT вместо 0.01\n";
echo "   - Проверить результат\n\n";

echo "4️⃣ Использовать другой подход к комиссиям:\n";
echo "   - Добавить комиссию к сумме отправителя\n";
echo "   - Или использовать баланс магазина для покрытия комиссий\n\n";

echo "🚀 СЛЕДУЮЩИЕ ШАГИ:\n";
echo "1. Проверить настройки в панели NOWPayments\n";
echo "2. Протестировать с большей суммой\n";
echo "3. При необходимости изменить логику обработки комиссий\n";
echo "4. Повторить тест\n\n";

echo "💡 РЕКОМЕНДАЦИЯ:\n";
echo "Скорее всего нужно включить опцию 'Withdrawal fee paid by receiver'\n";
echo "в настройках магазина NOWPayments.\n";
?>
