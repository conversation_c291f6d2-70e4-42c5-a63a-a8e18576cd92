<?php
/**
 * Ручное обновление курсов валют (для тестирования)
 * Можно запускать через браузер: http://your-domain.com/api/manual_update_rates.php
 */

// Простая защита от случайного запуска
$secret = $_GET['secret'] ?? '';
if ($secret !== 'update_rates_2025') {
    http_response_code(403);
    die('Access denied. Use: ?secret=update_rates_2025');
}

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html>
<head>
    <title>Ручное обновление курсов</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { background: #f5f5f5; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <h1>🔄 Ручное обновление курсов валют</h1>
    
    <div class="log">
        <strong>Запуск обновления...</strong><br>
        <?php
        
        // Захватываем вывод cron-скрипта
        ob_start();
        
        try {
            require_once __DIR__ . '/cron_update_rates_simple.php';
        } catch (Exception $e) {
            echo "<span class='error'>❌ Ошибка: " . htmlspecialchars($e->getMessage()) . "</span><br>";
        }
        
        $output = ob_get_clean();
        
        // Форматируем вывод для HTML
        $lines = explode("\n", $output);
        foreach ($lines as $line) {
            if (empty(trim($line))) continue;
            
            $line = htmlspecialchars($line);
            
            if (strpos($line, '✅') !== false) {
                echo "<span class='success'>{$line}</span><br>";
            } elseif (strpos($line, '❌') !== false || strpos($line, 'ERROR') !== false) {
                echo "<span class='error'>{$line}</span><br>";
            } else {
                echo "<span class='info'>{$line}</span><br>";
            }
        }
        
        ?>
    </div>
    
    <h2>📊 Проверка результата</h2>
    <div class="log">
        <?php
        
        // Проверяем кеш после обновления
        require_once __DIR__ . '/currency_cache.php';
        
        $cache = loadCurrencyCache();
        if ($cache && isset($cache['currencies'])) {
            echo "<span class='success'>✅ Кеш загружен, валют: " . count($cache['currencies']) . "</span><br>";
            
            $lastUpdated = $cache['last_updated'] ?? 0;
            $cacheAge = time() - $lastUpdated;
            
            echo "<span class='info'>Последнее обновление: " . date('Y-m-d H:i:s', $lastUpdated) . "</span><br>";
            echo "<span class='info'>Возраст кеша: " . floor($cacheAge / 60) . " минут</span><br>";
            
            if ($cacheAge < 300) { // 5 минут
                echo "<span class='success'>✅ Кеш свежий</span><br>";
            } else {
                echo "<span class='error'>⚠️ Кеш устарел</span><br>";
            }
            
            // Показываем несколько курсов
            echo "<br><strong>Актуальные курсы:</strong><br>";
            $count = 0;
            foreach ($cache['currencies'] as $code => $data) {
                if ($count >= 5) break;
                $rate = $data['rate_usd'] ?? 'N/A';
                $updated = $data['last_updated'] ?? 0;
                $age = $updated > 0 ? floor((time() - $updated) / 60) : 'N/A';
                echo "<span class='info'>{$code}: \${$rate} (обновлен {$age} мин назад)</span><br>";
                $count++;
            }
            
        } else {
            echo "<span class='error'>❌ Ошибка загрузки кеша</span><br>";
        }
        
        ?>
    </div>
    
    <h2>🔄 Действия</h2>
    <p>
        <a href="?secret=update_rates_2025" style="background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;">🔄 Обновить снова</a>
        <a href="../test_complete_system.html" style="background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-left: 10px;">🧪 Запустить тесты</a>
    </p>
    
    <h2>⚠️ Важно</h2>
    <div class="log">
        <span class="error">Это временное решение для тестирования!</span><br>
        <span class="info">Для продакшена обязательно настройте cron на сервере.</span><br>
        <span class="info">Инструкции: CRON_SETUP_INSTRUCTIONS.md</span>
    </div>
    
</body>
</html>
