<?php
// Тест исправления проблемы с проверкой баланса

require_once 'config.php';
require_once 'db_mock.php';
require_once 'security.php';

echo "🔧 ТЕСТ ИСПРАВЛЕНИЯ ПРОБЛЕМЫ С БАЛАНСОМ\n";
echo "=====================================\n\n";

// Загружаем данные пользователей
$userData = loadUserData();

// Ищем пользователей с балансом около 3000 монет
echo "🔍 Поиск пользователей с балансом 2900-3100 монет:\n";
$foundUsers = [];
foreach ($userData as $userId => $user) {
    if (isset($user['balance']) && $user['balance'] >= 2900 && $user['balance'] <= 3100) {
        $foundUsers[] = $userId;
        echo "👤 Пользователь: {$userId}, Баланс: {$user['balance']} монет\n";
    }
}

if (empty($foundUsers)) {
    echo "❌ Пользователи с балансом 3000 монет не найдены\n";
    echo "Тестируем пользователя 5880288830 вместо этого...\n";
    $testUserId = 5880288830;
} else {
    $testUserId = $foundUsers[0]; // Берем первого найденного
    echo "✅ Выбран пользователь: {$testUserId}\n";
}

echo "\n";

if (!isset($userData[$testUserId])) {
    echo "❌ Пользователь {$testUserId} не найден\n";
    exit;
}

$user = $userData[$testUserId];
echo "👤 Тестируем пользователя: {$testUserId}\n";
echo "💰 Текущий баланс: {$user['balance']} монет\n";
echo "📊 Всего заработано: " . ($user['total_earned'] ?? 'не указано') . " монет\n";

// Подсчитываем выведенные средства
$totalWithdrawn = 0;
if (isset($user['withdrawals']) && is_array($user['withdrawals'])) {
    foreach ($user['withdrawals'] as $withdrawal) {
        if (isset($withdrawal['coins_amount'])) {
            $totalWithdrawn += $withdrawal['coins_amount'];
        }
    }
}
echo "💸 Всего выведено: {$totalWithdrawn} монет\n";

// Рассчитываем доступную сумму по старой логике
$availableOld = ($user['total_earned'] ?? 0) - $totalWithdrawn;
echo "🔍 Доступно по старой логике: {$availableOld} монет\n";

echo "\n--- ТЕСТИРОВАНИЕ ВЫВОДА 972 МОНЕТ ---\n";

$testAmount = 972; // Сумма из скриншота (0.97269625 TON × 1000)

// Проверяем старую логику (до исправления)
echo "🔴 Старая логика: ";
if ($testAmount > $availableOld) {
    echo "БЛОКИРОВКА - недостаточно средств ({$testAmount} > {$availableOld})\n";
} else {
    echo "РАЗРЕШЕНО\n";
}

// Проверяем новую логику (после исправления)
echo "🟢 Новая логика: ";

// Добавляем отладочную информацию
echo "\n   🔍 Отладка: Баланс пользователя: {$user['balance']}, Запрошено: {$testAmount}\n";
echo "   🔍 Достаточно средств: " . ($user['balance'] >= $testAmount ? 'ДА' : 'НЕТ') . "\n";

$balanceCheckResult = verifyBalance($testUserId, $testAmount, $userData);
if ($balanceCheckResult) {
    echo "   ✅ РАЗРЕШЕНО - проверка баланса пройдена\n";
} else {
    echo "   ❌ БЛОКИРОВКА - проверка баланса не пройдена\n";
}

echo "\n--- ДОПОЛНИТЕЛЬНЫЕ ПРОВЕРКИ ---\n";

// Проверяем минимальный баланс для вывода
echo "🎯 Минимальный баланс для вывода: " . MIN_BALANCE_FOR_WITHDRAWAL . " монет\n";
if ($user['balance'] >= MIN_BALANCE_FOR_WITHDRAWAL) {
    echo "✅ Минимальный баланс достигнут\n";
} else {
    echo "❌ Минимальный баланс НЕ достигнут\n";
}

// Проверяем лимит выводов
echo "📅 Проверка лимита выводов: ";

// Показываем детали лимита
$currentTime = time();
$dayAgo = $currentTime - 86400;
$withdrawalLog = $userData[$testUserId]['withdrawal_log'] ?? [];
$dayWithdrawals = array_filter($withdrawalLog, function($timestamp) use ($dayAgo) {
    return $timestamp >= $dayAgo;
});

echo "\n   📊 Выводов за последние 24 часа: " . count($dayWithdrawals) . " из " . MAX_WITHDRAWALS_PER_DAY . "\n";
if (!empty($dayWithdrawals)) {
    foreach ($dayWithdrawals as $timestamp) {
        echo "   🕐 " . date('Y-m-d H:i:s', $timestamp) . "\n";
    }
}

$limitCheckResult = checkWithdrawalLimit($testUserId, $userData);
if ($limitCheckResult) {
    echo "   ✅ Лимит не превышен\n";
} else {
    echo "   ❌ Лимит превышен\n";
}

// Проверяем подозрительную активность
echo "🔒 Проверка подозрительной активности: ";
$suspiciousCheckResult = checkSuspiciousActivity($testUserId, $userData);
if ($suspiciousCheckResult) {
    echo "✅ Активность нормальная\n";
} else {
    echo "❌ Подозрительная активность\n";
}

echo "\n🎉 ИТОГ: ";
if ($balanceCheckResult && $user['balance'] >= MIN_BALANCE_FOR_WITHDRAWAL && $limitCheckResult && $suspiciousCheckResult) {
    echo "ВСЕ ПРОВЕРКИ ПРОЙДЕНЫ - ВЫВОД ДОЛЖЕН РАБОТАТЬ!\n";
} else {
    echo "ЕСТЬ ПРОБЛЕМЫ - ВЫВОД ЗАБЛОКИРОВАН\n";
}

echo "\n✨ Тест завершен\n";
?>
