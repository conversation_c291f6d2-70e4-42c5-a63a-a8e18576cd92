<?php
/**
 * API для получения минимальных сумм с учетом комиссий
 * Возвращает правильные минимумы для каждой валюты
 */

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/NOWPaymentsAPI.php';
require_once __DIR__ . '/FeeCalculator.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

try {
    // ИСПРАВЛЕНО: Создаем только FeeCalculator, НЕ делаем запросы к NOWPayments API
    $calculator = new FeeCalculator(null); // Передаем null, так как используем кэшированные данные

    // Список поддерживаемых валют
    $currencies = ['ton', 'eth', 'btc', 'usdttrc20'];

    $result = [
        'success' => true,
        'currencies' => [],
        'calculated_at' => date('Y-m-d H:i:s'),
        'note' => 'Минимумы рассчитаны с учетом комиссий из кэшированных данных (БЕЗ запросов к серверу)'
    ];

    foreach ($currencies as $currency) {
        error_log("getMinimumCoinsWithFees: Расчет для {$currency} из кэша");

        $minData = $calculator->getMinimumCoinsForCurrency($currency);

        if ($minData && isset($minData['minCoins'])) {
            $result['currencies'][$currency] = [
                'currency' => strtoupper($currency),
                'min_crypto_required' => $minData['min_crypto_required'] ?? null,
                'estimated_fee' => $minData['estimated_fee'] ?? null,
                'min_coins_with_fees' => $minData['min_coins_with_fees'] ?? $minData['minCoins'],
                'calculation_method' => $minData['calculation_method'] ?? 'cached_data',
                'safety_multiplier' => $minData['safety_multiplier'] ?? null,
                // Для совместимости с существующим кодом
                'minCoins' => $minData['minCoins']
            ];

            error_log("getMinimumCoinsWithFees: {$currency} - минимум {$minData['minCoins']} монет (из кэша)");
        } else {
            // Fallback значения
            $fallbackMinCoins = [
                'ton' => 3000,
                'eth' => 5000,
                'btc' => 10000,
                'usdttrc20' => 2000
            ];

            $result['currencies'][$currency] = [
                'currency' => strtoupper($currency),
                'min_crypto_required' => null,
                'estimated_fee' => null,
                'min_coins_with_fees' => $fallbackMinCoins[$currency] ?? 5000,
                'calculation_method' => 'fallback_safe',
                'minCoins' => $fallbackMinCoins[$currency] ?? 5000,
                'error' => 'Не удалось рассчитать из кэша, используем безопасный fallback'
            ];

            error_log("getMinimumCoinsWithFees: {$currency} - используем fallback {$fallbackMinCoins[$currency]} монет");
        }
    }

    echo json_encode($result);
    
} catch (Exception $e) {
    error_log("getMinimumCoinsWithFees ERROR: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'fallback_minimums' => [
            'ton' => ['minCoins' => 3000],
            'eth' => ['minCoins' => 5000], 
            'btc' => ['minCoins' => 10000],
            'usdttrc20' => ['minCoins' => 2000]
        ]
    ]);
}
?>
