<?php
/**
 * api/admin/apply_design.php
 * API для применения настроек дизайна в реальном времени
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Обработка preflight запросов
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Файл для хранения настроек дизайна
$designSettingsFile = __DIR__ . '/../../design_settings.json';

// Настройки дизайна по умолчанию
$defaultDesignSettings = [
    'colors' => [
        'primary_dark' => '#2a2a2a',
        'accent_orange' => '#ff6b35',
        'accent_neon' => '#00ffff',
        'accent_green' => '#39ff14',
        'bg_card' => '#3a3a3a',
        'bg_secondary' => '#333333',
        'text_primary' => '#ffffff',
        'text_secondary' => '#cccccc',
        'border_color' => '#4a4a4a',
        'cyber_border' => '#00ffff',
        'shadow_orange' => '#ff6b35',
        'shadow_neon' => '#00ffff'
    ],
    'effects' => [
        'crypto_opacity' => 0.6,
        'crypto_size' => 100,
        'crypto_speed' => 15,
        'crypto_amplitude' => 80,
        'crypto_glow_intensity' => 0.4,
        'crypto_blur' => 2,
        'enable_crypto_icons' => true,
        'enable_glitch_effects' => false,
        'enable_glow_effects' => true,
        'enable_animations' => true,
        'background_blur' => 2,
        'button_glow' => true,
        'card_borders' => true,
        'text_shadows' => true
    ],
    'layout' => [
        'border_radius' => 12,
        'button_radius' => 8,
        'card_padding' => 20,
        'spacing' => 16
    ],
    'theme' => 'cyberpunk'
];

// Загружаем текущие настройки
function loadDesignSettings($file, $defaults) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        $settings = json_decode($content, true);
        if ($settings) {
            return array_merge_recursive($defaults, $settings);
        }
    }
    return $defaults;
}

try {
    $settings = loadDesignSettings($designSettingsFile, $defaultDesignSettings);
    
    // Возвращаем настройки в формате JSON
    echo json_encode([
        'success' => true,
        'settings' => $settings
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'settings' => $defaultDesignSettings
    ]);
}
?>
