<?php
/**
 * api/admin/check_payout_status.php
 * AJAX endpoint для проверки статуса выплаты в NOWPayments
 */

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../error.log');
error_reporting(E_ALL);

// Подключаем файл с функциями аутентификации и авторизации
require_once __DIR__ . '/auth.php';

// Проверяем аутентификацию
session_start();
if (!isAuthenticated()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Не авторизован']);
    exit;
}

// Проверяем метод запроса
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Метод не разрешен']);
    exit;
}

// --- Подключение зависимостей с проверкой ---
if (!(@require_once __DIR__ . '/../config.php')) { 
    http_response_code(500); 
    echo json_encode(['success' => false, 'message' => 'Ошибка загрузки config.php']);
    exit;
}
if (!(@require_once __DIR__ . '/../NOWPaymentsAPI.php')) { 
    http_response_code(500); 
    echo json_encode(['success' => false, 'message' => 'Ошибка загрузки NOWPaymentsAPI.php']);
    exit;
}
// --- Конец проверки зависимостей ---

// Получаем данные запроса
$input = file_get_contents('php://input');
$data = json_decode($input, true);

if (!$data || !isset($data['payout_id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Не указан ID выплаты']);
    exit;
}

$payoutId = $data['payout_id'];

// Валидация ID выплаты
if (!is_string($payoutId) || empty(trim($payoutId))) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Некорректный ID выплаты']);
    exit;
}

try {
    // Создаем экземпляр API
    $api = new NOWPaymentsAPI(
        NOWPAYMENTS_API_KEY, 
        NOWPAYMENTS_PUBLIC_KEY, 
        NOWPAYMENTS_IPN_SECRET, 
        NOWPAYMENTS_API_URL
    );
    
    // Проверяем статус выплаты
    $payoutStatus = $api->getPayoutStatus($payoutId);
    
    if ($payoutStatus === false) {
        echo json_encode([
            'success' => false, 
            'message' => 'Не удалось получить статус выплаты. Возможно, ID неверный или выплата не найдена.'
        ]);
        exit;
    }
    
    // Если есть ошибка в ответе API
    if (isset($payoutStatus['error']) && $payoutStatus['error']) {
        echo json_encode([
            'success' => false, 
            'message' => $payoutStatus['message'] ?? 'Ошибка API NOWPayments',
            'api_error' => $payoutStatus
        ]);
        exit;
    }
    
    // Успешный ответ
    echo json_encode([
        'success' => true,
        'payout' => $payoutStatus,
        'checked_at' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    error_log("check_payout_status ERROR: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => 'Внутренняя ошибка сервера: ' . $e->getMessage()
    ]);
}
?>
