    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Скрипты для админки -->
    <script>
    // Функция для копирования реферальной ссылки в админке
    function copyReferralLinkAdmin(userId) {
        const linkInput = document.getElementById('referralLink' + userId);
        if (!linkInput) {
            alert('Ошибка: Не найден элемент ссылки');
            return;
        }

        const textToCopy = linkInput.value;

        // Пытаемся использовать современный API
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(textToCopy).then(() => {
                showCopySuccess(userId);
            }).catch((err) => {
                console.warn('Ошибка clipboard API:', err);
                fallbackCopy(linkInput, userId);
            });
        } else {
            // Fallback для старых браузеров
            fallbackCopy(linkInput, userId);
        }
    }

    // Fallback метод копирования
    function fallbackCopy(inputElement, userId) {
        try {
            inputElement.select();
            inputElement.setSelectionRange(0, 99999); // Для мобильных устройств
            document.execCommand('copy');
            showCopySuccess(userId);
        } catch (err) {
            console.error('Ошибка fallback копирования:', err);
            alert('Не удалось скопировать ссылку. Скопируйте вручную.');
        }
    }

    // Показать уведомление об успешном копировании
    function showCopySuccess(userId) {
        const button = document.querySelector(`button[onclick="copyReferralLinkAdmin('${userId}')"]`);
        if (button) {
            const originalHTML = button.innerHTML;
            button.innerHTML = '<i class="bi bi-check"></i>';
            button.classList.remove('btn-outline-secondary');
            button.classList.add('btn-success');

            setTimeout(() => {
                button.innerHTML = originalHTML;
                button.classList.remove('btn-success');
                button.classList.add('btn-outline-secondary');
            }, 2000);
        }

        // Показываем временное уведомление
        const notification = document.createElement('div');
        notification.className = 'alert alert-success alert-dismissible fade show position-fixed';
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            <i class="bi bi-check-circle"></i> Реферальная ссылка скопирована!
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // Автоматически удаляем уведомление через 3 секунды
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 3000);
    }

    // Функции для массовых действий с пользователями
    function toggleSelectAll() {
        const selectAllCheckbox = document.getElementById('selectAll');
        const userCheckboxes = document.querySelectorAll('.user-checkbox');

        userCheckboxes.forEach(checkbox => {
            checkbox.checked = selectAllCheckbox.checked;
        });

        updateMassActions();
    }

    function updateMassActions() {
        const userCheckboxes = document.querySelectorAll('.user-checkbox:checked');
        const selectedCount = userCheckboxes.length;
        const massActionsCard = document.getElementById('massActionsCard');
        const selectedCountSpan = document.getElementById('selectedCount');

        if (selectedCount > 0) {
            massActionsCard.style.display = 'block';
            selectedCountSpan.textContent = selectedCount;
        } else {
            massActionsCard.style.display = 'none';
        }

        // Обновляем состояние "Выбрать все"
        const allCheckboxes = document.querySelectorAll('.user-checkbox');
        const selectAllCheckbox = document.getElementById('selectAll');

        if (selectedCount === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        } else if (selectedCount === allCheckboxes.length) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = true;
        } else {
            selectAllCheckbox.indeterminate = true;
        }
    }

    function getSelectedUserIds() {
        const userCheckboxes = document.querySelectorAll('.user-checkbox:checked');
        return Array.from(userCheckboxes).map(checkbox => checkbox.value);
    }

    function massAction(action) {
        const selectedIds = getSelectedUserIds();

        if (selectedIds.length === 0) {
            alert('Выберите пользователей для выполнения действия');
            return;
        }

        let confirmMessage = '';
        let actionUrl = 'users.php';

        switch (action) {
            case 'block':
                confirmMessage = `Заблокировать ${selectedIds.length} пользователей?`;
                break;
            case 'unblock':
                confirmMessage = `Разблокировать ${selectedIds.length} пользователей?`;
                break;
            case 'reset_suspicious':
                confirmMessage = `Сбросить подозрительную активность для ${selectedIds.length} пользователей?`;
                break;
            case 'delete':
                confirmMessage = `ВНИМАНИЕ! Удалить ${selectedIds.length} пользователей? Это действие нельзя отменить!`;
                break;
            case 'export':
                // Экспорт не требует подтверждения
                exportUsers(selectedIds);
                return;
            default:
                alert('Неизвестное действие');
                return;
        }

        if (confirm(confirmMessage)) {
            // Создаем форму для отправки данных
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = actionUrl;

            // Добавляем действие
            const actionInput = document.createElement('input');
            actionInput.type = 'hidden';
            actionInput.name = 'mass_action';
            actionInput.value = action;
            form.appendChild(actionInput);

            // Добавляем ID пользователей
            selectedIds.forEach(id => {
                const idInput = document.createElement('input');
                idInput.type = 'hidden';
                idInput.name = 'user_ids[]';
                idInput.value = id;
                form.appendChild(idInput);
            });

            document.body.appendChild(form);
            form.submit();
        }
    }

    function exportUsers(userIds) {
        // Создаем форму для экспорта
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'export_users.php';
        form.target = '_blank'; // Открываем в новой вкладке

        // Добавляем ID пользователей
        userIds.forEach(id => {
            const idInput = document.createElement('input');
            idInput.type = 'hidden';
            idInput.name = 'user_ids[]';
            idInput.value = id;
            form.appendChild(idInput);
        });

        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);
    }
    </script>

</body>
</html>
