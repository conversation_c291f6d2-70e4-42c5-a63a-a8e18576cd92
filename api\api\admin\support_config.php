<?php
/**
 * api/admin/support_config.php
 * Конфигурация бота поддержки
 */

// Токен бота поддержки @uniqpaid_support_bot
define('SUPPORT_BOT_TOKEN', '7820736321:AAFxt4VAh3qF5uY3sRMb6pKLxkNWAt5zE4M');

// URL для webhook бота поддержки
define('SUPPORT_WEBHOOK_URL', 'https://app.uniqpaid.com/test3/api/admin/support_webhook.php');

// Имя бота поддержки
define('SUPPORT_BOT_USERNAME', 'uniqpaid_support_bot');

// API Telegram для бота поддержки
define('SUPPORT_TELEGRAM_API_URL', 'https://api.telegram.org/bot' . SUPPORT_BOT_TOKEN . '/');

// Файлы для хранения данных поддержки
define('SUPPORT_CHATS_FILE', __DIR__ . '/support_chats.json');
define('SUPPORT_MESSAGES_FILE', __DIR__ . '/support_messages.json');

// Логирование бота поддержки
define('SUPPORT_LOG_FILE', __DIR__ . '/support_bot.log');

/**
 * Логирование сообщений бота поддержки
 */
function supportBotLog($message) {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] {$message}\n";
    file_put_contents(SUPPORT_LOG_FILE, $logMessage, FILE_APPEND | LOCK_EX);
}

/**
 * Отправка запроса к Telegram API для бота поддержки
 */
function supportTelegramRequest($method, $data = []) {
    $url = SUPPORT_TELEGRAM_API_URL . $method;

    supportBotLog("INFO: Отправка запроса к Telegram API: {$method}");

    $options = [
        'http' => [
            'header' => "Content-type: application/x-www-form-urlencoded\r\n",
            'method' => 'POST',
            'content' => http_build_query($data)
        ]
    ];

    $context = stream_context_create($options);
    $result = file_get_contents($url, false, $context);

    if ($result === false) {
        supportBotLog("ERROR: Ошибка при отправке запроса к Telegram API");
        return false;
    }

    $response = json_decode($result, true);
    
    if (!$response['ok']) {
        supportBotLog("ERROR: Telegram API вернул ошибку: " . $response['description']);
        return false;
    }

    supportBotLog("SUCCESS: Запрос к Telegram API выполнен успешно");
    return $response;
}

/**
 * Отправка сообщения через бота поддержки
 */
function sendSupportMessage($chatId, $text, $replyMarkup = null) {
    $data = [
        'chat_id' => $chatId,
        'text' => $text,
        'parse_mode' => 'HTML'
    ];

    if ($replyMarkup) {
        $data['reply_markup'] = json_encode($replyMarkup);
    }

    return supportTelegramRequest('sendMessage', $data);
}
?>
