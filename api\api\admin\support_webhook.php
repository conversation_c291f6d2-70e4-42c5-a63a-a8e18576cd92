<?php
/**
 * api/admin/support_webhook.php
 * Webhook для обработки сообщений бота поддержки
 */

// Логируем сразу что webhook вызван
$timestamp = date('Y-m-d H:i:s');
$logFile = __DIR__ . '/support_bot.log';
file_put_contents($logFile, "[{$timestamp}] WEBHOOK CALLED\n", FILE_APPEND | LOCK_EX);

require_once __DIR__ . '/support_config.php';
require_once __DIR__ . '/support_data.php';

// Логируем все входящие данные
$method = $_SERVER['REQUEST_METHOD'];
$input = file_get_contents('php://input');
supportBotLog("INFO: Webhook вызван, метод: {$method}");
supportBotLog("INFO: Входящие данные: " . $input);

// Получаем данные от Telegram
$update = json_decode($input, true);

if (!$update) {
    supportBotLog("ERROR: Не удалось декодировать JSON от Telegram. Входящие данные: " . $input);
    http_response_code(400);
    exit;
}

supportBotLog("INFO: Получен update: " . json_encode($update, JSON_UNESCAPED_UNICODE));

// Обрабатываем сообщение
if (isset($update['message'])) {
    handleMessage($update['message']);
}

// Обрабатываем callback query
if (isset($update['callback_query'])) {
    handleCallbackQuery($update['callback_query']);
}

/**
 * Обработка входящих сообщений
 */
function handleMessage($message) {
    $chatId = $message['chat']['id'];
    $userId = $message['from']['id'];
    $text = $message['text'] ?? '';
    $messageId = $message['message_id'];

    $username = $message['from']['username'] ?? null;
    $firstName = $message['from']['first_name'] ?? 'Пользователь';
    $lastName = $message['from']['last_name'] ?? null;
    $telegramUser = $message['from'];

    supportBotLog("INFO: Сообщение от пользователя {$userId} ({$firstName}): {$text}");

    // Обрабатываем команду /start
    if (strpos($text, '/start') === 0) {
        handleStartCommand($chatId, $userId, $firstName, $telegramUser);
        return;
    }

    // Определяем язык пользователя
    $language = getUserLanguage($telegramUser);

    // Создаем или находим чат поддержки
    $supportChatId = findOrCreateSupportChat($userId, $username, $firstName, $lastName, $language);

    // Сохраняем сообщение
    addSupportMessage($supportChatId, $messageId, true, $text);

    // Отправляем подтверждение пользователю на его языке
    $confirmText = getSupportText('message_received', $language);
    sendSupportMessage($chatId, $confirmText);

    supportBotLog("INFO: Сообщение сохранено в чат {$supportChatId}, язык: {$language}");
}

/**
 * Обработка callback query
 */
function handleCallbackQuery($callbackQuery) {
    $chatId = $callbackQuery['message']['chat']['id'];
    $userId = $callbackQuery['from']['id'];
    $data = $callbackQuery['data'];
    $telegramUser = $callbackQuery['from'];
    $language = getUserLanguage($telegramUser);

    supportBotLog("INFO: Callback query от пользователя {$userId}: {$data}");

    switch ($data) {
        case 'faq':
            handleFAQ($chatId, $language);
            break;

        case 'withdrawal_help':
            handleWithdrawalHelp($chatId, $language);
            break;

        case 'back_to_main':
            $firstName = $telegramUser['first_name'] ?? 'Пользователь';
            handleStartCommand($chatId, $userId, $firstName, $telegramUser);
            break;

        default:
            // Отвечаем на неизвестный callback
            supportTelegramRequest('answerCallbackQuery', [
                'callback_query_id' => $callbackQuery['id'],
                'text' => getSupportText('unknown_action', $language)
            ]);
            return;
    }

    // Отвечаем на callback query
    supportTelegramRequest('answerCallbackQuery', [
        'callback_query_id' => $callbackQuery['id'],
        'text' => getSupportText('processed', $language)
    ]);
}

/**
 * Определение языка пользователя
 */
function getUserLanguage($telegramUser) {
    $languageCode = $telegramUser['language_code'] ?? 'en';

    // Русский язык для русскоязычных стран
    $russianLanguages = ['ru', 'be', 'kk', 'ky', 'uz', 'uk'];

    return in_array($languageCode, $russianLanguages) ? 'ru' : 'en';
}

/**
 * Получение текста на нужном языке
 */
function getSupportText($key, $language) {
    $texts = [
        'welcome_title' => [
            'ru' => "Добро пожаловать в службу поддержки UniQPaid! 👋",
            'en' => "Welcome to UniQPaid Support Service! 👋"
        ],
        'operator_message' => [
            'ru' => "🎧 Вам ответит первый освободившийся оператор поддержки!\n\n",
            'en' => "🎧 The first available support operator will respond to you!\n\n"
        ],
        'help_options' => [
            'ru' => "Здесь вы можете:\n• Задать вопрос о работе приложения\n• Сообщить о проблеме\n• Получить помощь с выводом средств\n\n",
            'en' => "Here you can:\n• Ask questions about the app\n• Report a problem\n• Get help with withdrawals\n\n"
        ],
        'write_question' => [
            'ru' => "Просто напишите ваш вопрос, и мы обязательно вам поможем!",
            'en' => "Just write your question and we will definitely help you!"
        ],
        'faq_button' => [
            'ru' => "❓ Часто задаваемые вопросы",
            'en' => "❓ Frequently Asked Questions"
        ],
        'withdrawal_help_button' => [
            'ru' => "💰 Помощь с выводом",
            'en' => "💰 Withdrawal Help"
        ],
        'main_app_button' => [
            'ru' => "🔗 Перейти в основное приложение",
            'en' => "🔗 Go to Main App"
        ],
        'message_received' => [
            'ru' => "✅ Ваше сообщение получено!\n\n🎧 Первый освободившийся оператор поддержки ответит вам в ближайшее время.",
            'en' => "✅ Your message has been received!\n\n🎧 The first available support operator will respond to you shortly."
        ],
        'processed' => [
            'ru' => "Обработано",
            'en' => "Processed"
        ],
        'unknown_action' => [
            'ru' => "Неизвестное действие",
            'en' => "Unknown action"
        ],
        'faq_title' => [
            'ru' => "❓ Часто задаваемые вопросы",
            'en' => "❓ Frequently Asked Questions"
        ],
        'faq_content' => [
            'ru' => "🔸 <b>Как заработать монеты?</b>\nПросматривайте рекламу в приложении. За каждый просмотр вы получаете монеты.\n\n🔸 <b>Как вывести средства?</b>\nПерейдите в раздел 'Вывод' в приложении. Минимальная сумма для вывода - 100 монет.\n\n🔸 <b>Какие криптовалюты поддерживаются?</b>\nBTC, ETH, USDT, TON\n\n🔸 <b>Сколько времени занимает вывод?</b>\nОбычно 10-30 минут после подтверждения.\n\n🔸 <b>Есть ли лимиты на просмотр рекламы?</b>\nДа, 20 просмотров в день.",
            'en' => "🔸 <b>How to earn coins?</b>\nWatch ads in the app. You get coins for each view.\n\n🔸 <b>How to withdraw funds?</b>\nGo to 'Withdrawal' section in the app. Minimum withdrawal amount is 100 coins.\n\n🔸 <b>Which cryptocurrencies are supported?</b>\nBTC, ETH, USDT, TON\n\n🔸 <b>How long does withdrawal take?</b>\nUsually 10-30 minutes after confirmation.\n\n🔸 <b>Are there limits on ad viewing?</b>\nYes, 20 views per day."
        ],
        'withdrawal_help_title' => [
            'ru' => "💰 Помощь с выводом средств",
            'en' => "💰 Withdrawal Help"
        ],
        'withdrawal_help_content' => [
            'ru' => "💳 <b>Пошаговая инструкция по выводу:</b>\n\n1️⃣ Накопите минимум 100 монет\n2️⃣ Откройте раздел 'Вывод' в приложении\n3️⃣ Выберите криптовалюту (BTC, ETH, USDT, TON)\n4️⃣ Введите адрес вашего кошелька\n5️⃣ Укажите сумму для вывода\n6️⃣ Подтвердите операцию\n\n⚠️ <b>Важно:</b>\n• Проверьте правильность адреса кошелька\n• Учитывайте комиссии сети\n• Вывод обрабатывается автоматически\n\n❓ Если возникли проблемы, напишите нам!",
            'en' => "💳 <b>Step-by-step withdrawal guide:</b>\n\n1️⃣ Accumulate at least 100 coins\n2️⃣ Open 'Withdrawal' section in the app\n3️⃣ Choose cryptocurrency (BTC, ETH, USDT, TON)\n4️⃣ Enter your wallet address\n5️⃣ Specify withdrawal amount\n6️⃣ Confirm the operation\n\n⚠️ <b>Important:</b>\n• Check wallet address carefully\n• Consider network fees\n• Withdrawal is processed automatically\n\n❓ If you have problems, write to us!"
        ],
        'back_button' => [
            'ru' => "⬅️ Назад в главное меню",
            'en' => "⬅️ Back to Main Menu"
        ]
    ];

    return $texts[$key][$language] ?? $texts[$key]['en'] ?? '';
}

/**
 * Обработка команды /start
 */
function handleStartCommand($chatId, $userId, $firstName, $telegramUser) {
    $language = getUserLanguage($telegramUser);

    // СНАЧАЛА отправляем сообщение о том, что ответит оператор
    $operatorMessage = getSupportText('operator_message', $language);
    sendSupportMessage($chatId, $operatorMessage);

    // Небольшая пауза
    sleep(1);

    // ПОТОМ отправляем основное приветствие
    $welcomeText = getSupportText('welcome_title', $language) . "\n\n";
    $welcomeText .= getSupportText('help_options', $language);
    $welcomeText .= getSupportText('write_question', $language);

    $keyboard = [
        'inline_keyboard' => [
            [
                ['text' => getSupportText('faq_button', $language), 'callback_data' => 'faq'],
                ['text' => getSupportText('withdrawal_help_button', $language), 'callback_data' => 'withdrawal_help']
            ],
            [
                ['text' => getSupportText('main_app_button', $language), 'url' => 'https://t.me/uniqpaid_paid_bot']
            ]
        ]
    ];

    sendSupportMessage($chatId, $welcomeText, $keyboard);

    // Создаем чат поддержки при первом обращении
    findOrCreateSupportChat($userId, null, $firstName, null, $language);

    supportBotLog("INFO: Отправлено приветствие пользователю {$userId} на языке {$language}");
}

/**
 * Обработка FAQ
 */
function handleFAQ($chatId, $language) {
    $text = getSupportText('faq_title', $language) . "\n\n";
    $text .= getSupportText('faq_content', $language);

    $keyboard = [
        'inline_keyboard' => [
            [
                ['text' => getSupportText('back_button', $language), 'callback_data' => 'back_to_main']
            ]
        ]
    ];

    sendSupportMessage($chatId, $text, $keyboard);
    supportBotLog("INFO: Отправлен FAQ на языке {$language}");
}

/**
 * Обработка помощи с выводом
 */
function handleWithdrawalHelp($chatId, $language) {
    $text = getSupportText('withdrawal_help_title', $language) . "\n\n";
    $text .= getSupportText('withdrawal_help_content', $language);

    $keyboard = [
        'inline_keyboard' => [
            [
                ['text' => getSupportText('back_button', $language), 'callback_data' => 'back_to_main']
            ]
        ]
    ];

    sendSupportMessage($chatId, $text, $keyboard);
    supportBotLog("INFO: Отправлена помощь с выводом на языке {$language}");
}

http_response_code(200);
echo "OK";
?>
