<?php
/**
 * api/admin/notifications.php
 * Страница управления уведомлениями пользователей
 */

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../error.log');
error_reporting(E_ALL);

// Подключаем файл с функциями аутентификации и авторизации
require_once __DIR__ . '/auth.php';

// Проверяем аутентификацию
session_start();
if (!isAuthenticated()) {
    header('Location: login.php');
    exit;
}

// Подключение зависимостей
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../functions.php';

// Функция получения настроек уведомлений из JSON
function getNotificationSettings() {
    $settingsFile = __DIR__ . '/../cron/notification_settings.json';

    // Настройки по умолчанию
    $defaultSettings = [
        'notifications_enabled' => ['value' => '1', 'enabled' => true],
        'message_template' => [
            'value' => 'Привет, {first_name}! 👋\n\n🎉 Добро пожаловать в UniQPaid!\n\n💰 Для вас сегодня доступна реклама в приложении - зарабатывайте монеты за просмотры!\n\n🚀 Начните прямо сейчас: @uniqpaid_paid_bot\n\n💎 Каждый просмотр = монеты на ваш баланс!',
            'enabled' => true
        ],
        'bot_username' => ['value' => 'uniqpaid_paid_bot', 'enabled' => true],
        'inactive_hours' => ['value' => '24', 'enabled' => true]
    ];

    if (!file_exists($settingsFile)) {
        return $defaultSettings;
    }

    $content = file_get_contents($settingsFile);
    $settings = json_decode($content, true);

    if ($settings === null) {
        return $defaultSettings;
    }

    return $settings;
}

// Функция получения статистики уведомлений из JSON
function getNotificationStats() {
    $logsFile = __DIR__ . '/../cron/notification_logs.json';
    $stats = [
        'total_sent' => 0,
        'today_sent' => 0,
        'week_sent' => 0,
        'failed_sent' => 0,
        'blocked_users' => 0
    ];

    if (!file_exists($logsFile)) {
        return $stats;
    }

    $content = file_get_contents($logsFile);
    $logs = json_decode($content, true);

    if (!$logs || !is_array($logs)) {
        return $stats;
    }

    $currentTime = time();
    $todayStart = strtotime('today');
    $weekAgo = $currentTime - (7 * 24 * 3600);

    foreach ($logs as $log) {
        $stats['total_sent']++;

        $sentAt = $log['sent_at'] ?? 0;

        if ($sentAt >= $todayStart) {
            $stats['today_sent']++;
        }

        if ($sentAt >= $weekAgo) {
            $stats['week_sent']++;
        }

        if (isset($log['status'])) {
            if ($log['status'] === 'failed') {
                $stats['failed_sent']++;
            } elseif ($log['status'] === 'blocked') {
                $stats['blocked_users']++;
            }
        }
    }

    return $stats;
}

// Получаем текущие настройки
$settings = getNotificationSettings();
$stats = getNotificationStats();

// Значения по умолчанию
$notificationsEnabled = isset($settings['notifications_enabled']) ? $settings['notifications_enabled']['enabled'] : true;
$messageTemplate = isset($settings['message_template']) ? $settings['message_template']['value'] : 
    'Привет, {first_name}! 👋\n\n🎉 Добро пожаловать в UniQPaid!\n\n💰 Для вас сегодня доступна реклама в приложении - зарабатывайте монеты за просмотры!\n\n🚀 Начните прямо сейчас: @uniqpaid_paid_bot\n\n💎 Каждый просмотр = монеты на ваш баланс!';
$botUsername = isset($settings['bot_username']) ? $settings['bot_username']['value'] : 'uniqpaid_paid_bot';
$inactiveHours = isset($settings['inactive_hours']) ? $settings['inactive_hours']['value'] : '24';

// Подключаем шаблон заголовка
include 'templates/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'templates/sidebar.php'; ?>

        <!-- Основное содержимое -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="bi bi-bell me-2"></i>
                    Уведомления пользователей
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                            <i class="bi bi-arrow-clockwise"></i> Обновить
                        </button>
                        <button type="button" class="btn btn-sm btn-info" onclick="testNotification()">
                            <i class="bi bi-send"></i> Тестовая отправка
                        </button>
                    </div>
                </div>
            </div>

            <!-- Статистика уведомлений -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        Всего отправлено
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($stats['total_sent']); ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-envelope-check text-primary" style="font-size: 2rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        Сегодня отправлено
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($stats['today_sent']); ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-calendar-check text-success" style="font-size: 2rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        За неделю
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($stats['week_sent']); ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-graph-up text-info" style="font-size: 2rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        Неудачные / Заблокированные
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($stats['failed_sent']); ?> / <?php echo number_format($stats['blocked_users']); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-exclamation-triangle text-warning" style="font-size: 2rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Форма настроек уведомлений -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-send-fill me-2"></i>
                        Ручная отправка уведомлений
                    </h6>
                </div>
                <div class="card-body">
                    <form id="manualNotificationForm">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="recipientType" class="form-label">Получатель</label>
                                <select class="form-select" id="recipientType" name="recipient_type">
                                    <option value="all" selected>Всем пользователям</option>
                                    <option value="single">Одному пользователю</option>
                                </select>
                            </div>
                            <div class="col-md-6" id="singleUserSearch" style="display: none;">
                                <label for="userSearch" class="form-label">Поиск пользователя (ID или Username)</label>
                                <input type="text" class="form-control" id="userSearch" name="user_search" placeholder="Например: 123456789 или @username">
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label for="manualMessage" class="form-label">Текст сообщения</label>
                                <textarea class="form-control" id="manualMessage" name="message" rows="5" required></textarea>
                                <small class="text-muted">Используйте <code>{first_name}</code>, <code>{last_name}</code>, <code>{username}</code> для персонализации.</small>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <button type="submit" class="btn btn-success">
                                    <i class="bi bi-send me-1"></i>
                                    Отправить
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Форма настроек уведомлений -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-gear me-2"></i>
                        Настройки уведомлений
                    </h6>
                </div>
                <div class="card-body">
                    <form id="notificationSettingsForm">
                        <!-- Включение/выключение уведомлений -->
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="notificationsEnabled" 
                                           name="notifications_enabled" <?php echo $notificationsEnabled ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="notificationsEnabled">
                                        <strong>Включить автоматические уведомления</strong>
                                        <small class="text-muted d-block">
                                            Отправлять уведомления неактивным пользователям каждый день в 10:00 UTC
                                        </small>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Настройки времени неактивности -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="inactiveHours" class="form-label">
                                    <i class="bi bi-clock me-1"></i>
                                    Часы неактивности
                                </label>
                                <input type="number" class="form-control" id="inactiveHours" 
                                       name="inactive_hours" value="<?php echo htmlspecialchars($inactiveHours); ?>" 
                                       min="1" max="168">
                                <small class="text-muted">
                                    Отправлять уведомления пользователям, неактивным более указанного количества часов
                                </small>
                            </div>
                            <div class="col-md-6">
                                <label for="botUsername" class="form-label">
                                    <i class="bi bi-robot me-1"></i>
                                    Username бота
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">@</span>
                                    <input type="text" class="form-control" id="botUsername" 
                                           name="bot_username" value="<?php echo htmlspecialchars($botUsername); ?>">
                                </div>
                                <small class="text-muted">
                                    Username Telegram бота для ссылки в уведомлениях
                                </small>
                            </div>
                        </div>

                        <!-- Шаблон сообщения -->
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label for="messageTemplate" class="form-label">
                                    <i class="bi bi-chat-text me-1"></i>
                                    Шаблон сообщения
                                </label>
                                <textarea class="form-control" id="messageTemplate" name="message_template" 
                                          rows="8" style="font-family: monospace;"><?php echo htmlspecialchars($messageTemplate); ?></textarea>
                                <small class="text-muted">
                                    Доступные переменные: <code>{first_name}</code>, <code>{last_name}</code>, <code>{username}</code>
                                    <br>Используйте <code>\n</code> для переноса строк
                                </small>
                            </div>
                        </div>

                        <!-- Кнопки управления -->
                        <div class="row">
                            <div class="col-md-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-lg me-1"></i>
                                    Сохранить настройки
                                </button>
                                <button type="button" class="btn btn-secondary ms-2" onclick="resetToDefaults()">
                                    <i class="bi bi-arrow-clockwise me-1"></i>
                                    Сбросить к умолчанию
                                </button>
                                <button type="button" class="btn btn-info ms-2" onclick="previewMessage()">
                                    <i class="bi bi-eye me-1"></i>
                                    Предварительный просмотр
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Последние отправленные уведомления -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-clock-history me-2"></i>
                        Последние отправленные уведомления
                    </h6>
                </div>
                <div class="card-body">
                    <div id="recentNotifications">
                        <div class="text-center py-3">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Загрузка...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Модальное окно предварительного просмотра -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-eye me-2"></i>
                    Предварительный просмотр сообщения
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <strong>Пример для пользователя:</strong> Иван Петров (@ivan_petrov)
                </div>
                <div class="border p-3 bg-light" style="white-space: pre-line; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                    <div id="previewContent"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Закрыть</button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
<script>
// Сохранение настроек
document.getElementById('notificationSettingsForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    // Добавляем значение чекбокса
    formData.set('notifications_enabled', document.getElementById('notificationsEnabled').checked ? '1' : '0');
    
    fetch('save_notification_settings.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('Настройки успешно сохранены!', 'success');
        } else {
            showAlert('Ошибка сохранения: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        showAlert('Ошибка сети: ' + error.message, 'danger');
    });
});

// Предварительный просмотр сообщения
function previewMessage() {
    const template = document.getElementById('messageTemplate').value;
    const preview = template
        .replace(/{first_name}/g, 'Иван')
        .replace(/{last_name}/g, 'Петров')
        .replace(/{username}/g, '@ivan_petrov')
        .replace(/\\n/g, '\n');
    
    document.getElementById('previewContent').textContent = preview;
    new bootstrap.Modal(document.getElementById('previewModal')).show();
}

// Сброс к настройкам по умолчанию
function resetToDefaults() {
    if (confirm('Вы уверены, что хотите сбросить все настройки к значениям по умолчанию?')) {
        document.getElementById('notificationsEnabled').checked = true;
        document.getElementById('inactiveHours').value = '24';
        document.getElementById('botUsername').value = 'uniqpaid_paid_bot';
        document.getElementById('messageTemplate').value = 'Привет, {first_name}! 👋\n\n🎉 Добро пожаловать в UniQPaid!\n\n💰 Для вас сегодня доступна реклама в приложении - зарабатывайте монеты за просмотры!\n\n🚀 Начните прямо сейчас: @uniqpaid_paid_bot\n\n💎 Каждый просмотр = монеты на ваш баланс!';
    }
}

// Тестовая отправка уведомления
function testNotification() {
    if (confirm('Отправить тестовое уведомление администратору?')) {
        fetch('test_notification.php', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('Тестовое уведомление отправлено!', 'success');
            } else {
                showAlert('Ошибка отправки: ' + data.error, 'danger');
            }
        })
        .catch(error => {
            showAlert('Ошибка сети: ' + error.message, 'danger');
        });
    }
}

// Показ уведомлений
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('main');
    container.insertBefore(alertDiv, container.firstChild);
    
    // Автоматически скрыть через 5 секунд
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// Загрузка последних уведомлений
function loadRecentNotifications() {
    fetch('get_recent_notifications.php')
        .then(response => response.json())
        .then(data => {
            const container = document.getElementById('recentNotifications');
            if (data.success && data.notifications.length > 0) {
                let html = '<div class="table-responsive"><table class="table table-sm">';
                html += '<thead><tr><th>Время</th><th>Пользователь</th><th>Статус</th><th>Сообщение</th></tr></thead><tbody>';
                
                data.notifications.forEach(notification => {
                    const statusBadge = notification.status === 'sent' ? 'success' : 
                                       notification.status === 'failed' ? 'danger' : 'warning';
                    const statusText = notification.status === 'sent' ? 'Отправлено' : 
                                      notification.status === 'failed' ? 'Ошибка' : 'Заблокирован';
                    
                    html += `<tr>
                        <td><small>${notification.sent_at}</small></td>
                        <td><small>${notification.first_name} ${notification.last_name || ''} ${notification.username ? '(@' + notification.username + ')' : ''}</small></td>
                        <td><span class="badge bg-${statusBadge}">${statusText}</span></td>
                        <td><small>${notification.message_text.substring(0, 50)}...</small></td>
                    </tr>`;
                });
                
                html += '</tbody></table></div>';
                container.innerHTML = html;
            } else {
                container.innerHTML = '<div class="text-center text-muted py-3">Уведомления еще не отправлялись</div>';
            }
        })
        .catch(error => {
            document.getElementById('recentNotifications').innerHTML = 
                '<div class="text-center text-danger py-3">Ошибка загрузки данных</div>';
        });
}

// Загружаем последние уведомления при загрузке страницы
document.addEventListener('DOMContentLoaded', function() {
    loadRecentNotifications();

    const recipientType = document.getElementById('recipientType');
    const singleUserSearch = document.getElementById('singleUserSearch');

    recipientType.addEventListener('change', function() {
        if (this.value === 'single') {
            singleUserSearch.style.display = 'block';
        } else {
            singleUserSearch.style.display = 'none';
        }
    });

    const manualNotificationForm = document.getElementById('manualNotificationForm');
    manualNotificationForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const recipientType = formData.get('recipient_type');
        const message = formData.get('message');

        if (!message) {
            showAlert('Текст сообщения не может быть пустым.', 'warning');
            return;
        }

        let confirmationText = recipientType === 'all' 
            ? 'Вы уверены, что хотите отправить это сообщение всем пользователям?'
            : 'Вы уверены, что хотите отправить это сообщение?';

        if (confirm(confirmationText)) {
            fetch('send_manual_notification.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert(data.message, 'success');
                    manualNotificationForm.reset();
                    singleUserSearch.style.display = 'none';
                } else {
                    showAlert('Ошибка: ' + data.error, 'danger');
                }
            })
            .catch(error => {
                showAlert('Сетевая ошибка: ' + error.message, 'danger');
            });
        }
    });
});
</script>

<?php include 'templates/footer.php'; ?>
