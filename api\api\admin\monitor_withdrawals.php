<?php
/**
 * api/admin/monitor_withdrawals.php
 * Мониторинг статуса выплат в реальном времени
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

// Проверяем аутентификацию
session_start();
require_once __DIR__ . '/auth.php';
if (!isAuthenticated()) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../db_mock.php';
require_once __DIR__ . '/../NOWPaymentsAPI.php';

try {
    $api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);
    
    // Загружаем данные пользователей
    $userData = loadUserData();
    
    $pendingWithdrawals = [];
    $completedWithdrawals = [];
    $failedWithdrawals = [];
    $totalWithdrawals = 0;
    $totalAmount = 0;
    
    // Проходим по всем пользователям и их выплатам
    foreach ($userData as $userId => $user) {
        if (isset($user['withdrawals']) && is_array($user['withdrawals'])) {
            foreach ($user['withdrawals'] as $withdrawal) {
                $totalWithdrawals++;
                $totalAmount += $withdrawal['coins_amount'] ?? 0;
                
                // Проверяем статус выплаты через API
                $currentStatus = $withdrawal['status'] ?? 'unknown';
                $payoutId = $withdrawal['payout_id'] ?? null;
                $statusUpdated = false;

                if ($payoutId) {
                    try {
                        $statusCheck = $api->getPayoutStatus($payoutId);
                        if ($statusCheck && isset($statusCheck['status'])) {
                            $newStatus = strtolower($statusCheck['status']);

                            // Обновляем статус в данных пользователя если изменился
                            if ($newStatus !== $currentStatus) {
                                $withdrawalIndex = array_search($withdrawal, $user['withdrawals']);
                                if ($withdrawalIndex !== false) {
                                    $userData[$userId]['withdrawals'][$withdrawalIndex]['status'] = $newStatus;
                                    $userData[$userId]['withdrawals'][$withdrawalIndex]['updated_at'] = date('Y-m-d H:i:s');

                                    // Добавляем дополнительную информацию из API
                                    if (isset($statusCheck['amount'])) {
                                        $userData[$userId]['withdrawals'][$withdrawalIndex]['actual_amount'] = $statusCheck['amount'];
                                    }
                                    if (isset($statusCheck['fee'])) {
                                        $userData[$userId]['withdrawals'][$withdrawalIndex]['actual_fee'] = $statusCheck['fee'];
                                    }
                                    if (isset($statusCheck['hash'])) {
                                        $userData[$userId]['withdrawals'][$withdrawalIndex]['transaction_hash'] = $statusCheck['hash'];
                                    }

                                    $currentStatus = $newStatus;
                                    $statusUpdated = true;
                                    error_log("Monitor INFO: Статус выплаты {$payoutId} обновлен: {$withdrawal['status']} -> {$newStatus}");
                                }
                            }
                        }
                    } catch (Exception $e) {
                        error_log("Monitor ERROR: Не удалось проверить статус выплаты {$payoutId}: " . $e->getMessage());
                    }
                }
                
                $withdrawalData = [
                    'id' => $withdrawal['id'] ?? 'unknown',
                    'user_id' => $userId,
                    'user_name' => $user['first_name'] ?? "User {$userId}",
                    'amount' => $withdrawal['coins_amount'] ?? 0,
                    'currency' => $withdrawal['currency'] ?? 'unknown',
                    'address' => $withdrawal['wallet_address'] ?? 'unknown',
                    'status' => $currentStatus,
                    'created_at' => $withdrawal['created_at'] ?? 'unknown',
                    'updated_at' => $withdrawal['updated_at'] ?? $withdrawal['created_at'] ?? 'unknown',
                    'payout_id' => $payoutId,
                    'usd_amount' => ($withdrawal['coins_amount'] ?? 0) * CONVERSION_RATE
                ];
                
                // Группируем по статусам (обновленные статусы NOWPayments)
                switch (strtolower($currentStatus)) {
                    // Статусы в обработке
                    case 'waiting':
                    case 'pending':
                    case 'processing':
                    case 'sending':
                    case 'confirming':
                        $pendingWithdrawals[] = $withdrawalData;
                        break;
                    // Завершенные статусы
                    case 'completed':
                    case 'finished':
                    case 'confirmed':
                        $completedWithdrawals[] = $withdrawalData;
                        break;
                    // Неудачные статусы
                    case 'failed':
                    case 'rejected':
                    case 'cancelled':
                    case 'expired':
                        $failedWithdrawals[] = $withdrawalData;
                        break;
                    default:
                        $pendingWithdrawals[] = $withdrawalData;
                }
            }
        }
    }
    
    // Сохраняем обновленные данные
    if (saveUserData($userData)) {
        error_log("Monitor INFO: Данные успешно сохранены");
    } else {
        error_log("Monitor ERROR: Не удалось сохранить данные");
    }
    
    // Сортируем по дате (новые сначала)
    usort($pendingWithdrawals, function($a, $b) {
        return strtotime($b['created_at']) - strtotime($a['created_at']);
    });
    usort($completedWithdrawals, function($a, $b) {
        return strtotime($b['created_at']) - strtotime($a['created_at']);
    });
    usort($failedWithdrawals, function($a, $b) {
        return strtotime($b['created_at']) - strtotime($a['created_at']);
    });
    
    // Получаем баланс NOWPayments
    $balance = $api->getAccountBalance();
    $totalBalanceUSD = 0;
    
    if ($balance) {
        foreach ($balance as $currency => $data) {
            $amount = is_array($data) ? ($data['amount'] ?? 0) : $data;
            if ($amount > 0) {
                try {
                    $estimate = $api->getEstimateAmount($amount, $currency, 'usd');
                    if (isset($estimate['estimated_amount'])) {
                        $totalBalanceUSD += $estimate['estimated_amount'];
                    }
                } catch (Exception $e) {
                    // Игнорируем ошибки конвертации
                }
            }
        }
    }
    
    // Возвращаем результат
    echo json_encode([
        'success' => true,
        'timestamp' => time(),
        'summary' => [
            'total_withdrawals' => $totalWithdrawals,
            'total_amount_coins' => $totalAmount,
            'total_amount_usd' => $totalAmount * CONVERSION_RATE,
            'pending_count' => count($pendingWithdrawals),
            'completed_count' => count($completedWithdrawals),
            'failed_count' => count($failedWithdrawals),
            'balance_usd' => $totalBalanceUSD
        ],
        'withdrawals' => [
            'pending' => array_slice($pendingWithdrawals, 0, 10), // Последние 10
            'completed' => array_slice($completedWithdrawals, 0, 10), // Последние 10
            'failed' => array_slice($failedWithdrawals, 0, 10) // Последние 10
        ],
        'balance' => $balance,
        'updated_at' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    error_log("Monitor CRITICAL ERROR: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Ошибка мониторинга: ' . $e->getMessage(),
        'timestamp' => time()
    ]);
}
?>
