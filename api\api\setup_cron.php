<?php
/**
 * setup_cron.php
 * Настройка автоматического обновления статусов выплат
 */

echo "<!DOCTYPE html>\n";
echo "<html><head><meta charset='utf-8'><title>Настройка автообновления</title>";
echo "<style>body{font-family:Arial;margin:20px;background:#0a0a0a;color:white;} .success{color:#00ff00;} .error{color:#ff4444;} .info{color:#00ffff;} .container{background:linear-gradient(45deg,#1a1a2e,#16213e,#533483);padding:20px;border-radius:10px;margin:20px 0;} code{background:#333;padding:2px 6px;border-radius:3px;}</style>";
echo "</head><body>\n";

echo "<div class='container'>\n";
echo "<h1>⚙️ Настройка автоматического обновления статусов выплат</h1>\n";
echo "</div>\n";

echo "<div class='container'>\n";
echo "<h2>🤖 Автоматизация через Cron</h2>\n";
echo "<p>Для автоматического обновления статусов выплат каждые 5 минут добавьте в crontab:</p>\n";
echo "<code>*/5 * * * * /usr/bin/php " . __DIR__ . "/auto_update_withdrawals.php</code>\n";
echo "<p class='info'>💡 Это будет проверять и обновлять статусы всех активных выплат каждые 5 минут</p>\n";
echo "</div>\n";

echo "<div class='container'>\n";
echo "<h2>🌐 Веб-вызов (альтернатива)</h2>\n";
echo "<p>Если cron недоступен, можно настроить внешний сервис для вызова URL каждые 5 минут:</p>\n";
echo "<code>https://app.uniqpaid.com/test2/api/auto_update_withdrawals.php?web=1</code>\n";
echo "<p class='info'>💡 Используйте сервисы типа cron-job.org или uptimerobot.com</p>\n";
echo "</div>\n";

echo "<div class='container'>\n";
echo "<h2>🔧 Ручное управление</h2>\n";
echo "<p><a href='auto_update_withdrawals.php?web=1' style='color:#00ffff;'>🔄 Запустить обновление сейчас</a></p>\n";
echo "<p><a href='fix_withdrawals.php' style='color:#00ffff;'>🛠️ Панель исправления выплат</a></p>\n";
echo "<p><a href='../admin/withdrawals.php' style='color:#00ffff;'>📊 Админ-панель выплат</a></p>\n";
echo "</div>\n";

echo "<div class='container'>\n";
echo "<h2>📋 Инструкция по настройке</h2>\n";
echo "<ol>\n";
echo "<li><strong>Через cPanel/Hosting Panel:</strong>\n";
echo "<ul>\n";
echo "<li>Найдите раздел 'Cron Jobs' или 'Задания по расписанию'</li>\n";
echo "<li>Добавьте новое задание с командой выше</li>\n";
echo "<li>Установите интервал: каждые 5 минут</li>\n";
echo "</ul>\n";
echo "</li>\n";
echo "<li><strong>Через SSH (если есть доступ):</strong>\n";
echo "<ul>\n";
echo "<li>Выполните: <code>crontab -e</code></li>\n";
echo "<li>Добавьте строку с командой выше</li>\n";
echo "<li>Сохраните и выйдите</li>\n";
echo "</ul>\n";
echo "</li>\n";
echo "<li><strong>Через внешний сервис:</strong>\n";
echo "<ul>\n";
echo "<li>Зарегистрируйтесь на cron-job.org</li>\n";
echo "<li>Добавьте URL для вызова каждые 5 минут</li>\n";
echo "<li>Активируйте задание</li>\n";
echo "</ul>\n";
echo "</li>\n";
echo "</ol>\n";
echo "</div>\n";

echo "<div class='container'>\n";
echo "<h2>✅ Проверка работы</h2>\n";
echo "<p>После настройки автообновления:</p>\n";
echo "<ul>\n";
echo "<li>Создайте тестовую выплату</li>\n";
echo "<li>Подождите 5-10 минут</li>\n";
echo "<li>Проверьте, обновился ли статус в приложении</li>\n";
echo "<li>Посмотрите логи в файле error_log</li>\n";
echo "</ul>\n";
echo "</div>\n";

// Проверяем текущее состояние
echo "<div class='container'>\n";
echo "<h2>📊 Текущее состояние</h2>\n";

try {
    require_once __DIR__ . '/config.php';
    require_once __DIR__ . '/functions.php';
    
    $userData = loadUserData();
    if (is_array($userData)) {
        $totalWithdrawals = 0;
        $pendingWithdrawals = 0;
        
        foreach ($userData as $user) {
            if (isset($user['withdrawals']) && is_array($user['withdrawals'])) {
                foreach ($user['withdrawals'] as $withdrawal) {
                    $totalWithdrawals++;
                    if (($withdrawal['status'] ?? 'unknown') === 'pending') {
                        $pendingWithdrawals++;
                    }
                }
            }
        }
        
        echo "<p class='success'>✅ Данные загружены успешно</p>\n";
        echo "<p><strong>Всего выплат:</strong> {$totalWithdrawals}</p>\n";
        echo "<p><strong>В ожидании:</strong> {$pendingWithdrawals}</p>\n";
        
        if ($pendingWithdrawals > 0) {
            echo "<p class='info'>💡 Есть выплаты, требующие обновления статуса</p>\n";
        }
    } else {
        echo "<p class='error'>❌ Не удалось загрузить данные пользователей</p>\n";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Ошибка: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "</div>\n";

echo "</body></html>\n";
?>
