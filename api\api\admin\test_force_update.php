<?php
/**
 * Тест для проверки принудительного обновления статусов
 */

require_once __DIR__ . '/auth.php';

// Проверяем аутентификацию
session_start();
if (!isAuthenticated()) {
    header('Location: login.php');
    exit;
}

header('Content-Type: text/plain; charset=utf-8');

echo "=== ТЕСТ ПРИНУДИТЕЛЬНОГО ОБНОВЛЕНИЯ СТАТУСОВ ===\n\n";

// Тестируем обычный режим
echo "1. Тестируем обычный режим (без JSON):\n";
echo "URL: ../force_update_withdrawals.php\n\n";

$output = file_get_contents('http://argun-defolt.loc/api/force_update_withdrawals.php');
echo "Результат:\n";
echo $output;
echo "\n" . str_repeat("=", 50) . "\n\n";

// Тестируем JSON режим
echo "2. Тестируем JSON режим:\n";
echo "URL: ../force_update_withdrawals.php?json=1\n\n";

// Создаем контекст с сессией
$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => 'Cookie: ' . session_name() . '=' . session_id()
    ]
]);

$jsonOutput = file_get_contents('http://argun-defolt.loc/api/force_update_withdrawals.php?json=1', false, $context);
echo "Результат:\n";
echo $jsonOutput;
echo "\n" . str_repeat("=", 50) . "\n\n";

// Проверяем валидность JSON
if ($jsonOutput) {
    $decoded = json_decode($jsonOutput, true);
    if ($decoded !== null) {
        echo "✅ JSON валидный\n";
        echo "Структура ответа:\n";
        print_r($decoded);
    } else {
        echo "❌ JSON невалидный\n";
        echo "Ошибка JSON: " . json_last_error_msg() . "\n";
    }
} else {
    echo "❌ Пустой ответ\n";
}

echo "\n=== ТЕСТ ЗАВЕРШЕН ===\n";
?>
