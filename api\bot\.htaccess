# Защита bot директории
# Настройки для корректной работы с Telegram мини-приложением

<IfModule mod_headers.c>
    # РАЗРЕШАЕМ отображение в iframe для Telegram
    Header always unset X-Frame-Options
    Header always set X-Frame-Options "ALLOWALL"

    # Разрешаем CORS для webhook запросов
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With, X-Telegram-Bot-Api-Secret-Token"
</IfModule>

# Обработка preflight запросов
RewriteEngine On
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]

# Запрещаем доступ ко всем файлам по умолчанию
<Files "*">
    Order Deny,Allow
    Deny from all
</Files>

# Разрешаем доступ только к webhook.php для Telegram
<Files "webhook.php">
    Order Allow,Deny
    Allow from all
    
    # Дополнительная защита - разрешаем только POST запросы от Telegram
    <RequireAll>
        Require method POST
    </RequireAll>
</Files>

# Разрешаем доступ к административным файлам
<Files "set_webhook.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "check_webhook.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "setup_webhook.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "remove_webhook.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "test_webhook.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "fix_bot.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "diagnose.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "update_image.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "update_super_banner.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "reload_image.php">
    Order Allow,Deny
    Allow from all
</Files>

# Запрещаем доступ к конфигурационным файлам
<Files "config.php">
    Order Deny,Allow
    Deny from all
</Files>

# Запрещаем доступ к документации
<Files "*.md">
    Order Deny,Allow
    Deny from all
</Files>

# Запрещаем доступ к логам
<Files "*.log">
    Order Deny,Allow
    Deny from all
</Files>

# Запрещаем просмотр директории
Options -Indexes
