<?php
/**
 * api/getCurrencyData.php
 * API для получения актуальных данных о валютах с минимумами и комиссиями
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Обработка preflight запросов
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Подключаем конфигурацию
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/NOWPaymentsAPI.php';

try {
    // Создаем экземпляр API для получения актуальных минимумов
    $api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);
    
    // Базовые данные о валютах (синхронизированы с main.js)
    $currencyData = [
        'ton' => [
            'name' => 'TON (Telegram)',
            'minCoins' => 1956, // Актуальный минимум из API
            'networkFee' => 0.15,
            'status' => 'best'
        ],
        'eth' => [
            'name' => 'Ethereum (ETH)',
            'minCoins' => 833, // Актуальный минимум из API
            'networkFee' => 0.25,
            'status' => 'best'
        ],
        'btc' => [
            'name' => 'Bitcoin (BTC)',
            'minCoins' => 1689, // Актуальный минимум из API
            'networkFee' => 0.50,
            'status' => 'good'
        ],
        'usdttrc20' => [
            'name' => 'USDT (TRC20)',
            'minCoins' => 21714, // Актуальный минимум из API
            'networkFee' => 5.58,
            'status' => 'expensive'
        ],
        'ltc' => [
            'name' => 'Litecoin (LTC)',
            'minCoins' => 1196, // Актуальный минимум из API
            'networkFee' => 0.50,
            'status' => 'good'
        ],
        'bch' => [
            'name' => 'Bitcoin Cash (BCH)',
            'minCoins' => 1172, // Актуальный минимум из API
            'networkFee' => 0.30,
            'status' => 'good'
        ],
        'xrp' => [
            'name' => 'Ripple (XRP)',
            'minCoins' => 2903, // Актуальный минимум из API
            'networkFee' => 0.20,
            'status' => 'good'
        ],
        'ada' => [
            'name' => 'Cardano (ADA)',
            'minCoins' => 1354, // Актуальный минимум из API
            'networkFee' => 0.30,
            'status' => 'good'
        ],
        'dot' => [
            'name' => 'Polkadot (DOT)',
            'minCoins' => 1303, // Актуальный минимум из API
            'networkFee' => 0.40,
            'status' => 'good'
        ]
    ];
    
    // Получаем актуальные курсы валют
    $exchangeRates = [];
    foreach ($currencyData as $currency => $data) {
        try {
            if ($currency === 'usdttrc20') {
                $exchangeRates[$currency] = 1.0; // USDT = 1 USD
            } else {
                $rate = $api->getEstimateAmount(1, $currency, 'usd');
                if ($rate && isset($rate['estimated_amount'])) {
                    $exchangeRates[$currency] = $rate['estimated_amount'];
                    error_log("getCurrencyData INFO: Актуальный курс {$currency}: {$rate['estimated_amount']} USD");
                }
            }
        } catch (Exception $e) {
            error_log("getCurrencyData WARNING: Не удалось получить курс {$currency}: " . $e->getMessage());
            // Используем fallback курсы
            $fallbackRates = [
                'ton' => 5.5,
                'eth' => 2630,
                'btc' => 43000,
                'ltc' => 73,
                'bch' => 250,
                'xrp' => 0.52,
                'ada' => 0.38,
                'dot' => 6.8
            ];
            $exchangeRates[$currency] = $fallbackRates[$currency] ?? 1;
        }
    }

    // Обновляем минимумы из API NOWPayments
    foreach ($currencyData as $currency => &$data) {
        try {
            $apiMinimum = $api->getMinWithdrawalAmount($currency);
            if ($apiMinimum !== null) {
                // Конвертируем API минимум из криптовалюты в USD
                $apiMinInUsd = $apiMinimum;
                if ($currency !== 'usdttrc20' && isset($exchangeRates[$currency])) {
                    $apiMinInUsd = $apiMinimum * $exchangeRates[$currency];
                }

                // Рассчитываем минимум в USD с учетом комиссии
                // Формула: (минимум_API + комиссия) = сумма_в_монетах * 0.001 - комиссия
                // Отсюда: сумма_в_монетах = (минимум_API + 2*комиссия) / 0.001
                $requiredUsdBeforeFee = $apiMinInUsd + (2 * $data['networkFee']);

                // Конвертируем в монеты с запасом
                $minInCoins = ceil($requiredUsdBeforeFee / CONVERSION_RATE);

                // ИСПРАВЛЕНИЕ: Увеличиваем запас для ETH из-за высоких комиссий и проблем с округлением
                if ($currency === 'eth') {
                    $minInCoins = ceil($minInCoins * 1.5); // 50% запас для ETH
                } else {
                    $minInCoins = ceil($minInCoins * 1.2); // 20% запас для остальных
                }

                // Используем рассчитанный минимум
                $data['minCoins'] = max($minInCoins, 1);
                $data['api_minimum'] = $apiMinimum;
                $data['api_min_usd'] = $apiMinInUsd;
                $data['required_usd_before_fee'] = $requiredUsdBeforeFee;

                // Обновляем комиссию на основе актуального курса
                if (isset($exchangeRates[$currency])) {
                    $data['current_rate'] = $exchangeRates[$currency];
                }

                error_log("getCurrencyData INFO: {$currency} - API минимум: {$apiMinimum}, USD: {$minInUsd}, монеты: {$minInCoins}, итого: {$data['minCoins']}");
            }
        } catch (Exception $e) {
            error_log("getCurrencyData WARNING: Ошибка получения минимума для {$currency}: " . $e->getMessage());
            // Добавляем fallback курс
            if (isset($exchangeRates[$currency])) {
                $data['current_rate'] = $exchangeRates[$currency];
            }
        }
    }
    
    // Добавляем информацию о том, показывать ли комиссии
    $showFees = defined('SHOW_FEES_TO_USER') ? SHOW_FEES_TO_USER : true;
    
    // Возвращаем обновленные данные
    echo json_encode([
        'success' => true,
        'currencies' => $currencyData,
        'exchange_rates' => $exchangeRates,
        'show_fees' => $showFees,
        'conversion_rate' => CONVERSION_RATE,
        'updated_at' => time(),
        'message' => 'Данные о валютах успешно загружены с актуальными курсами'
    ]);
    
} catch (Exception $e) {
    error_log("getCurrencyData ERROR: " . $e->getMessage());
    
    // В случае ошибки возвращаем актуальные базовые данные
    $fallbackData = [
        'ton' => [
            'name' => 'TON (Telegram)',
            'minCoins' => 1956,
            'networkFee' => 0.15,
            'status' => 'best'
        ],
        'eth' => [
            'name' => 'Ethereum (ETH)',
            'minCoins' => 833,
            'networkFee' => 0.25,
            'status' => 'best'
        ],
        'btc' => [
            'name' => 'Bitcoin (BTC)',
            'minCoins' => 1689,
            'networkFee' => 0.50,
            'status' => 'good'
        ],
        'usdttrc20' => [
            'name' => 'USDT (TRC20)',
            'minCoins' => 21714,
            'networkFee' => 5.58,
            'status' => 'expensive'
        ]
    ];
    
    echo json_encode([
        'success' => true,
        'currencies' => $fallbackData,
        'show_fees' => true,
        'conversion_rate' => 0.001,
        'updated_at' => time(),
        'message' => 'Использованы данные по умолчанию из-за ошибки API',
        'error' => $e->getMessage()
    ]);
}
?>
