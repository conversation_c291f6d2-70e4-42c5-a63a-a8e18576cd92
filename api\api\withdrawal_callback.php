<?php
/**
 * api/withdrawal_callback.php
 * Обработчик IPN (Instant Payment Notification) от NOWPayments API
 */

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/error.log');
error_reporting(E_ALL);

// --- Подключение зависимостей с проверкой ---
if (!(@require_once __DIR__ . '/config.php')) {
    http_response_code(500);
    error_log('FATAL: config.php not found in withdrawal_callback.php');
    exit;
}
if (!(@require_once __DIR__ . '/db_mock.php')) {
    http_response_code(500);
    error_log('FATAL: db_mock.php not found in withdrawal_callback.php');
    exit;
}
if (!(@require_once __DIR__ . '/NOWPaymentsAPI.php')) {
    http_response_code(500);
    error_log('FATAL: NOWPaymentsAPI.php not found in withdrawal_callback.php');
    exit;
}
// --- Конец проверки зависимостей ---

// Получаем данные IPN уведомления
$ipnData = file_get_contents('php://input');
error_log("withdrawal_callback INFO: Получено IPN уведомление: " . $ipnData);

// Проверяем наличие заголовка с подписью
if (!isset($_SERVER['HTTP_X_NOWPAYMENTS_SIG'])) {
    error_log("withdrawal_callback ERROR: Отсутствует заголовок X-NOWPayments-Sig");
    http_response_code(400);
    exit;
}

$signature = $_SERVER['HTTP_X_NOWPAYMENTS_SIG'];

try {
    // Создаем экземпляр API клиента
    $api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);

    // Проверяем подпись
    if (!$api->verifyIPNSignature($ipnData, $signature)) {
        error_log("withdrawal_callback ERROR: Неверная подпись IPN");
        http_response_code(403);
        exit;
    }

    // Декодируем данные
    $data = json_decode($ipnData, true);
    if (!$data) {
        error_log("withdrawal_callback ERROR: Не удалось декодировать JSON данные");
        http_response_code(400);
        exit;
    }

    // Проверяем наличие необходимых полей (NOWPayments использует другие поля)
    if (!isset($data['id']) || !isset($data['status'])) {
        error_log("withdrawal_callback ERROR: Отсутствуют обязательные поля в IPN данных");
        http_response_code(400);
        exit;
    }

    $withdrawalId = $data['id'];
    $status = $data['status'];
    $userId = $data['extra_id'] ?? null; // NOWPayments передает user_id в extra_id

    if (!$userId) {
        error_log("withdrawal_callback ERROR: Не найден user_id в extra_id");
        http_response_code(400);
        exit;
    }

    $userId = intval($userId);

    error_log("withdrawal_callback INFO: Обработка IPN для withdrawal_id: {$withdrawalId}, status: {$status}, user_id: {$userId}");

    // Обрабатываем статус выплаты (NOWPayments статусы)
    // Нормализуем статус
    $normalizedStatus = strtolower($status);
    
    switch ($normalizedStatus) {
        case 'finished':
        case 'confirmed':
        case 'completed':
            recordSuccessfulWithdrawal($userId, $withdrawalId, $data);
            break;

        case 'failed':
        case 'expired':
        case 'refunded':
        case 'rejected':
        case 'cancelled':
            // Выплата не удалась, возвращаем средства пользователю
            refundFailedWithdrawal($userId, $withdrawalId, $data);
            break;

        case 'waiting':
        case 'confirming':
        case 'sending':
        default:
            // Другие статусы (pending, processing и т.д.) просто логируем
            error_log("withdrawal_callback INFO: Получен статус '{$status}' для withdrawal_id: {$withdrawalId}");
            break;
    }

    // Отправляем успешный ответ
    http_response_code(200);
    echo 'OK';

} catch (Exception $e) {
    error_log("withdrawal_callback ERROR: Исключение при обработке IPN: " . $e->getMessage());
    http_response_code(500);
    exit;
}

/**
 * Записывает информацию об успешной выплате
 *
 * @param int $userId ID пользователя
 * @param string $withdrawalId ID выплаты
 * @param array $data Данные IPN уведомления
 */
function recordSuccessfulWithdrawal($userId, $withdrawalId, $data) {
    error_log("recordSuccessfulWithdrawal INFO: Запись успешной выплаты для пользователя {$userId}, withdrawal_id: {$withdrawalId}");

    // Загружаем данные пользователя
    $userData = loadUserData();
    if (!is_array($userData) || !isset($userData[$userId])) {
        error_log("recordSuccessfulWithdrawal ERROR: Пользователь {$userId} не найден");
        return;
    }

    // Находим запись о выплате по withdrawalId
    $withdrawalFound = false;
    if (isset($userData[$userId]['withdrawals'])) {
        foreach ($userData[$userId]['withdrawals'] as &$withdrawal) {
            if (($withdrawal['id'] === $withdrawalId) || (isset($withdrawal['payout_id']) && $withdrawal['payout_id'] === $withdrawalId)) {
                // Обновляем статус выплаты
                $withdrawal['status'] = 'confirmed';
                $withdrawal['updated_at'] = date('Y-m-d H:i:s');
                $withdrawalFound = true;
                break;
            }
        }
    }

    if (!$withdrawalFound) {
        error_log("recordSuccessfulWithdrawal WARNING: Запись о выплате {$withdrawalId} не найдена, создаем новую");
        $userData[$userId]['withdrawals'][] = [
            'id' => $withdrawalId,
            'status' => 'confirmed',
            'coins_amount' => $data['coins_amount'] ?? 0, // Добавляем сумму в монетах
            'amount' => $data['amount'] ?? 0,
            'currency' => $data['currency'] ?? '',
            'timestamp' => time(),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
    } else {
        // Списание баланса при успешной выплате
        $coinsAmount = $withdrawal['coins_amount'] ?? 0;
        if ($coinsAmount > 0) {
            if ($userData[$userId]['balance'] < $coinsAmount) {
                error_log("recordSuccessfulWithdrawal WARNING: Баланс пользователя {$userId} меньше суммы списания ({$coinsAmount})");
            } else {
                $userData[$userId]['balance'] -= $coinsAmount;
                error_log("recordSuccessfulWithdrawal INFO: Списано {$coinsAmount} монет с баланса пользователя {$userId}");
            }
        } else {
            error_log("recordSuccessfulWithdrawal ERROR: Не найдена сумма списания в записи о выплате");
        }
    }

    // Сохраняем данные пользователя
    if (!saveUserData($userData)) {
        error_log("recordSuccessfulWithdrawal ERROR: Не удалось сохранить данные пользователя {$userId}");
    } else {
        error_log("recordSuccessfulWithdrawal SUCCESS: Статус выплаты {$withdrawalId} обновлен на 'confirmed'");
    }
}

/**
 * Возвращает средства пользователю при неудачной выплате
 *
 * @param int $userId ID пользователя
 * @param string $withdrawalId ID выплаты
 * @param array $data Данные IPN уведомления
 */
function refundFailedWithdrawal($userId, $withdrawalId, $data) {
    error_log("refundFailedWithdrawal INFO: Возврат средств для пользователя {$userId}, withdrawal_id: {$withdrawalId}");

    // Загружаем данные пользователя
    $userData = loadUserData();
    if (!is_array($userData) || !isset($userData[$userId])) {
        error_log("refundFailedWithdrawal ERROR: Пользователь {$userId} не найден");
        return;
    }

    // Ищем исходную сумму выплаты в монетах
    $refundAmount = 0;
    $withdrawalFound = false;
    $actualStatus = strtolower($data['status'] ?? 'failed');

    if (isset($userData[$userId]['withdrawals'])) {
        foreach ($userData[$userId]['withdrawals'] as &$withdrawal) {
            if (($withdrawal['id'] === $withdrawalId) || (isset($withdrawal['payout_id']) && $withdrawal['payout_id'] === $withdrawalId)) {
                $refundAmount = $withdrawal['coins_amount'] ?? 0;
                $withdrawal['status'] = $actualStatus; // Используем реальный статус из IPN
                $withdrawal['updated_at'] = date('Y-m-d H:i:s');

                // Помечаем как возвращенное
                $withdrawal['refunded'] = true;
                $withdrawal['refund_amount'] = $refundAmount;
                $withdrawal['refund_date'] = date('Y-m-d H:i:s');
                $withdrawal['refund_reason'] = "Автоматический возврат за {$actualStatus} выплату";

                $withdrawalFound = true;
                break;
            }
        }
    }

    if (!$withdrawalFound) {
        error_log("refundFailedWithdrawal ERROR: Запись о выплате {$withdrawalId} не найдена");
        return;
    }

    if ($refundAmount <= 0) {
        error_log("refundFailedWithdrawal ERROR: Неверная сумма для возврата: {$refundAmount}");
        return;
    }

    // Сохраняем изменения в выплате
    if (!saveUserData($userData)) {
        error_log("refundFailedWithdrawal ERROR: Не удалось сохранить данные выплаты для пользователя {$userId}");
        return;
    }

    // Возвращаем средства на баланс используя функцию из functions.php
    $refundReason = "Возврат за {$actualStatus} выплату #{$withdrawalId}";
    if (refundUserBalance($userId, $refundAmount, $refundReason)) {
        error_log("refundFailedWithdrawal SUCCESS: Возвращено {$refundAmount} монет пользователю {$userId} ({$refundReason})");
    } else {
        error_log("refundFailedWithdrawal ERROR: Не удалось вернуть {$refundAmount} монет пользователю {$userId}");
    }
}
?>
