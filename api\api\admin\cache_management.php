<?php
/**
 * api/admin/cache_management.php
 * Управление кэшем и обновление бота
 */

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../error.log');
error_reporting(E_ALL);

// Подключаем файл с функциями аутентификации и авторизации
require_once __DIR__ . '/auth.php';

// Проверяем аутентификацию
session_start();
if (!isAuthenticated()) {
    header('Location: login.php');
    exit;
}

// Подключение зависимостей
require_once __DIR__ . '/../config.php';

// Конфигурация бота
$botToken = '8146437206:AAET9K-yAOukAr9gKuntU9IQds8DAEiAbnA';
$botUsername = 'uniqpaid_paid_bot';
$apiUrl = "https://api.telegram.org/bot{$botToken}/";
$webhookUrl = 'https://app.uniqpaid.com/test3/bot/webhook.php';
$webappUrl = 'https://app.uniqpaid.com/test3/';

// Функция для выполнения запросов к Telegram API
function telegramApiCall($method, $data = []) {
    global $apiUrl;
    
    $url = $apiUrl . $method;
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($result === false || $httpCode !== 200) {
        return ['error' => "HTTP Error: $httpCode"];
    }
    
    $response = json_decode($result, true);
    if (!$response || !$response['ok']) {
        return ['error' => $response['description'] ?? 'Unknown error'];
    }
    
    return $response['result'];
}

$actionMessage = '';
$actionType = '';

// Обработка действий
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    
    switch ($action) {
        case 'clear_cache':
            // Очистка кэша (можно добавить свою логику)
            if (function_exists('opcache_reset')) {
                opcache_reset();
            }
            if (function_exists('apcu_clear_cache')) {
                apcu_clear_cache();
            }
            $actionMessage = 'Кэш сервера успешно очищен';
            $actionType = 'success';
            break;
            
        case 'update_webhook':
            $result = telegramApiCall('setWebhook', [
                'url' => $webhookUrl,
                'allowed_updates' => ['message', 'callback_query'],
                'drop_pending_updates' => true
            ]);
            
            if (isset($result['error'])) {
                $actionMessage = 'Ошибка обновления webhook: ' . $result['error'];
                $actionType = 'error';
            } else {
                $actionMessage = 'Webhook успешно обновлен';
                $actionType = 'success';
            }
            break;
            
        case 'delete_webhook':
            $result = telegramApiCall('deleteWebhook', ['drop_pending_updates' => true]);
            
            if (isset($result['error'])) {
                $actionMessage = 'Ошибка удаления webhook: ' . $result['error'];
                $actionType = 'error';
            } else {
                $actionMessage = 'Webhook успешно удален';
                $actionType = 'success';
            }
            break;
            
        case 'update_bot_commands':
            $commands = [
                ['command' => 'start', 'description' => '🚀 Запустить бота и открыть мини-приложение'],
                ['command' => 'help', 'description' => '❓ Помощь по использованию бота'],
                ['command' => 'balance', 'description' => '💰 Проверить баланс монет'],
                ['command' => 'referral', 'description' => '👥 Реферальная программа']
            ];
            
            $result = telegramApiCall('setMyCommands', ['commands' => $commands]);
            
            if (isset($result['error'])) {
                $actionMessage = 'Ошибка обновления команд: ' . $result['error'];
                $actionType = 'error';
            } else {
                $actionMessage = 'Команды бота успешно обновлены';
                $actionType = 'success';
            }
            break;
            
        case 'full_bot_update':
            $errors = [];
            
            // 1. Удаление старого webhook
            $result = telegramApiCall('deleteWebhook', ['drop_pending_updates' => true]);
            if (isset($result['error'])) {
                $errors[] = 'Ошибка удаления webhook: ' . $result['error'];
            }
            
            sleep(2);
            
            // 2. Установка нового webhook
            $result = telegramApiCall('setWebhook', [
                'url' => $webhookUrl,
                'allowed_updates' => ['message', 'callback_query'],
                'drop_pending_updates' => true
            ]);
            if (isset($result['error'])) {
                $errors[] = 'Ошибка установки webhook: ' . $result['error'];
            }
            
            // 3. Обновление команд
            $commands = [
                ['command' => 'start', 'description' => '🚀 Запустить бота и открыть мини-приложение'],
                ['command' => 'help', 'description' => '❓ Помощь по использованию бота'],
                ['command' => 'balance', 'description' => '💰 Проверить баланс монет'],
                ['command' => 'referral', 'description' => '👥 Реферальная программа']
            ];
            $result = telegramApiCall('setMyCommands', ['commands' => $commands]);
            if (isset($result['error'])) {
                $errors[] = 'Ошибка обновления команд: ' . $result['error'];
            }
            
            // 4. Очистка кэша
            if (function_exists('opcache_reset')) {
                opcache_reset();
            }
            
            if (empty($errors)) {
                $actionMessage = 'Полное обновление бота выполнено успешно!';
                $actionType = 'success';
            } else {
                $actionMessage = 'Обновление завершено с ошибками: ' . implode('; ', $errors);
                $actionType = 'warning';
            }
            break;
    }
}

// Получение информации о боте
$botInfo = telegramApiCall('getMe');
$webhookInfo = telegramApiCall('getWebhookInfo');

// Подключаем шаблон заголовка
include 'templates/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'templates/sidebar.php'; ?>

        <!-- Основное содержимое -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">🔄 Управление кэшем и обновление бота</h1>
            </div>

            <?php if ($actionMessage): ?>
                <div class="alert alert-<?php echo $actionType === 'success' ? 'success' : ($actionType === 'error' ? 'danger' : 'warning'); ?> alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($actionMessage); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Информация о боте -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">🤖 Информация о боте</h5>
                        </div>
                        <div class="card-body">
                            <?php if (isset($botInfo['error'])): ?>
                                <div class="alert alert-danger">
                                    ❌ Ошибка подключения к боту: <?php echo htmlspecialchars($botInfo['error']); ?>
                                </div>
                            <?php else: ?>
                                <p><strong>ID:</strong> <?php echo $botInfo['id']; ?></p>
                                <p><strong>Username:</strong> @<?php echo $botInfo['username']; ?></p>
                                <p><strong>Имя:</strong> <?php echo $botInfo['first_name']; ?></p>
                                <p><strong>Статус:</strong> <span class="badge bg-success">Активен</span></p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">🔗 Webhook</h5>
                        </div>
                        <div class="card-body">
                            <?php if (isset($webhookInfo['error'])): ?>
                                <div class="alert alert-warning">
                                    ⚠️ Ошибка получения webhook: <?php echo htmlspecialchars($webhookInfo['error']); ?>
                                </div>
                            <?php else: ?>
                                <p><strong>URL:</strong> <?php echo $webhookInfo['url'] ?: 'Не установлен'; ?></p>
                                <p><strong>Статус:</strong> 
                                    <span class="badge bg-<?php echo $webhookInfo['url'] ? 'success' : 'warning'; ?>">
                                        <?php echo $webhookInfo['url'] ? 'Активен' : 'Не установлен'; ?>
                                    </span>
                                </p>
                                <?php if (isset($webhookInfo['pending_update_count'])): ?>
                                    <p><strong>Ожидающие обновления:</strong> <?php echo $webhookInfo['pending_update_count']; ?></p>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Действия с кэшем -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">🗂️ Управление кэшем</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <form method="post" style="display: inline;">
                                        <input type="hidden" name="action" value="clear_cache">
                                        <button type="submit" class="btn btn-warning w-100" onclick="return confirm('Очистить кэш сервера?')">
                                            <i class="bi bi-trash3"></i> Очистить кэш сервера
                                        </button>
                                    </form>
                                    <small class="text-muted">Очищает OpCache и APCu кэш</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Действия с ботом -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">🤖 Управление ботом</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <form method="post" style="display: inline;">
                                        <input type="hidden" name="action" value="update_webhook">
                                        <button type="submit" class="btn btn-primary w-100">
                                            <i class="bi bi-arrow-repeat"></i> Обновить Webhook
                                        </button>
                                    </form>
                                    <small class="text-muted">Переустанавливает webhook</small>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <form method="post" style="display: inline;">
                                        <input type="hidden" name="action" value="delete_webhook">
                                        <button type="submit" class="btn btn-outline-danger w-100" onclick="return confirm('Удалить webhook?')">
                                            <i class="bi bi-trash"></i> Удалить Webhook
                                        </button>
                                    </form>
                                    <small class="text-muted">Удаляет webhook</small>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <form method="post" style="display: inline;">
                                        <input type="hidden" name="action" value="update_bot_commands">
                                        <button type="submit" class="btn btn-info w-100">
                                            <i class="bi bi-list-task"></i> Обновить команды
                                        </button>
                                    </form>
                                    <small class="text-muted">Обновляет список команд бота</small>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <form method="post" style="display: inline;">
                                        <input type="hidden" name="action" value="full_bot_update">
                                        <button type="submit" class="btn btn-success w-100" onclick="return confirm('Выполнить полное обновление бота?')">
                                            <i class="bi bi-rocket"></i> Полное обновление
                                        </button>
                                    </form>
                                    <small class="text-muted">Webhook + команды + кэш</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Информация -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">ℹ️ Информация</h5>
                        </div>
                        <div class="card-body">
                            <p><strong>Токен бота:</strong> <?php echo substr($botToken, 0, 15); ?>...</p>
                            <p><strong>Webhook URL:</strong> <?php echo $webhookUrl; ?></p>
                            <p><strong>WebApp URL:</strong> <?php echo $webappUrl; ?></p>
                            <p><strong>Последнее обновление:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include 'templates/footer.php'; ?>
