@charset "UTF-8";

:root {
  --primary-dark: #1a1a1a;
  --accent-orange: #ffc107;
  --accent-orange-light: #ffd54f;
  --accent-orange-dark: #ffa500;
  --bg-card: #2a2a2a;
  --text-primary: #ffffff;
  --bg-secondary: #333333;
  --border-color: #4a4a4a;
  --gradient-orange: linear-gradient(135deg, #ffc107 0%, #ffd54f 100%);
}
body {
  background: #1a1a1a !important;
  color: #ffffff !important;
}

.app-header {
  background: #2a2a2a !important;
  border-bottom: 1px solid #4a4a4a !important;
}

.app-section,
.app-main {
  background-color: transparent !important;
}

.status-message {
  background: #2a2a2a !important;
  color: #ffffff !important;
  border: 1px solid #4a4a4a !important;
}

.balance-info {
  background: #ffc107 !important;
  color: #1a1a1a !important;
}

.user-avatar {
  border: 2px solid #ffc107 !important;
  background: #ffc107 !important;
}

.user-avatar-icon {
  color: #1a1a1a !important;
}

.app-nav {
  background: #2a2a2a !important;
  border-top: 1px solid #4a4a4a !important;
}

.nav-button {
  color: #ffffff !important;
}

.nav-button.active {
  color: #1a1a1a !important;
  background: rgba(255, 193, 7, 0.15) !important;
}

h2 svg,
h3 svg,
h2 > svg,
h3 > svg {
  color: #ffc107 !important;
  fill: #ffc107 !important;
  stroke: #ffc107 !important;
  filter: drop-shadow(0 0 8px rgba(255, 193, 7, 0.5)) !important;
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
  width: 22px !important;
  height: 22px !important;
}

:root {
  --accent-orange: #ffc107 !important;
  --accent-primary: #ffc107 !important;
  --accent-neon: #ffc107 !important;
  --shadow-orange: rgba(255, 193, 7, 0.4) !important;
  --shadow-neon: rgba(255, 193, 7, 0.4) !important;
}

svg[style*="var(--accent-orange)"],
svg[style*="var(--accent-primary)"],
svg[style*="var(--accent-neon)"] {
  color: #ffc107 !important;
  fill: #ffc107 !important;
  stroke: #ffc107 !important;
}

.currency-tab .tab-icon,
.currency-icon {
  color: #ffffff !important;
  fill: #ffffff !important;
  stroke: #ffffff !important;
  filter: none !important;
  width: 24px !important;
  height: 24px !important;
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
  background: none !important;
}

.currency-tab .tab-icon path,
.currency-tab .tab-icon circle {
  fill: #ffffff !important;
  stroke: #ffffff !important;
  opacity: 1 !important;
}

.currency-tab .tab-icon path[fill="none"] {
  fill: none !important;
  stroke: #ffffff !important;
  stroke-width: 1.5 !important;
}

.currency-tab .tab-icon path[fill="currentColor"] {
  fill: #ffffff !important;
  stroke: none !important;
}

.currency-tab .tab-icon circle[fill="none"] {
  fill: none !important;
  stroke: #ffffff !important;
  stroke-width: 1.5 !important;
}

.currency-tab .tab-icon {
  color: #ffffff !important;
  fill: #ffffff !important;
  stroke: #ffffff !important;
}

.currency-tab.active .tab-icon {
  color: #ffffff !important;
  fill: #ffffff !important;
  stroke: #ffffff !important;
}

.history-item,
.withdrawal-item {
  background: #2a2a2a !important;
  border: 1px solid #4a4a4a !important;
  border-top: 3px solid #ffc107 !important;
  color: #ffffff !important;
  padding: 20px !important;
  margin-bottom: 16px !important;
  border-radius: 16px !important;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2), 0 4px 8px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 193, 7, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;
}

.history-item:hover,
.withdrawal-item:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.3), 0 6px 12px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 193, 7, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  border-color: #ffc107 !important;
}

.history-item svg,
.withdrawal-item svg {
  color: #ffc107 !important;
  fill: #ffc107 !important;
  stroke: #ffc107 !important;
}

.withdrawal-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-bottom: 12px !important;
}

.withdrawal-amount {
  font-size: 18px !important;
  font-weight: bold !important;
  color: #ffc107 !important;
  font-family: "Orbitron", monospace !important;
  text-shadow: 0 0 8px rgba(255, 193, 7, 0.4) !important;
}

.withdrawal-status {
  padding: 6px 12px !important;
  border-radius: 12px !important;
  font-size: 11px !important;
  font-weight: bold !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

.withdrawal-details {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  font-size: 12px !important;
  color: #cccccc !important;
  margin-bottom: 8px !important;
}

.withdrawal-currency {
  font-weight: bold !important;
  color: #ffc107 !important;
  text-transform: uppercase !important;
  text-shadow: 0 0 6px rgba(255, 193, 7, 0.3) !important;
}

.withdrawal-date {
  color: #cccccc !important;
  font-size: 12px !important;
  opacity: 0.8 !important;
}

.withdrawal-address {
  font-family: "Courier New", monospace !important;
  font-size: 11px !important;
  color: #ffc107 !important;
  word-break: break-all !important;
  background: rgba(255, 193, 7, 0.1) !important;
  border: 1px solid rgba(255, 193, 7, 0.2) !important;
  padding: 8px 10px !important;
  border-radius: 8px !important;
  margin-top: 8px !important;
}

.status-pending {
  background: linear-gradient(135deg, #ffaa00, #ff8800) !important;
  color: #1a1a1a !important;
  border: 1px solid rgba(255, 170, 0, 0.4) !important;
  box-shadow: 0 0 8px rgba(255, 170, 0, 0.3) !important;
}

.status-processing {
  background: linear-gradient(135deg, #2196f3, #1976d2) !important;
  color: #ffffff !important;
  border: 1px solid rgba(33, 150, 243, 0.4) !important;
  box-shadow: 0 0 8px rgba(33, 150, 243, 0.3) !important;
}

.status-completed,
.status-confirmed {
  background: linear-gradient(135deg, #4caf50, #388e3c) !important;
  color: #ffffff !important;
  border: 1px solid rgba(76, 175, 80, 0.4) !important;
  box-shadow: 0 0 8px rgba(76, 175, 80, 0.3) !important;
}

.status-failed,
.status-cancelled {
  background: linear-gradient(135deg, #ff4444, #cc0000) !important;
  color: #ffffff !important;
  border: 1px solid rgba(255, 68, 68, 0.4) !important;
  box-shadow: 0 0 8px rgba(255, 68, 68, 0.3) !important;
}

.status-expired {
  background: linear-gradient(135deg, #ff9800, #f57c00) !important;
  color: #ffffff !important;
  border: 1px solid rgba(255, 152, 0, 0.4) !important;
  box-shadow: 0 0 8px rgba(255, 152, 0, 0.3) !important;
}

.status-unknown {
  background: linear-gradient(135deg, #666666, #444444) !important;
  color: #ffffff !important;
  border: 1px solid rgba(102, 102, 102, 0.4) !important;
  box-shadow: 0 0 8px rgba(102, 102, 102, 0.3) !important;
}

.withdrawal-success-info {
  background: rgba(76, 175, 80, 0.1) !important;
  color: #4caf50 !important;
  border: 1px solid rgba(76, 175, 80, 0.2) !important;
  border-radius: 8px !important;
  padding: 8px 12px !important;
  margin-top: 8px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
}

.withdrawal-error-info {
  background: rgba(255, 68, 68, 0.1) !important;
  color: #ff4444 !important;
  border: 1px solid rgba(255, 68, 68, 0.2) !important;
  border-radius: 8px !important;
  padding: 8px 12px !important;
  margin-top: 8px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
}

.withdrawal-pending-info {
  background: rgba(255, 170, 0, 0.1) !important;
  color: #ffaa00 !important;
  border: 1px solid rgba(255, 170, 0, 0.2) !important;
  border-radius: 8px !important;
  padding: 8px 12px !important;
  margin-top: 8px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
}

.cancel-button {
  background: linear-gradient(135deg, #ff4444, #cc0000) !important;
  color: #ffffff !important;
  border: none !important;
  padding: 8px 16px !important;
  border-radius: 8px !important;
  font-size: 12px !important;
  font-weight: bold !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  margin-top: 8px !important;
  box-shadow: 0 4px 8px rgba(255, 68, 68, 0.3) !important;
}

.cancel-button:hover {
  background: linear-gradient(135deg, #ff6666, #ff0000) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 6px 12px rgba(255, 68, 68, 0.4) !important;
}

h2,
h3 {
  position: relative !important;
}

.withdrawal-payout-id {
  color: #cccccc !important;
  font-size: 11px !important;
  font-family: "Orbitron", monospace !important;
  opacity: 0.8 !important;
  margin-top: 4px !important;
}

.withdrawal-blockchain-link {
  margin-top: 8px !important;
}

.withdrawal-blockchain-link a {
  color: #ffc107 !important;
  text-decoration: none !important;
  font-size: 12px !important;
  display: inline-flex !important;
  align-items: center !important;
  gap: 6px !important;
  padding: 6px 10px !important;
  background: rgba(255, 193, 7, 0.1) !important;
  border: 1px solid rgba(255, 193, 7, 0.2) !important;
  border-radius: 6px !important;
  transition: all 0.3s ease !important;
  font-family: "Inter", sans-serif !important;
  font-weight: 500 !important;
}

.withdrawal-blockchain-link a:hover {
  background: rgba(255, 193, 7, 0.2) !important;
  border-color: rgba(255, 193, 7, 0.5) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3) !important;
}

.history-container {
  background: #2a2a2a !important;
  border: 1px solid #4a4a4a !important;
  border-top: 3px solid #ffc107 !important;
  border-radius: 16px !important;
  overflow: hidden !important;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2), 0 4px 8px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 193, 7, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

.withdrawal-history {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  margin-top: 15px !important;
}

.history-item.placeholder {
  background: #2a2a2a !important;
  border: 1px solid #4a4a4a !important;
  border-top: 3px solid #ffc107 !important;
  color: #ffffff !important;
  text-align: center !important;
  padding: 20px !important;
  border-radius: 16px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8px !important;
}

h2 svg,
h3 svg {
  position: static !important;
  z-index: 10 !important;
  pointer-events: none !important;
  animation: none !important;
  transition: none !important;
}

svg {
  animation: none !important;
}

#tasks-title svg,
#earnings-title svg,
#friends-title svg {
  color: #ffc107 !important;
  fill: #ffc107 !important;
  stroke: #ffc107 !important;
  filter: drop-shadow(0 0 8px rgba(255, 193, 7, 0.4)) !important;
  width: 28px !important;
  height: 28px !important;
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

#tasks-title,
#earnings-title,
#friends-title {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 12px !important;
  visibility: visible !important;
  opacity: 1 !important;
}

#balance-title svg,
#calculator-title svg,
#withdrawal-title svg,
#history-title svg,
#share-title svg,
#invite-title svg,
#stats-title svg,
#subscriptions-title svg {
  color: #ffc107 !important;
  fill: #ffc107 !important;
  stroke: #ffc107 !important;
  filter: drop-shadow(0 0 6px rgba(255, 193, 7, 0.4)) !important;
  width: 22px !important;
  height: 22px !important;
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

h2[style*="display: flex"],
h3[style*="display: flex"] {
  display: flex !important;
  align-items: center !important;
  gap: 10px !important;
}

.refresh-history-btn {
  background: linear-gradient(145deg, #ffc107, #ffa500) !important;
  color: #1a1a1a !important;
  border: none !important;
  border-radius: 8px !important;
  padding: 8px 16px !important;
  font-size: 14px !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 8px rgba(255, 193, 7, 0.3) !important;
}

.refresh-history-btn:hover {
  background: linear-gradient(145deg, #ffd54f, #ffc107) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 12px rgba(255, 193, 7, 0.4) !important;
}

.nav-button {
  background: transparent !important;
  color: #ffffff !important;
}

.nav-button:hover {
  background: rgba(255, 193, 7, 0.1) !important;
  color: #ffc107 !important;
}

.nav-button.active {
  background: linear-gradient(145deg, #ffc107, #ffa500) !important;
  color: #1a1a1a !important;
  border-radius: 18px !important;
  box-shadow: 0 8px 16px rgba(255, 193, 7, 0.4) !important;
  transform: translateY(-2px) !important;
}

.nav-button .cyber-icon {
  fill: #ffffff !important;
  stroke: #ffffff !important;
}

.nav-button:hover .cyber-icon {
  fill: #ffc107 !important;
  stroke: #ffc107 !important;
}

.nav-button.active .cyber-icon {
  fill: #1a1a1a !important;
  stroke: #1a1a1a !important;
}

.earn-block,
.friends-block {
  background: #2a2a2a !important;
  border: 1px solid #4a4a4a !important;
  border-top: 3px solid #ffc107 !important;
  color: #ffffff !important;
}

.earn-block h3,
.friends-block h3 {
  color: #ffffff !important;
}

.earn-block p,
.friends-block p {
  color: #ffffff !important;
}

.withdrawal-recommendations {
  background: #2a2a2a !important;
  border: 1px solid #4a4a4a !important;
  border-top: 3px solid #ffc107 !important;
  color: #ffffff !important;
}

.withdrawal-recommendations h4 {
  color: #ffffff !important;
}

.currency-info-card {
  background: #2a2a2a !important;
  border: 1px solid #4a4a4a !important;
  border-top: 3px solid #ffc107 !important;
  color: #ffffff !important;
}

.currency-tabs-container {
  background: #2a2a2a !important;
  border-top: 3px solid #ffc107 !important;
}

.calculator-header {
  color: #ffffff !important;
}

.amount-input-section {
  color: #ffffff !important;
}

.amount-input-section label {
  color: #ffffff !important;
}

.withdrawal-form {
  background: #2a2a2a !important;
  border: 1px solid #4a4a4a !important;
  border-radius: 21px;
  border-top: 3px solid #ffc107 !important;
  color: #ffffff !important;
}

.withdrawal-form label {
  color: #ffffff !important;
  margin-bottom: 8px !important;
  display: block !important;
  font-weight: 500 !important;
}

.withdrawal-form {
  padding: 20px !important;
}

.withdrawal-form > * {
  margin-bottom: 16px !important;
}

.withdrawal-form select,
.withdrawal-form input {
  width: 100% !important;
  padding: 12px !important;
  border-radius: 8px !important;
  margin-bottom: 16px !important;
}

.currency-tab {
  background: #333333 !important;
  color: #ffffff !important;
  border: 1px solid #4a4a4a !important;
}

.currency-tab.active {
  background: #ffc107 !important;
  color: #1a1a1a !important;
}

.currency-tab .tab-icon {
  color: #ffffff !important;
  fill: #ffffff !important;
  stroke: #ffffff !important;
  width: 24px !important;
  height: 24px !important;
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
  filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.3)) !important;
}

.currency-tab.active .tab-icon {
  color: #1a1a1a !important;
  fill: #1a1a1a !important;
  stroke: #1a1a1a !important;
  filter: drop-shadow(0 0 4px rgba(26, 26, 26, 0.3)) !important;
}

.currency-tab[data-currency="eth"] .tab-icon,
.currency-tab[data-currency="btc"] .tab-icon,
.currency-tab[data-currency="usdttrc20"] .tab-icon,
.currency-tab[data-currency="ton"] .tab-icon {
  color: #ffffff !important;
  fill: #ffffff !important;
  stroke: #ffffff !important;
  width: 24px !important;
  height: 24px !important;
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
  pointer-events: none !important;
  position: relative !important;
  z-index: 10 !important;
}

.currency-tab.active[data-currency="eth"] .tab-icon,
.currency-tab.active[data-currency="btc"] .tab-icon,
.currency-tab.active[data-currency="usdttrc20"] .tab-icon,
.currency-tab.active[data-currency="ton"] .tab-icon {
  color: #1a1a1a !important;
  fill: #1a1a1a !important;
  stroke: #1a1a1a !important;
}

.currency-icon,
#currency-card-icon {
  color: #ffffff !important;
  fill: #ffffff !important;
  stroke: #ffffff !important;
  width: 24px !important;
  height: 24px !important;
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
  filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.4)) !important;
  pointer-events: none !important;
  position: relative !important;
  z-index: 10 !important;
}

.currency-icon path,
#currency-card-icon path,
.currency-icon circle,
#currency-card-icon circle {
  fill: #ffffff !important;
  stroke: #ffffff !important;
  color: #ffffff !important;
}

.currency-title .currency-icon,
.currency-title #currency-card-icon {
  color: #ffffff !important;
  fill: #ffffff !important;
  stroke: #ffffff !important;
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

#currency-card-icon {
  color: #ffffff !important;
  width: 24px !important;
  height: 24px !important;
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
  filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.4)) !important;
  margin-right: 12px !important;
  background: transparent !important;
  font-size: 0 !important;
  text-indent: -9999px !important;
}

#currency-card-icon path,
#currency-card-icon circle,
#currency-card-icon rect,
#currency-card-icon line {
  stroke: #ffffff !important;
  fill: #ffffff !important;
}

#currency-card-icon path[fill="none"],
#currency-card-icon circle[fill="none"],
#currency-card-icon rect[fill="none"] {
  fill: none !important;
  stroke: #ffffff !important;
}

#currency-card-icon::before,
#currency-card-icon::after {
  display: none !important;
}

#currency-card-icon * {
  fill: #ffffff !important;
  stroke: #ffffff !important;
  color: #ffffff !important;
}

.currency-title {
  display: flex !important;
  align-items: center !important;
  gap: 15px !important;
}

.currency-title svg {
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.refresh-history-btn .refresh-icon {
  color: #1a1a1a !important;
  fill: none !important;
  stroke: #1a1a1a !important;
  stroke-width: 2 !important;
  width: 18px !important;
  height: 18px !important;
  margin-right: 8px !important;
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.refresh-history-btn .refresh-icon path {
  fill: none !important;
  stroke: #1a1a1a !important;
}

.refresh-icon {
  color: #ffffff !important;
  fill: none !important;
  stroke: #ffffff !important;
  stroke-width: 1.5 !important;
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.refresh-icon path {
  fill: none !important;
  stroke: #ffffff !important;
}

.action-button {
  background: linear-gradient(145deg, #ffc107, #ffa500) !important;
  color: #1a1a1a !important;
  border: none !important;
  box-shadow: 0 12px 24px rgba(255, 193, 7, 0.4),
    0 6px 12px rgba(255, 193, 7, 0.3) !important;
  overflow: hidden !important;
}

.action-button:hover {
  background: linear-gradient(145deg, #ffd54f, #ffc107) !important;
  box-shadow: 0 16px 32px rgba(255, 193, 7, 0.5),
    0 8px 16px rgba(255, 193, 7, 0.4) !important;
}

.action-button:active {
  background: linear-gradient(145deg, #ffa500, #ffc107) !important;
  box-shadow: 0 6px 12px rgba(255, 193, 7, 0.3),
    0 3px 6px rgba(255, 193, 7, 0.2) !important;
}

.action-button.purple-button,
.action-button.orange-button,
.action-button.yellow-button {
  background: linear-gradient(145deg, #ffc107, #ffa500) !important;
  box-shadow: 0 12px 24px rgba(255, 193, 7, 0.4),
    0 6px 12px rgba(255, 193, 7, 0.3) !important;
  overflow: hidden !important;
}

.action-button {
  position: relative !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 12px 50px 12px 20px !important;
  text-align: center !important;
}

.action-button .button-icon {
  position: absolute !important;
  left: 16px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  width: 20px !important;
  height: 20px !important;
  color: #1a1a1a !important;
  fill: none !important;
  stroke: #1a1a1a !important;
  stroke-width: 2 !important;
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
  margin: 0 !important;
  flex-shrink: 0 !important;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3)) !important;
}

.action-button .button-text {
  color: #1a1a1a !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  text-align: center !important;
  width: 100% !important;
  margin: 0 !important;
}

.error-message,
#withdrawal-error.error-message {
  background: rgba(255, 68, 68, 0.1) !important;
  color: #ff4444 !important;
  border: 1px solid rgba(255, 68, 68, 0.3) !important;
  border-left: 4px solid #ff4444 !important;
  border-radius: 8px !important;
  padding: 12px 16px !important;
  margin: 12px 0 !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  font-family: "Inter", sans-serif !important;
  box-shadow: 0 2px 8px rgba(255, 68, 68, 0.2) !important;
  display: block !important;
  position: relative !important;
}

.error-message::before,
#withdrawal-error.error-message::before {
  content: "⚠" !important;
  margin-right: 8px !important;
  font-size: 16px !important;
  font-weight: bold !important;
  font-family: "Arial", "Helvetica", sans-serif !important;
}

#withdrawal-error.success-message::before {
  content: "" !important;
  margin-right: 0 !important;
}

.success-message,
#withdrawal-error.success-message {
  background: rgba(76, 175, 80, 0.1) !important;
  color: #4caf50 !important;
  border: 1px solid rgba(76, 175, 80, 0.3) !important;
  border-left: 4px solid #4caf50 !important;
  border-radius: 8px !important;
  padding: 12px 16px !important;
  margin: 12px 0 !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  font-family: "Inter", sans-serif !important;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2) !important;
  display: block !important;
  position: relative !important;
  text-align: center !important;
}

.success-message::before {
  content: "\2713" !important;
  margin-right: 8px !important;
  font-size: 16px !important;
  font-weight: bold !important;
  font-family: "Arial", "Helvetica", sans-serif !important;
}

.warning-message {
  background: rgba(255, 170, 0, 0.1) !important;
  color: #ffaa00 !important;
  border: 1px solid rgba(255, 170, 0, 0.3) !important;
  border-left: 4px solid #ffaa00 !important;
  border-radius: 8px !important;
  padding: 12px 16px !important;
  margin: 12px 0 !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  font-family: "Inter", sans-serif !important;
  box-shadow: 0 2px 8px rgba(255, 170, 0, 0.2) !important;
}

.warning-message::before {
  content: "!" !important;
  margin-right: 8px !important;
  font-size: 16px !important;
  font-weight: bold !important;
  font-family: "Arial", "Helvetica", sans-serif !important;
  background: #ffaa00 !important;
  color: #000 !important;
  border-radius: 50% !important;
  width: 18px !important;
  height: 18px !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 12px !important;
}

.currency-requirements {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.requirement-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.requirement-section:not(:last-child) {
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.result-section {
  background: rgba(0, 255, 136, 0.05);
  border: 1px solid rgba(0, 255, 136, 0.2);
  border-radius: 8px;
  padding: 12px;
}

.result-section .requirement-value {
  color: #00ff88;
  font-weight: 700;
  text-shadow: 0 0 4px rgba(0, 255, 136, 0.3);
}

.warning-section {
  background: rgba(255, 107, 53, 0.08);
  border: 2px solid rgba(255, 107, 53, 0.3);
  border-radius: 10px;
  padding: 14px;
  position: relative;
}

.warning-section::before {
  content: "⚠";
  position: absolute;
  top: -8px;
  left: 12px;
  background: #2a2a2a;
  padding: 0 6px;
  font-size: 16px;
  color: #ff6b35;
  font-weight: bold;
  font-family: "Arial", "Helvetica", sans-serif;
}

.requirement-item.minimum-info {
  flex-direction: column !important;
  align-items: center !important;
  gap: 4px !important;
  text-align: center !important;
  margin-bottom: 16px !important;
}

.requirement-item.minimum-info .requirement-label {
  color: #ff6b35;
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 6px;
  text-align: center;
}

.requirement-item.minimum-info .requirement-value {
  color: #ff6b35;
  font-weight: 700;
  text-shadow: 0 0 4px rgba(255, 107, 53, 0.4);
  font-size: 16px;
  margin-left: 0 !important;
  text-align: center !important;
}

.requirement-item.missing-info {
  flex-direction: column !important;
  align-items: center !important;
  gap: 2px !important;
  text-align: center !important;
  margin-top: 8px !important;
}

.requirement-item.missing-info .requirement-label {
  color: #ffaa00;
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 4px;
  text-align: center;
}

.requirement-item.missing-info .requirement-value {
  color: #ffaa00;
  font-weight: 700;
  text-shadow: 0 0 4px rgba(255, 170, 0, 0.4);
  font-size: 16px;
  margin-left: 0 !important;
  text-align: center !important;
}

@media (max-width: 768px) {
  .requirement-item.minimum-info {
    margin-bottom: 20px !important;
  }

  .requirement-item.minimum-info .requirement-label {
    font-size: 15px;
    margin-bottom: 8px;
    line-height: 1.3;
  }

  .requirement-item.minimum-info .requirement-value {
    font-size: 15px;
    line-height: 1.3;
  }

  .requirement-item.missing-info {
    margin-top: 12px !important;
  }

  .requirement-item.missing-info .requirement-label {
    font-size: 15px;
    margin-bottom: 6px;
    line-height: 1.3;
  }

  .requirement-item.missing-info .requirement-value {
    font-size: 15px;
    line-height: 1.3;
  }

  .warning-section {
    padding: 16px;
    margin: 16px 0;
  }

  .result-section {
    padding: 14px;
    margin: 14px 0;
  }
}

@media (max-width: 480px) {
  .requirement-item.minimum-info {
    margin-bottom: 18px !important;
  }

  .requirement-item.minimum-info .requirement-label {
    font-size: 14px;
    margin-bottom: 7px;
  }

  .requirement-item.minimum-info .requirement-value {
    font-size: 14px;
  }

  .requirement-item.missing-info {
    margin-top: 10px !important;
  }

  .requirement-item.missing-info .requirement-label {
    font-size: 14px;
    margin-bottom: 5px;
  }

  .requirement-item.missing-info .requirement-value {
    font-size: 14px;
  }
}

@media (max-width: 360px) {
  .requirement-item.minimum-info {
    margin-bottom: 16px !important;
  }

  .requirement-item.minimum-info .requirement-label {
    font-size: 13px;
    margin-bottom: 6px;
  }

  .requirement-item.minimum-info .requirement-value {
    font-size: 13px;
  }

  .requirement-item.missing-info {
    margin-top: 8px !important;
  }

  .requirement-item.missing-info .requirement-label {
    font-size: 13px;
    margin-bottom: 4px;
  }

  .requirement-item.missing-info .requirement-value {
    font-size: 13px;
  }
}

.requirement-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 24px;
  line-height: 1.4;
  padding: 4px 0;
}

.requirement-label {
  flex: 1;
  font-size: 14px;
  color: #cccccc;
  font-weight: 500;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.requirement-value {
  flex-shrink: 0;
  text-align: right;
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  margin-left: 12px;
  line-height: 1.3;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

@media (max-width: 768px) {
  .requirement-item {
    padding: 6px 0;
    min-height: 28px;
  }

  .requirement-label {
    font-size: 14px;
    line-height: 1.4;
  }

  .requirement-value {
    font-size: 14px;
    line-height: 1.4;
    margin-left: 8px;
  }
}

@media (max-width: 480px) {
  .requirement-item {
    padding: 5px 0;
    min-height: 26px;
  }

  .requirement-label {
    font-size: 13px;
    line-height: 1.3;
  }

  .requirement-value {
    font-size: 13px;
    line-height: 1.3;
    margin-left: 6px;
  }
}

@media (max-width: 360px) {
  .requirement-item {
    padding: 4px 0;
    min-height: 24px;
  }

  .requirement-label {
    font-size: 12px;
    line-height: 1.3;
  }

  .requirement-value {
    font-size: 12px;
    line-height: 1.3;
    margin-left: 4px;
  }
}

.info-message {
  background: rgba(33, 150, 243, 0.1) !important;
  color: #2196f3 !important;
  border: 1px solid rgba(33, 150, 243, 0.3) !important;
  border-left: 4px solid #2196f3 !important;
  border-radius: 8px !important;
  padding: 12px 16px !important;
  margin: 12px 0 !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  font-family: "Inter", sans-serif !important;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.2) !important;
}

.info-message::before {
  content: "i" !important;
  margin-right: 8px !important;
  font-size: 14px !important;
  font-weight: bold !important;
  font-family: "Arial", "Helvetica", sans-serif !important;
  background: #2196f3 !important;
  color: #fff !important;
  border-radius: 50% !important;
  width: 18px !important;
  height: 18px !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

input,
select,
textarea {
  background: #333333 !important;
  color: #ffffff !important;
  border: 1px solid #4a4a4a !important;
}

input:focus,
select:focus,
textarea:focus {
  border-color: #ffc107 !important;
  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25) !important;
}

.glitch-line {
  display: none;
}

body::before {
  content: "" !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background: linear-gradient(
      135deg,
      transparent 0%,
      transparent 40%,
      rgba(255, 193, 7, 0.2) 40%,
      rgba(255, 193, 7, 0.2) 60%,
      transparent 60%
    ),
    linear-gradient(
      -45deg,
      transparent 0%,
      transparent 70%,
      rgba(255, 193, 7, 0.1) 70%,
      rgba(255, 193, 7, 0.1) 85%,
      transparent 85%
    ),
    repeating-linear-gradient(
      45deg,
      transparent,
      transparent 50px,
      rgba(255, 193, 7, 0.05) 50px,
      rgba(255, 193, 7, 0.05) 52px
    ) !important;
  z-index: -2 !important;
  opacity: 0.8 !important;
  animation: backgroundPulse 8s ease-in-out infinite !important;
  display: block !important;
}

body::after {
  content: "" !important;
  position: fixed !important;
  top: -50% !important;
  right: -20% !important;
  width: 80% !important;
  height: 200% !important;
  background: linear-gradient(
    45deg,
    transparent 0%,
    transparent 30%,
    #ffc107 30%,
    #ffc107 32%,
    transparent 32%
  ) !important;
  transform: rotate(15deg) scale(1) !important;
  z-index: -1 !important;
  opacity: 0.24 !important;
  animation: geometricFloatAndRotate 12s ease-in-out infinite !important;
  display: block !important;
}

@keyframes geometricFloatAndRotate {
  0% {
    transform: rotate(15deg) translateY(0px) scale(1);
    opacity: 0.24;
  }
  25% {
    transform: rotate(20deg) translateY(-20px) scale(1.05);
    opacity: 0.3;
  }
  50% {
    transform: rotate(25deg) translateY(-40px) scale(0.95);
    opacity: 0.18;
  }
  75% {
    transform: rotate(30deg) translateY(-15px) scale(1.02);
    opacity: 0.28;
  }
  100% {
    transform: rotate(35deg) translateY(0px) scale(1);
    opacity: 0.24;
  }
}

@keyframes backgroundPulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.02);
    opacity: 0.9;
  }
}

.app-container::before {
  content: "" !important;
  position: fixed !important;
  top: 10% !important;
  left: -5% !important;
  width: 120px !important;
  height: 120px !important;
  background: linear-gradient(
    45deg,
    rgba(255, 193, 7, 0.1),
    rgba(255, 193, 7, 0.2)
  ) !important;
  border-radius: 50% !important;
  z-index: -1 !important;
  animation: floatingCircle 10s ease-in-out infinite !important;
  display: block !important;
}

.app-container::after {
  content: "" !important;
  position: fixed !important;
  bottom: 15% !important;
  right: -8% !important;
  width: 80px !important;
  height: 80px !important;
  background: linear-gradient(
    135deg,
    rgba(255, 193, 7, 0.15),
    rgba(255, 193, 7, 0.25)
  ) !important;
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%) !important;
  z-index: -1 !important;
  animation: floatingTriangle 14s ease-in-out infinite reverse !important;
  display: block !important;
}

@keyframes floatingCircle {
  0%,
  100% {
    transform: translateY(0px) scale(1);
    opacity: 0.1;
  }
  50% {
    transform: translateY(-30px) scale(1.1);
    opacity: 0.2;
  }
}

@keyframes floatingTriangle {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.15;
  }
  50% {
    transform: translateY(25px) rotate(180deg);
    opacity: 0.25;
  }
}

.crypto-background {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  max-width: 480px;
  height: 100vh;
  z-index: -10;
  pointer-events: none;
  overflow: hidden;
}

.floating-crypto {
  position: absolute;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: rgba(255, 193, 7, 0.08);
  backdrop-filter: blur(2px);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 25px rgba(255, 193, 7, 0.2);
  border: 1px solid rgba(255, 193, 7, 0.15);
}

.floating-crypto svg {
  width: 60px;
  height: 60px;
  color: #ffc107;
  opacity: 0.6;
  filter: drop-shadow(0 0 10px rgba(255, 193, 7, 0.4));
}

.crypto-btc-1 {
  top: 12%;
  left: 8%;
  animation: cryptoRotateFloat 15s ease-in-out infinite;
}

.crypto-eth-1 {
  top: 18%;
  right: 12%;
  animation: cryptoRotateFloat 16s ease-in-out infinite;
}

.crypto-usdt-1 {
  top: 45%;
  left: 5%;
  animation: cryptoRotateFloat 17s ease-in-out infinite;
}

.crypto-ton-1 {
  top: 40%;
  right: 8%;
  animation: cryptoRotateFloat 14s ease-in-out infinite;
}

.crypto-dollar-1 {
  top: 50%;
  left: 50%;
  transform: translateX(-50%);
  animation: cryptoRotateFloat 19s ease-in-out infinite;
}

.crypto-btc-2 {
  bottom: 25%;
  left: 15%;
  width: 80px;
  height: 80px;
  animation: cryptoRotateFloatReverse 18s ease-in-out infinite;
}

.crypto-eth-2 {
  bottom: 20%;
  right: 20%;
  width: 90px;
  height: 90px;
  animation: cryptoRotateFloatReverse 20s ease-in-out infinite;
}

.crypto-dollar-2 {
  bottom: 12%;
  left: 45%;
  width: 85px;
  height: 85px;
  animation: cryptoRotateFloatReverse 21s ease-in-out infinite;
}

@keyframes cryptoRotateFloat {
  0% {
    transform: translateY(0px) scale(1) rotate(0deg);
    opacity: 0.5;
  }
  25% {
    transform: translateY(-50px) scale(1.1) rotate(90deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-80px) scale(0.95) rotate(180deg);
    opacity: 0.6;
  }
  75% {
    transform: translateY(-35px) scale(1.05) rotate(270deg);
    opacity: 0.65;
  }
  100% {
    transform: translateY(0px) scale(1) rotate(360deg);
    opacity: 0.5;
  }
}

@keyframes cryptoRotateFloatReverse {
  0% {
    transform: translateY(0px) scale(1) rotate(360deg);
    opacity: 0.45;
  }
  25% {
    transform: translateY(-40px) scale(1.15) rotate(270deg);
    opacity: 0.65;
  }
  50% {
    transform: translateY(-70px) scale(0.9) rotate(180deg);
    opacity: 0.55;
  }
  75% {
    transform: translateY(-25px) scale(1.08) rotate(90deg);
    opacity: 0.6;
  }
  100% {
    transform: translateY(0px) scale(1) rotate(0deg);
    opacity: 0.45;
  }
}

.theme-geometric .action-button {
  border-radius: 8px;
  background: var(--gradient-orange);
  overflow: hidden !important;
}
#calculator-section > div.amount-input-section > div.input-group > span {
  display: none;
}

.history-list.empty-state .empty-history-state,
.empty-history-state {
  background: linear-gradient(
    135deg,
    rgba(255, 107, 53, 0.08),
    rgba(255, 170, 0, 0.05)
  ) !important;
  border: 2px dashed rgba(255, 107, 53, 0.3) !important;
  border-radius: 16px !important;
  padding: 40px 20px !important;
  text-align: center !important;
  position: relative !important;
  overflow: hidden !important;
  margin: 20px 0 !important;
}

.empty-history-state::before {
  content: "" !important;
  position: absolute !important;
  top: 0 !important;
  left: -100% !important;
  width: 100% !important;
  height: 100% !important;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 107, 53, 0.1),
    transparent
  ) !important;
  animation: shimmerEmpty 3s infinite !important;
}

.empty-history-icon {
  width: 64px !important;
  height: 64px !important;
  margin: 0 auto 20px !important;
  background: rgba(255, 107, 53, 0.15) !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  position: relative !important;
  border: 2px solid rgba(255, 107, 53, 0.3) !important;
}

.empty-history-icon svg {
  width: 32px !important;
  height: 32px !important;
  color: #ff6b35 !important;
  filter: drop-shadow(0 0 8px rgba(255, 107, 53, 0.4)) !important;
  fill: none !important;
  stroke: #ff6b35 !important;
}

.empty-history-content {
  max-width: 300px !important;
  margin: 0 auto !important;
}

.empty-history-title {
  font-size: 18px !important;
  font-weight: 700 !important;
  color: #ffffff !important;
  margin: 0 0 12px 0 !important;
  text-shadow: 0 0 4px rgba(255, 255, 255, 0.2) !important;
}

.empty-history-description {
  font-size: 14px !important;
  color: #cccccc !important;
  margin: 0 0 20px 0 !important;
  line-height: 1.5 !important;
}

.empty-history-hint {
  background: rgba(255, 170, 0, 0.1) !important;
  border: 1px solid rgba(255, 170, 0, 0.3) !important;
  border-radius: 10px !important;
  padding: 12px 16px !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  font-size: 13px !important;
  color: #ffaa00 !important;
  text-align: left !important;
  font-weight: 500 !important;
}

.hint-icon {
  content: "\1F4A1" !important;
  font-size: 16px;
  flex-shrink: 0;
}

@keyframes shimmerEmpty {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@media (max-width: 480px) {
  .empty-history-state {
    padding: 30px 16px;
    margin: 16px 0;
  }

  .empty-history-icon {
    width: 56px;
    height: 56px;
    margin-bottom: 16px;
  }

  .empty-history-icon svg {
    width: 28px;
    height: 28px;
  }

  .empty-history-title {
    font-size: 16px;
  }

  .empty-history-hint {
    padding: 10px 12px;
    font-size: 12px;
  }

  .requirement-item {
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 4px !important;
  }

  .requirement-value {
    text-align: left !important;
    margin-left: 0 !important;
    font-size: 13px !important;
    line-height: 1.4 !important;
  }

  .requirement-label {
    font-size: 13px !important;
    margin-bottom: 2px !important;
  }
}
