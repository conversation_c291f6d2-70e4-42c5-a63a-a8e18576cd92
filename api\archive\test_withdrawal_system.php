<?php
/**
 * test_withdrawal_system.php
 * Скрипт для эмуляции системы выплат с изолированными данными
 */

// Создаем временную папку для теста
$tmpDir = __DIR__ . '/test_temp';
if (!file_exists($tmpDir)) mkdir($tmpDir);

// Копируем необходимые файлы
copy(__DIR__ . '/api/config.php', $tmpDir . '/config.php');
copy(__DIR__ . '/api/db_mock.php', $tmpDir . '/db_mock.php');
copy(__DIR__ . '/api/NOWPaymentsAPI.php', $tmpDir . '/NOWPaymentsAPI.php');

// Модифицируем config.php для тестов
file_put_contents($tmpDir . '/config.php', 
    str_replace(
        "__DIR__ . '/user_data.json'", 
        "'$tmpDir/user_data.json'", 
        file_get_contents($tmpDir . '/config.php')
    )
);

// Подключаем зависимости из временной папки
require_once $tmpDir . '/config.php';
require_once $tmpDir . '/db_mock.php';
require_once $tmpDir . '/NOWPaymentsAPI.php';

// Эмуляция NOWPayments API
class TestNOWPaymentsAPI extends NOWPaymentsAPI {
    public function createPayoutWithAutoConversion($address, $currency, $amount) {
        return [
            'id' => 'test_payout_'.time(),
            'status' => 'pending',
            'amount' => $amount,
            'currency' => $currency
        ];
    }
    
    public function verifyIPNSignature($data, $signature) {
        return true;
    }
}

// Переопределяем API для тестов
$GLOBALS['NOWPaymentsAPI'] = new TestNOWPaymentsAPI(
    NOWPAYMENTS_API_KEY, 
    NOWPAYMENTS_PUBLIC_KEY, 
    NOWPAYMENTS_IPN_SECRET, 
    NOWPAYMENTS_API_URL
);

// Тестовые данные
$testUserId = 123456789;
$testAmount = 1000;
$testCryptoAddress = 'TTestAddress1234567890';
$testCurrency = 'usdttrc20';

// Инициализируем тестового пользователя
file_put_contents($tmpDir . '/user_data.json', json_encode([
    $testUserId => [
        'balance' => $testAmount,
        'telegram_data' => ['id' => $testUserId]
    ]
]));

// 1. Тест создания выплаты
echo "=== Тест создания выплаты ===\n";
$input = [
    'initData' => json_encode(['user' => ['id' => $testUserId]]),
    'amount' => $testAmount,
    'crypto_address' => $testCryptoAddress,
    'crypto_currency' => $testCurrency
];

$inputJSON = json_encode($input);
file_put_contents('php://input', $inputJSON);
$_SERVER['CONTENT_TYPE'] = 'application/json';

ob_start();
include __DIR__ . '/api/requestWithdrawal.php';
$response = json_decode(ob_get_clean(), true);
print_r($response);

if ($response['success']) {
    echo "✅ Запрос на выплату успешно создан\n";
    $withdrawalId = $response['withdrawal_data']['id'];
} else {
    echo "❌ Ошибка создания выплаты\n";
    exit;
}

// 2. Тест callback успешной выплаты
echo "\n=== Тест успешной выплаты ===\n";
$callbackData = [
    'id' => $withdrawalId,
    'status' => 'confirmed',
    'extra_id' => $testUserId,
    'amount' => 10,
    'currency' => $testCurrency
];

$_SERVER['HTTP_X_NOWPAYMENTS_SIG'] = 'test_signature';
file_put_contents('php://input', json_encode($callbackData));

include __DIR__ . '/api/withdrawal_callback.php';
echo "✅ Callback успешной выплаты обработан\n";

// 3. Проверка обновления баланса
$userData = json_decode(file_get_contents($tmpDir . '/user_data.json'), true);
$userBalance = $userData[$testUserId]['balance'] ?? -1;
$withdrawalStatus = $userData[$testUserId]['withdrawals'][0]['status'] ?? '';

echo "\n=== Результаты теста ===\n";
echo "Начальный баланс: $testAmount монет\n";
echo "Текущий баланс: $userBalance монет\n";
echo "Статус выплаты: $withdrawalStatus\n";

if ($userBalance == 0 && $withdrawalStatus === 'confirmed') {
    echo "✅ Тест пройден успешно! Баланс списан, статус обновлен\n";
} else {
    echo "❌ Тест не пройден!\n";
}

// Очистка
exec("rm -rf $tmpDir");
?>