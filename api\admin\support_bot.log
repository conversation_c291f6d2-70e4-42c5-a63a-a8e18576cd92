[2025-06-30 17:39:02] INFO: Отправка запроса к Telegram API: getMe
[2025-06-30 17:39:03] SUCCESS: Запрос к Telegram API выполнен успешно
[2025-06-30 17:39:03] INFO: Отправка запроса к Telegram API: getWebhookInfo
[2025-06-30 17:39:04] SUCCESS: Запрос к Telegram API выполнен успешно
[2025-06-30 17:39:08] INFO: Отправка запроса к Telegram API: setWebhook
[2025-06-30 17:39:09] SUCCESS: Запрос к Telegram API выполнен успешно
[2025-06-30 17:39:09] INFO: Webhook успешно установлен через админку
[2025-06-30 15:10:53] INFO: Получен update: {"update_id":249108941,"message":{"message_id":40,"from":{"id":5880288830,"is_bot":false,"first_name":"Альтер","last_name":"Эго","username":"alter_mega_ego","language_code":"ru"},"chat":{"id":5880288830,"first_name":"Альтер","last_name":"Эго","username":"alter_mega_ego","type":"private"},"date":1751296253,"text":"test"}}
[2025-06-30 15:10:53] INFO: Сообщение от пользователя 5880288830 (Альтер): test
[2025-06-30 15:10:53] INFO: Отправка запроса к Telegram API: sendMessage
[2025-06-30 15:10:53] SUCCESS: Запрос к Telegram API выполнен успешно
[2025-06-30 15:10:53] INFO: Сообщение сохранено в чат chat_5880288830_1751296253, язык: ru
[2025-06-30 15:11:23] INFO: Отправка запроса к Telegram API: sendMessage
[2025-06-30 15:11:23] SUCCESS: Запрос к Telegram API выполнен успешно
[2025-06-30 15:11:23] INFO: Сообщение отправлено из админки в чат chat_5880288830_1751296253: ты чё за тип вообще?
[2025-06-30 15:14:12] INFO: Получен update: {"update_id":249108942,"message":{"message_id":43,"from":{"id":5880288830,"is_bot":false,"first_name":"Альтер","last_name":"Эго","username":"alter_mega_ego","language_code":"ru"},"chat":{"id":5880288830,"first_name":"Альтер","last_name":"Эго","username":"alter_mega_ego","type":"private"},"date":1751296452,"text":"какой то хитрый вопрос от негра"}}
[2025-06-30 15:14:12] INFO: Сообщение от пользователя 5880288830 (Альтер): какой то хитрый вопрос от негра
[2025-06-30 15:14:12] INFO: Отправка запроса к Telegram API: sendMessage
[2025-06-30 15:14:12] SUCCESS: Запрос к Telegram API выполнен успешно
[2025-06-30 15:14:12] INFO: Сообщение сохранено в чат chat_5880288830_1751296253, язык: ru
[2025-06-30 15:25:36] INFO: Отправка запроса к Telegram API: sendMessage
[2025-06-30 15:25:36] SUCCESS: Запрос к Telegram API выполнен успешно
[2025-06-30 15:25:36] INFO: Сообщение отправлено из админки в чат chat_5880288830_1751296253: test
[2025-06-30 15:26:14] INFO: Получен update: {"update_id":249108943,"message":{"message_id":46,"from":{"id":5880288830,"is_bot":false,"first_name":"Альтер","last_name":"Эго","username":"alter_mega_ego","language_code":"ru"},"chat":{"id":5880288830,"first_name":"Альтер","last_name":"Эго","username":"alter_mega_ego","type":"private"},"date":1751297174,"text":"супер хитрый вопрос от индуса, для композитора!"}}
[2025-06-30 15:26:14] INFO: Сообщение от пользователя 5880288830 (Альтер): супер хитрый вопрос от индуса, для композитора!
[2025-06-30 15:26:14] INFO: Отправка запроса к Telegram API: sendMessage
[2025-06-30 15:26:14] SUCCESS: Запрос к Telegram API выполнен успешно
[2025-06-30 15:26:14] INFO: Сообщение сохранено в чат chat_5880288830_1751296253, язык: ru
[2025-06-30 15:26:23] INFO: Получен update: {"update_id":249108944,"callback_query":{"id":"6808904145747409893","from":{"id":5880288830,"is_bot":false,"first_name":"Альтер","last_name":"Эго","username":"alter_mega_ego","language_code":"ru"},"message":{"message_id":37,"from":{"id":7820736321,"is_bot":true,"first_name":"UniQPaid support","username":"uniqpaid_support_bot"},"chat":{"id":5880288830,"first_name":"Альтер","last_name":"Эго","username":"alter_mega_ego","type":"private"},"date":1751295903,"text":"Добро пожаловать в службу поддержки UniQPaid! 👋\n\n🎧 Вам ответит первый освободившийся оператор поддержки!\n\nЗдесь вы можете:\n• Задать вопрос о работе приложения\n• Сообщить о проблеме\n• Получить помощь с выводом средств\n\nПросто напишите ваш вопрос, и мы обязательно вам поможем!","reply_markup":{"inline_keyboard":[[{"text":"❓ Часто задаваемые вопросы","callback_data":"faq"},{"text":"💰 Помощь с выводом","callback_data":"withdrawal_help"}],[{"text":"🔗 Перейти в основное приложение","url":"https:\/\/t.me\/uniqpaid_paid_bot"}]]}},"chat_instance":"7001972393523781297","data":"faq"}}
[2025-06-30 15:26:23] INFO: Callback query от пользователя 5880288830: faq
[2025-06-30 15:26:23] INFO: Отправка запроса к Telegram API: sendMessage
[2025-06-30 15:26:23] SUCCESS: Запрос к Telegram API выполнен успешно
[2025-06-30 15:26:23] INFO: Отправлен FAQ на языке ru
[2025-06-30 15:26:23] INFO: Отправка запроса к Telegram API: answerCallbackQuery
[2025-06-30 15:26:23] SUCCESS: Запрос к Telegram API выполнен успешно
[2025-06-30 15:54:04] INFO: Получен update: {"update_id":249108947,"message":{"message_id":53,"from":{"id":5880288830,"is_bot":false,"first_name":"Альтер","last_name":"Эго","username":"alter_mega_ego","language_code":"ru"},"chat":{"id":5880288830,"first_name":"Альтер","last_name":"Эго","username":"alter_mega_ego","type":"private"},"date":1751298844,"text":"\/start","entities":[{"offset":0,"length":6,"type":"bot_command"}]}}
[2025-06-30 15:54:04] INFO: Сообщение от пользователя 5880288830 (Альтер): /start
[2025-06-30 15:54:04] INFO: Отправка запроса к Telegram API: sendMessage
[2025-06-30 15:54:04] SUCCESS: Запрос к Telegram API выполнен успешно
[2025-06-30 15:54:04] INFO: Отправлено приветствие пользователю 5880288830 на языке ru
[2025-06-30 15:54:17] INFO: Получен update: {"update_id":249108948,"callback_query":{"id":"6808904144283797486","from":{"id":5880288830,"is_bot":false,"first_name":"Альтер","last_name":"Эго","username":"alter_mega_ego","language_code":"ru"},"message":{"message_id":54,"from":{"id":7820736321,"is_bot":true,"first_name":"UniQPaid support","username":"uniqpaid_support_bot"},"chat":{"id":5880288830,"first_name":"Альтер","last_name":"Эго","username":"alter_mega_ego","type":"private"},"date":1751298844,"text":"Добро пожаловать в службу поддержки UniQPaid! 👋\n\n🎧 Вам ответит первый освободившийся оператор поддержки!\n\nЗдесь вы можете:\n• Задать вопрос о работе приложения\n• Сообщить о проблеме\n• Получить помощь с выводом средств\n\nПросто напишите ваш вопрос, и мы обязательно вам поможем!","reply_markup":{"inline_keyboard":[[{"text":"❓ Часто задаваемые вопросы","callback_data":"faq"},{"text":"💰 Помощь с выводом","callback_data":"withdrawal_help"}],[{"text":"🔗 Перейти в основное приложение","url":"https:\/\/t.me\/uniqpaid_paid_bot"}]]}},"chat_instance":"7001972393523781297","data":"faq"}}
[2025-06-30 15:54:17] INFO: Callback query от пользователя 5880288830: faq
[2025-06-30 15:54:17] INFO: Отправка запроса к Telegram API: sendMessage
[2025-06-30 15:54:19] SUCCESS: Запрос к Telegram API выполнен успешно
[2025-06-30 15:54:19] INFO: Отправлен FAQ на языке ru
[2025-06-30 15:54:19] INFO: Отправка запроса к Telegram API: answerCallbackQuery
[2025-06-30 15:54:19] SUCCESS: Запрос к Telegram API выполнен успешно
[2025-06-30 15:55:01] INFO: Получен update: {"update_id":249108949,"message":{"message_id":56,"from":{"id":5880288830,"is_bot":false,"first_name":"Альтер","last_name":"Эго","username":"alter_mega_ego","language_code":"ru"},"chat":{"id":5880288830,"first_name":"Альтер","last_name":"Эго","username":"alter_mega_ego","type":"private"},"date":1751298901,"text":"Класскный бот, наверное клёвый мастер его делал?"}}
[2025-06-30 15:55:01] INFO: Сообщение от пользователя 5880288830 (Альтер): Класскный бот, наверное клёвый мастер его делал?
[2025-06-30 15:55:01] INFO: Отправка запроса к Telegram API: sendMessage
[2025-06-30 15:55:02] SUCCESS: Запрос к Telegram API выполнен успешно
[2025-06-30 15:55:02] INFO: Сообщение сохранено в чат chat_5880288830_1751296253, язык: ru
[2025-06-30 16:04:02] INFO: Получен update: {"update_id":249108950,"callback_query":{"id":"6808904143576949930","from":{"id":5880288830,"is_bot":false,"first_name":"Альтер","last_name":"Эго","username":"alter_mega_ego","language_code":"ru"},"message":{"message_id":54,"from":{"id":7820736321,"is_bot":true,"first_name":"UniQPaid support","username":"uniqpaid_support_bot"},"chat":{"id":5880288830,"first_name":"Альтер","last_name":"Эго","username":"alter_mega_ego","type":"private"},"date":1751298844,"text":"Добро пожаловать в службу поддержки UniQPaid! 👋\n\n🎧 Вам ответит первый освободившийся оператор поддержки!\n\nЗдесь вы можете:\n• Задать вопрос о работе приложения\n• Сообщить о проблеме\n• Получить помощь с выводом средств\n\nПросто напишите ваш вопрос, и мы обязательно вам поможем!","reply_markup":{"inline_keyboard":[[{"text":"❓ Часто задаваемые вопросы","callback_data":"faq"},{"text":"💰 Помощь с выводом","callback_data":"withdrawal_help"}],[{"text":"🔗 Перейти в основное приложение","url":"https:\/\/t.me\/uniqpaid_paid_bot"}]]}},"chat_instance":"7001972393523781297","data":"faq"}}
[2025-06-30 16:04:02] INFO: Callback query от пользователя 5880288830: faq
[2025-06-30 16:04:02] INFO: Отправка запроса к Telegram API: sendMessage
[2025-06-30 16:04:03] SUCCESS: Запрос к Telegram API выполнен успешно
[2025-06-30 16:04:03] INFO: Отправлен FAQ на языке ru
[2025-06-30 16:04:03] INFO: Отправка запроса к Telegram API: answerCallbackQuery
[2025-06-30 16:04:04] SUCCESS: Запрос к Telegram API выполнен успешно
[2025-06-30 16:15:46] INFO: Получен update: {"update_id":249108951,"message":{"message_id":59,"from":{"id":5880288830,"is_bot":false,"first_name":"Альтер","last_name":"Эго","username":"alter_mega_ego","language_code":"ru"},"chat":{"id":5880288830,"first_name":"Альтер","last_name":"Эго","username":"alter_mega_ego","type":"private"},"date":1751300146,"text":"супер хитрый вопрос от индуса, для композитора!"}}
[2025-06-30 16:15:46] INFO: Сообщение от пользователя 5880288830 (Альтер): супер хитрый вопрос от индуса, для композитора!
[2025-06-30 16:15:46] INFO: Отправка запроса к Telegram API: sendMessage
[2025-06-30 16:15:46] SUCCESS: Запрос к Telegram API выполнен успешно
[2025-06-30 16:15:46] INFO: Сообщение сохранено в чат chat_5880288830_1751296253, язык: ru
[2025-06-30 17:26:32] INFO: Получен update: {"update_id":249108952,"message":{"message_id":61,"from":{"id":5880288830,"is_bot":false,"first_name":"Альтер","last_name":"Эго","username":"alter_mega_ego","language_code":"ru"},"chat":{"id":5880288830,"first_name":"Альтер","last_name":"Эго","username":"alter_mega_ego","type":"private"},"date":1751304391,"text":"\/start","entities":[{"offset":0,"length":6,"type":"bot_command"}]}}
[2025-06-30 17:26:32] INFO: Сообщение от пользователя 5880288830 (Альтер): /start
[2025-06-30 17:26:32] INFO: Отправка запроса к Telegram API: sendMessage
[2025-06-30 17:26:32] SUCCESS: Запрос к Telegram API выполнен успешно
[2025-06-30 17:26:32] INFO: Отправлено приветствие пользователю 5880288830 на языке ru
[2025-06-30 17:26:58] INFO: Получен update: {"update_id":249108953,"message":{"message_id":63,"from":{"id":5880288830,"is_bot":false,"first_name":"Альтер","last_name":"Эго","username":"alter_mega_ego","language_code":"ru"},"chat":{"id":5880288830,"first_name":"Альтер","last_name":"Эго","username":"alter_mega_ego","type":"private"},"date":1751304418,"text":"Тестовый впорос для композитора"}}
[2025-06-30 17:26:58] INFO: Сообщение от пользователя 5880288830 (Альтер): Тестовый впорос для композитора
[2025-06-30 17:26:58] INFO: Отправка запроса к Telegram API: sendMessage
[2025-06-30 17:26:59] SUCCESS: Запрос к Telegram API выполнен успешно
[2025-06-30 17:26:59] INFO: Сообщение сохранено в чат chat_5880288830_1751296253, язык: ru
[2025-06-30 19:06:52] INFO: Получен update: {"update_id":249108954,"callback_query":{"id":"6808904143965510939","from":{"id":5880288830,"is_bot":false,"first_name":"Альтер","last_name":"Эго","username":"alter_mega_ego","language_code":"ru"},"message":{"message_id":62,"from":{"id":7820736321,"is_bot":true,"first_name":"UniQPaid support","username":"uniqpaid_support_bot"},"chat":{"id":5880288830,"first_name":"Альтер","last_name":"Эго","username":"alter_mega_ego","type":"private"},"date":1751304392,"text":"Добро пожаловать в службу поддержки UniQPaid! 👋\n\n🎧 Вам ответит первый освободившийся оператор поддержки!\n\nЗдесь вы можете:\n• Задать вопрос о работе приложения\n• Сообщить о проблеме\n• Получить помощь с выводом средств\n\nПросто напишите ваш вопрос, и мы обязательно вам поможем!","reply_markup":{"inline_keyboard":[[{"text":"❓ Часто задаваемые вопросы","callback_data":"faq"},{"text":"💰 Помощь с выводом","callback_data":"withdrawal_help"}],[{"text":"🔗 Перейти в основное приложение","url":"https:\/\/t.me\/uniqpaid_paid_bot"}]]}},"chat_instance":"7001972393523781297","data":"faq"}}
[2025-06-30 19:06:52] INFO: Callback query от пользователя 5880288830: faq
[2025-06-30 19:06:52] INFO: Отправка запроса к Telegram API: sendMessage
[2025-06-30 19:06:53] SUCCESS: Запрос к Telegram API выполнен успешно
[2025-06-30 19:06:53] INFO: Отправлен FAQ на языке ru
[2025-06-30 19:06:53] INFO: Отправка запроса к Telegram API: answerCallbackQuery
[2025-06-30 19:06:53] SUCCESS: Запрос к Telegram API выполнен успешно
[2025-07-01 08:23:33] INFO: Получен update: {"update_id":249108955,"message":{"message_id":66,"from":{"id":5880288830,"is_bot":false,"first_name":"Альтер","last_name":"Эго","username":"alter_mega_ego","language_code":"ru"},"chat":{"id":5880288830,"first_name":"Альтер","last_name":"Эго","username":"alter_mega_ego","type":"private"},"date":1751358213,"text":"\/start","entities":[{"offset":0,"length":6,"type":"bot_command"}]}}
[2025-07-01 08:23:33] INFO: Сообщение от пользователя 5880288830 (Альтер): /start
[2025-07-01 08:23:33] INFO: Отправка запроса к Telegram API: sendMessage
[2025-07-01 08:23:34] SUCCESS: Запрос к Telegram API выполнен успешно
[2025-07-01 08:23:34] INFO: Отправлено приветствие пользователю 5880288830 на языке ru
[2025-07-01 08:23:50] INFO: Получен update: {"update_id":249108956,"message":{"message_id":68,"from":{"id":5880288830,"is_bot":false,"first_name":"Альтер","last_name":"Эго","username":"alter_mega_ego","language_code":"ru"},"chat":{"id":5880288830,"first_name":"Альтер","last_name":"Эго","username":"alter_mega_ego","type":"private"},"date":1751358230,"text":"Тест"}}
[2025-07-01 08:23:50] INFO: Сообщение от пользователя 5880288830 (Альтер): Тест
[2025-07-01 08:23:50] INFO: Отправка запроса к Telegram API: sendMessage
[2025-07-01 08:23:51] SUCCESS: Запрос к Telegram API выполнен успешно
[2025-07-01 08:23:51] INFO: Сообщение сохранено в чат chat_5880288830_1751296253, язык: ru
[2025-07-01 21:29:23] INFO: Получен update: {"update_id":249108957,"message":{"message_id":70,"from":{"id":405669007,"is_bot":false,"first_name":"Oleg","username":"UniqPaid","language_code":"ru"},"chat":{"id":405669007,"first_name":"Oleg","username":"UniqPaid","type":"private"},"date":1751405363,"text":"Проверка связи"}}
[2025-07-01 21:29:23] INFO: Сообщение от пользователя 405669007 (Oleg): Проверка связи
[2025-07-01 21:29:23] INFO: Отправка запроса к Telegram API: sendMessage
[2025-07-01 21:29:23] SUCCESS: Запрос к Telegram API выполнен успешно
[2025-07-01 21:29:23] INFO: Сообщение сохранено в чат chat_405669007_1751405363, язык: ru
[2025-07-02 03:44:15] INFO: Отправка запроса к Telegram API: sendMessage
[2025-07-02 03:44:15] SUCCESS: Запрос к Telegram API выполнен успешно
[2025-07-02 03:44:15] INFO: Сообщение отправлено из админки в чат chat_405669007_1751405363: Здравствуйте дорогой композитор, какой у вас вопрос?
[2025-07-02 04:55:37] INFO: Получен update: {"update_id":249108958,"message":{"message_id":73,"from":{"id":405669007,"is_bot":false,"first_name":"Oleg","username":"UniqPaid","language_code":"ru"},"chat":{"id":405669007,"first_name":"Oleg","username":"UniqPaid","type":"private"},"date":1751432137,"text":"Вопросов больше нет)"}}
[2025-07-02 04:55:37] INFO: Сообщение от пользователя 405669007 (Oleg): Вопросов больше нет)
[2025-07-02 04:55:37] INFO: Отправка запроса к Telegram API: sendMessage
[2025-07-02 04:55:37] SUCCESS: Запрос к Telegram API выполнен успешно
[2025-07-02 04:55:37] INFO: Сообщение сохранено в чат chat_405669007_1751405363, язык: ru
[2025-07-02 07:53:43] INFO: Получен update: {"update_id":249108959,"callback_query":{"id":"6808904143087633125","from":{"id":5880288830,"is_bot":false,"first_name":"Альтер","last_name":"Эго","username":"alter_mega_ego","language_code":"ru"},"message":{"message_id":67,"from":{"id":7820736321,"is_bot":true,"first_name":"UniQPaid support","username":"uniqpaid_support_bot"},"chat":{"id":5880288830,"first_name":"Альтер","last_name":"Эго","username":"alter_mega_ego","type":"private"},"date":1751358214,"text":"Добро пожаловать в службу поддержки UniQPaid! 👋\n\n🎧 Вам ответит первый освободившийся оператор поддержки!\n\nЗдесь вы можете:\n• Задать вопрос о работе приложения\n• Сообщить о проблеме\n• Получить помощь с выводом средств\n\nПросто напишите ваш вопрос, и мы обязательно вам поможем!","reply_markup":{"inline_keyboard":[[{"text":"❓ Часто задаваемые вопросы","callback_data":"faq"},{"text":"💰 Помощь с выводом","callback_data":"withdrawal_help"}],[{"text":"🔗 Перейти в основное приложение","url":"https:\/\/t.me\/uniqpaid_paid_bot"}]]}},"chat_instance":"7001972393523781297","data":"withdrawal_help"}}
[2025-07-02 07:53:43] INFO: Callback query от пользователя 5880288830: withdrawal_help
[2025-07-02 07:53:43] INFO: Отправка запроса к Telegram API: sendMessage
[2025-07-02 07:53:43] SUCCESS: Запрос к Telegram API выполнен успешно
[2025-07-02 07:53:43] INFO: Отправлена помощь с выводом на языке ru
[2025-07-02 07:53:43] INFO: Отправка запроса к Telegram API: answerCallbackQuery
[2025-07-02 07:53:43] SUCCESS: Запрос к Telegram API выполнен успешно
