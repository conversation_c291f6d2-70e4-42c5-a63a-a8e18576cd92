/**
 * === ad-counters.js ===
 * Модуль для управления счетчиками показов рекламы
 */

class AdCountersManager {
  constructor() {
    this.dailyLimits = {
      'native_banner': 20,
      'rewarded_video': 20,
      'interstitial': 20
    };

    this.adTypeMapping = {
      'native_banner': 'native-banner-counter',
      'rewarded_video': 'rewarded-video-counter',
      'interstitial': 'interstitial-counter'
    };

    this.localization = null;
    this.isInitialized = false;
  }

  /**
   * Инициализация менеджера счетчиков
   */
  async init() {
    if (this.isInitialized) {
      console.warn('[AdCounters] Уже инициализирован');
      return;
    }

    // Ждем загрузки локализации
    await this.waitForLocalization();

    // Очищаем старые данные счетчиков
    this.cleanupOldCounters();

    // Обновляем все счетчики
    this.updateAllCounters();

    this.isInitialized = true;
    console.log('[AdCounters] ✅ Инициализирован');
  }

  /**
   * Ожидание загрузки системы локализации
   */
  async waitForLocalization() {
    let attempts = 0;
    const maxAttempts = 50; // 5 секунд максимум

    while (attempts < maxAttempts) {
      if (window.appLocalization && window.appLocalization.isLoaded) {
        this.localization = window.appLocalization;
        console.log('[AdCounters] 🌐 Локализация найдена');
        return;
      }
      
      await new Promise(resolve => setTimeout(resolve, 100));
      attempts++;
    }

    console.warn('[AdCounters] ⚠️ Локализация не найдена, используем дефолтные тексты');
  }

  /**
   * Получает текущую дату в UTC формате YYYY-MM-DD
   */
  getCurrentUTCDate() {
    const now = new Date();
    const utcYear = now.getUTCFullYear();
    const utcMonth = String(now.getUTCMonth() + 1).padStart(2, '0');
    const utcDay = String(now.getUTCDate()).padStart(2, '0');
    return `${utcYear}-${utcMonth}-${utcDay}`;
  }

  /**
   * Получает ключ для localStorage для определенного типа рекламы
   */
  getAdCountKey(adType) {
    const todayUTC = this.getCurrentUTCDate();
    return `ad_count_${adType}_${todayUTC}`;
  }

  /**
   * Получает количество просмотренной рекламы определенного типа за сегодня
   */
  getTodayAdCount(adType) {
    const key = this.getAdCountKey(adType);
    const count = localStorage.getItem(key);
    return count ? parseInt(count, 10) : 0;
  }

  /**
   * Получает оставшееся количество показов для типа рекламы
   */
  getRemainingCount(adType) {
    const currentCount = this.getTodayAdCount(adType);
    const limit = this.dailyLimits[adType] || 20;
    return Math.max(0, limit - currentCount);
  }

  /**
   * Форматирует текст счетчика с учетом локализации
   */
  formatCounterText(count, language = 'ru') {
    if (!this.localization) {
      // Дефолтные тексты если локализация не загружена
      return language === 'en' 
        ? `${count} ad views left`
        : `осталось ${count} показов`;
    }

    if (language === 'en') {
      return this.localization.get('tasks.ad_views_left', { count });
    }

    // Для русского языка учитываем склонения
    if (count === 1) {
      return this.localization.get('tasks.ad_views_left_single', { count });
    } else if (count >= 2 && count <= 4) {
      return this.localization.get('tasks.ad_views_left_few', { count });
    } else {
      return this.localization.get('tasks.ad_views_left', { count });
    }
  }

  /**
   * Обновляет счетчик для определенного типа рекламы
   */
  updateCounter(adType) {
    const counterId = this.adTypeMapping[adType];
    if (!counterId) {
      console.warn(`[AdCounters] Неизвестный тип рекламы: ${adType}`);
      return;
    }

    const counterElement = document.getElementById(counterId);
    if (!counterElement) {
      console.warn(`[AdCounters] Элемент счетчика не найден: ${counterId}`);
      return;
    }

    const remainingCount = this.getRemainingCount(adType);
    const currentLanguage = this.localization?.currentLanguage || 'ru';
    const counterText = this.formatCounterText(remainingCount, currentLanguage);

    counterElement.textContent = counterText;

    // Добавляем класс для стилизации если лимит достигнут
    if (remainingCount === 0) {
      counterElement.classList.add('limit-reached');
    } else {
      counterElement.classList.remove('limit-reached');
    }

    console.log(`[AdCounters] Обновлен счетчик ${adType}: ${counterText}`);
  }

  /**
   * Обновляет все счетчики
   */
  updateAllCounters() {
    // Проверяем и очищаем старые данные при каждом обновлении
    this.cleanupOldCounters();

    Object.keys(this.adTypeMapping).forEach(adType => {
      this.updateCounter(adType);
    });
    console.log('[AdCounters] 🔄 Все счетчики обновлены');
  }

  /**
   * Увеличивает счетчик после успешного просмотра рекламы
   */
  incrementCounter(adType) {
    const key = this.getAdCountKey(adType);
    const currentCount = this.getTodayAdCount(adType);
    const newCount = currentCount + 1;
    
    localStorage.setItem(key, newCount.toString());
    
    // Обновляем отображение
    this.updateCounter(adType);
    
    console.log(`[AdCounters] 📊 Счетчик ${adType} увеличен: ${newCount}/${this.dailyLimits[adType]}`);
    
    return newCount;
  }

  /**
   * Проверяет, достигнут ли лимит для определенного типа рекламы
   */
  isLimitReached(adType) {
    const currentCount = this.getTodayAdCount(adType);
    const limit = this.dailyLimits[adType] || 20;
    return currentCount >= limit;
  }

  /**
   * Обновляет язык интерфейса
   */
  updateLanguage(language) {
    console.log(`[AdCounters] 🌐 Обновление языка на: ${language}`);
    this.updateAllCounters();
  }

  /**
   * Сброс всех счетчиков (для тестирования)
   */
  resetAllCounters() {
    Object.keys(this.dailyLimits).forEach(adType => {
      const key = this.getAdCountKey(adType);
      localStorage.removeItem(key);
    });
    this.updateAllCounters();
    console.log('[AdCounters] 🔄 Все счетчики сброшены');
  }

  /**
   * Очищает старые данные счетчиков (старше текущего дня UTC)
   */
  cleanupOldCounters() {
    const currentUTCDate = this.getCurrentUTCDate();
    const keysToRemove = [];

    // Проходим по всем ключам localStorage
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);

      // Ищем ключи счетчиков рекламы
      if (key && key.startsWith('ad_count_')) {
        // Извлекаем дату из ключа (формат: ad_count_TYPE_YYYY-MM-DD)
        const datePart = key.split('_').slice(-3).join('-'); // Последние 3 части после разделения по _

        // Если дата не совпадает с текущей UTC датой, помечаем для удаления
        if (datePart !== currentUTCDate) {
          keysToRemove.push(key);
        }
      }
    }

    // Удаляем старые ключи
    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
      console.log(`[AdCounters] 🗑️ Удален старый счетчик: ${key}`);
    });

    if (keysToRemove.length > 0) {
      console.log(`[AdCounters] 🔄 Очищено ${keysToRemove.length} старых счетчиков`);
    } else {
      console.log(`[AdCounters] ✅ Старых счетчиков не найдено`);
    }
  }

  /**
   * Получает время до следующего дня UTC
   */
  getTimeUntilNextDayUTC() {
    const now = new Date();
    const nextDayUTC = new Date(now);
    nextDayUTC.setUTCDate(now.getUTCDate() + 1);
    nextDayUTC.setUTCHours(0, 0, 0, 0);

    const msUntilNextDay = nextDayUTC.getTime() - now.getTime();
    const hours = Math.floor(msUntilNextDay / (1000 * 60 * 60));
    const minutes = Math.floor((msUntilNextDay % (1000 * 60 * 60)) / (1000 * 60));

    return {
      milliseconds: msUntilNextDay,
      hours,
      minutes,
      nextResetTime: nextDayUTC.toISOString()
    };
  }

  /**
   * Получает информацию о всех лимитах
   */
  getAllLimitsInfo() {
    const info = {};
    const timeUntilReset = this.getTimeUntilNextDayUTC();

    Object.keys(this.dailyLimits).forEach(adType => {
      const current = this.getTodayAdCount(adType);
      const limit = this.dailyLimits[adType];
      const remaining = Math.max(0, limit - current);

      info[adType] = {
        current,
        limit,
        remaining,
        isLimitReached: remaining === 0
      };
    });

    // Добавляем информацию о времени до сброса
    info._resetInfo = {
      currentUTCDate: this.getCurrentUTCDate(),
      timeUntilReset: timeUntilReset,
      nextResetTime: timeUntilReset.nextResetTime
    };

    return info;
  }
}

// Создаем глобальный экземпляр
window.adCountersManager = new AdCountersManager();

// Экспортируем для модульной системы
if (typeof module !== 'undefined' && module.exports) {
  module.exports = AdCountersManager;
}

console.log('[AdCounters] 📊 Модуль загружен');
