# Конвертация SVG в PNG для Telegram Bot

## Проблема
Telegram Bot API **НЕ поддерживает SVG файлы** для метода `sendPhoto`!

Поддерживаемые форматы:
- ✅ JPEG
- ✅ PNG  
- ✅ GIF
- ✅ WEBP
- ❌ SVG (НЕ поддерживается!)

## Решение
Нужно конвертировать `bot_welcome_banner.svg` в `bot_welcome_banner.png`

## Способы конвертации

### 1. Онлайн конвертеры
- https://convertio.co/svg-png/
- https://cloudconvert.com/svg-to-png
- https://www.freeconvert.com/svg-to-png

### 2. Photoshop/GIMP
1. Откройте `images/bot_welcome_banner.svg`
2. Экспортируйте как PNG
3. Рекомендуемый размер: 800x400px или больше
4. Сохраните как `images/bot_welcome_banner.png`

### 3. Командная строка (если есть ImageMagick)
```bash
convert images/bot_welcome_banner.svg images/bot_welcome_banner.png
```

### 4. Inkscape
```bash
inkscape --export-type=png --export-filename=bot_welcome_banner.png bot_welcome_banner.svg
```

## После конвертации

1. Загрузите `bot_welcome_banner.png` в папку `images/` на сервер
2. Убедитесь, что файл доступен по URL: 
   `https://app.uniqpaid.com/test3/images/bot_welcome_banner.png`
3. Загрузите обновленный `bot/webhook.php` на сервер
4. Протестируйте бота - картинка должна появиться!

## Проверка
После загрузки файлов:
1. Откройте в браузере: https://app.uniqpaid.com/test3/images/bot_welcome_banner.png
2. Отправьте `/start` боту в Telegram
3. Картинка должна появиться в сообщении!

## Альтернативное решение
Если конвертация невозможна, можно использовать красивое текстовое сообщение с эмодзи:

```php
// Красивый заголовок с эмодзи вместо картинки
$welcomeHeader = "🎨🚀💎 <b>UniQPaid - Криптозаработок</b> 💎🚀🎨\n\n";
$fullWelcomeText = $welcomeHeader . $welcomeText;
sendMessage($chatId, $fullWelcomeText, $keyboard);
```
