<?php
/**
 * Финальный тест системы истории выплат
 */

header('Content-Type: text/plain; charset=utf-8');

echo "=== ФИНАЛЬНЫЙ ТЕСТ СИСТЕМЫ ИСТОРИИ ВЫПЛАТ ===\n\n";

$testUserId = 5880288830;

// 1. Проверяем данные в файле
echo "1. ПРОВЕРКА ДАННЫХ В ФАЙЛЕ:\n";
echo str_repeat("-", 40) . "\n";

require_once __DIR__ . '/functions.php';

$userData = loadUserData();
if (isset($userData[$testUserId])) {
    $user = $userData[$testUserId];
    echo "✅ Пользователь найден: {$user['first_name']} {$user['last_name']}\n";
    echo "Баланс: {$user['balance']} монет\n";
    
    if (isset($user['withdrawals'])) {
        echo "Выплат в файле: " . count($user['withdrawals']) . "\n";
        foreach ($user['withdrawals'] as $i => $withdrawal) {
            echo "  " . ($i + 1) . ". " . ($withdrawal['coins_amount'] ?? 'N/A') . " монет (" . ($withdrawal['status'] ?? 'N/A') . ") - " . ($withdrawal['currency'] ?? 'N/A') . "\n";
        }
    } else {
        echo "❌ Нет поля withdrawals\n";
    }
} else {
    echo "❌ Пользователь не найден\n";
}

echo "\n";

// 2. Тестируем API с разными типами данных
echo "2. ТЕСТ API С РАЗНЫМИ ДАННЫМИ:\n";
echo str_repeat("-", 40) . "\n";

$testCases = [
    [
        'name' => 'Реальные initData',
        'data' => [
            'initData' => "user=" . urlencode(json_encode([
                'id' => $testUserId,
                'first_name' => 'Альтер',
                'last_name' => 'Эго',
                'username' => 'alter_mega_ego',
                'language_code' => 'ru'
            ])) . "&auth_date=" . time() . "&hash=test"
        ]
    ],
    [
        'name' => 'Fallback данные',
        'data' => [
            'initData' => "user=" . urlencode(json_encode([
                'id' => $testUserId,
                'first_name' => 'User'
            ])) . "&auth_date=" . time() . "&hash=fallback"
        ]
    ],
    [
        'name' => 'Минимальные данные',
        'data' => [
            'initData' => "user=" . urlencode(json_encode([
                'id' => $testUserId
            ])) . "&auth_date=" . time() . "&hash=minimal"
        ]
    ]
];

foreach ($testCases as $i => $testCase) {
    echo ($i + 1) . ". " . $testCase['name'] . ":\n";
    
    $postData = json_encode($testCase['data']);
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/json',
            'content' => $postData
        ]
    ]);
    
    try {
        $response = file_get_contents('http://argun-defolt.loc/api/getWithdrawalHistory.php', false, $context);
        
        if ($response === false) {
            echo "   ❌ Ошибка запроса\n";
        } else {
            $decoded = json_decode($response, true);
            if ($decoded && isset($decoded['withdrawals'])) {
                echo "   ✅ Получено выплат: " . count($decoded['withdrawals']) . "\n";
                if (count($decoded['withdrawals']) > 0) {
                    echo "   Первая: " . $decoded['withdrawals'][0]['coins_amount'] . " монет (" . $decoded['withdrawals'][0]['status'] . ")\n";
                }
            } elseif ($decoded && isset($decoded['error'])) {
                echo "   ❌ Ошибка API: " . $decoded['error'] . "\n";
            } else {
                echo "   ❌ Неожиданный ответ\n";
            }
        }
    } catch (Exception $e) {
        echo "   ❌ Исключение: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

// 3. Проверяем функцию getUserWithdrawals
echo "3. ТЕСТ ФУНКЦИИ getUserWithdrawals:\n";
echo str_repeat("-", 40) . "\n";

$withdrawals = getUserWithdrawals($testUserId);
echo "Функция вернула: " . count($withdrawals) . " выплат\n";

if (count($withdrawals) > 0) {
    echo "Детали выплат:\n";
    foreach ($withdrawals as $i => $withdrawal) {
        echo "  " . ($i + 1) . ". ID: " . ($withdrawal['id'] ?? 'N/A') . "\n";
        echo "     Сумма: " . ($withdrawal['coins_amount'] ?? 'N/A') . " монет\n";
        echo "     Статус: " . ($withdrawal['status'] ?? 'N/A') . "\n";
        echo "     Валюта: " . ($withdrawal['currency'] ?? 'N/A') . "\n";
        echo "     Адрес: " . ($withdrawal['wallet_address'] ?? $withdrawal['address'] ?? 'N/A') . "\n";
        echo "     Дата: " . (isset($withdrawal['timestamp']) ? date('Y-m-d H:i:s', $withdrawal['timestamp']) : ($withdrawal['created_at'] ?? 'N/A')) . "\n";
        echo "\n";
    }
}

// 4. Итоговая сводка
echo "4. ИТОГОВАЯ СВОДКА:\n";
echo str_repeat("-", 40) . "\n";

$dataInFile = isset($userData[$testUserId]['withdrawals']) ? count($userData[$testUserId]['withdrawals']) : 0;
$dataFromFunction = count($withdrawals);

echo "Выплат в файле: {$dataInFile}\n";
echo "Выплат из функции: {$dataFromFunction}\n";

if ($dataInFile > 0 && $dataFromFunction > 0 && $dataInFile === $dataFromFunction) {
    echo "✅ ВСЕ ТЕСТЫ ПРОЙДЕНЫ УСПЕШНО!\n";
    echo "✅ Система истории выплат работает корректно\n";
    echo "✅ В мини-приложении должны отображаться {$dataInFile} выплаты\n";
} else {
    echo "❌ Обнаружены проблемы в системе\n";
}

echo "\n=== ТЕСТ ЗАВЕРШЕН ===\n";
?>
