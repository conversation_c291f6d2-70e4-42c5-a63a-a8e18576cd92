<?php
/**
 * api/admin/bot_settings.php
 * Финальная, очищенная версия страницы настроек бота
 */

// Безопасность и аутентификация
require_once __DIR__ . '/auth.php';
session_start();
if (!isAuthenticated()) {
    header('Location: login.php');
    exit;
}

// Зависимости
require_once __DIR__ . '/../config.php';

$message = '';
$error = '';

// --- Пути к файлам настроек ---
$botTextsFile = __DIR__ . '/../../bot/bot_texts.json';
$buttonVisibilityFile = __DIR__ . '/../../bot/button_visibility.json';
$configFile = __DIR__ . '/../config.php';

// --- Обработка сохранения настроек ---
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // 1. Сохранение настроек бота в config.php
    try {
        $configContent = file_get_contents($configFile);
        if ($configContent === false) throw new Exception("Не удалось прочитать config.php");

        $post_config = $_POST['config'] ?? [];
        foreach ($post_config as $key => $value) {
            $pattern = "/define\(\'" . preg_quote($key, '/') . "\',\s*.*\);/";
            $replacement = "define('" . $key . "', '" . addslashes($value) . "');";
            $configContent = preg_replace($pattern, $replacement, $configContent, 1);
        }

        if (file_put_contents($configFile, $configContent) === false) {
            throw new Exception("Не удалось записать в config.php");
        }
        $message .= 'Настройки бота сохранены. ';

    } catch (Exception $e) {
        $error .= 'Ошибка сохранения настроек: ' . $e->getMessage() . ' ';
    }

    // 2. Сохранение видимости кнопок
    // Используем жестко заданный список ключей, чтобы избежать ошибок, если файл пуст или поврежден
    $buttonKeys = ['launch_app', 'my_balance', 'friends', 'statistics', 'help'];
    $buttons_data = [];
    foreach ($buttonKeys as $key) {
        // Если чекбокс отмечен, он будет в POST. Если нет, то нет.
        $buttons_data[$key] = isset($_POST['buttons'][$key]);
    }
    if (file_put_contents($buttonVisibilityFile, json_encode($buttons_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE))) {
        $message .= 'Настройки видимости кнопок сохранены. ';
    } else {
        $error .= 'Ошибка сохранения видимости кнопок. ';
    }

    // 3. Сохранение текстов бота
    if (isset($_POST['texts'])) {
        $updatedTexts = json_decode(file_get_contents($botTextsFile), true);
        foreach ($_POST['texts'] as $lang => $groups) {
            foreach ($groups as $key_path => $text) {
                $keys = explode('[', str_replace(']', '', $key_path));
                $temp = &$updatedTexts[$lang];
                foreach ($keys as $key) {
                    if (!isset($temp[$key])) $temp[$key] = [];
                    $temp = &$temp[$key];
                }
                $temp = $text;
            }
        }
        if (file_put_contents($botTextsFile, json_encode($updatedTexts, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE))) {
            $message .= 'Тексты бота сохранены.';
        } else {
            $error .= 'Ошибка сохранения текстов бота.';
        }
    }
    
    if (function_exists('opcache_invalidate')) opcache_invalidate($configFile, true);
    header("Location: " . $_SERVER['PHP_SELF'] . "?save=success");
    exit;
}

if(isset($_GET['save']) && $_GET['save'] == 'success'){
    $message = "Настройки успешно сохранены!";
}

// --- Загрузка текущих настроек ---
$botTexts = file_exists($botTextsFile) ? json_decode(file_get_contents($botTextsFile), true) : [];
$defaultButtons = ['launch_app' => true, 'my_balance' => true, 'friends' => true, 'statistics' => true, 'help' => true];
$buttonVisibility = file_exists($buttonVisibilityFile) ? json_decode(file_get_contents($buttonVisibilityFile), true) : $defaultButtons;

$botSettings = [
    'TELEGRAM_BOT_TOKEN' => defined('TELEGRAM_BOT_TOKEN') ? TELEGRAM_BOT_TOKEN : '',
    'BOT_USERNAME' => defined('BOT_USERNAME') ? BOT_USERNAME : '',
    'WEBAPP_URL' => defined('WEBAPP_URL') ? WEBAPP_URL : '',
];

include 'templates/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'templates/sidebar.php'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Настройки Telegram Бота</h1>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($message); ?><button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($error); ?><button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <form method="POST">
                <ul class="nav nav-tabs" id="main-tabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="settings-tab" data-bs-toggle="tab" data-bs-target="#settings-panel" type="button" role="tab">⚙️ Настройки Бота</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="texts-tab" data-bs-toggle="tab" data-bs-target="#texts-panel" type="button" role="tab">✏️ Тексты Бота</button>
                    </li>
                </ul>

                <div class="tab-content p-3 border border-top-0" id="main-tabs-content">
                    <div class="tab-pane fade show active" id="settings-panel" role="tabpanel">
                        <div class="card mb-4">
                            <div class="card-header"><strong>Основные параметры</strong></div>
                            <div class="card-body">
                                <div class="mb-3"><label for="cfg_TELEGRAM_BOT_TOKEN" class="form-label">BOT_TOKEN</label><input type="text" class="form-control" id="cfg_TELEGRAM_BOT_TOKEN" name="config[TELEGRAM_BOT_TOKEN]" value="<?= htmlspecialchars($botSettings['TELEGRAM_BOT_TOKEN']) ?>"></div>
                                <div class="mb-3"><label for="cfg_BOT_USERNAME" class="form-label">BOT_USERNAME</label><input type="text" class="form-control" id="cfg_BOT_USERNAME" name="config[BOT_USERNAME]" value="<?= htmlspecialchars($botSettings['BOT_USERNAME']) ?>"></div>
                                <div class="mb-3"><label for="cfg_WEBAPP_URL" class="form-label">WEBAPP_URL</label><input type="text" class="form-control" id="cfg_WEBAPP_URL" name="config[WEBAPP_URL]" value="<?= htmlspecialchars($botSettings['WEBAPP_URL']) ?>"></div>
                            </div>
                        </div>

                        <div class="card mb-4">
                            <div class="card-header"><strong>Видимость кнопок в меню</strong></div>
                            <div class="card-body">
                                <div class="row">
                                <?php foreach ($buttonVisibility as $key => $isVisible): ?>
                                    <div class="col-md-4">
                                        <div class="form-check form-switch mb-2">
                                            <input class="form-check-input" type="checkbox" id="button_<?= $key ?>" name="buttons[<?= $key ?>]" <?= $isVisible ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="button_<?= $key ?>"><?= ucfirst(str_replace('_', ' ', $key)) ?></label>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="tab-pane fade" id="texts-panel" role="tabpanel">
                        <ul class="nav nav-pills mb-3" id="text-lang-tabs" role="tablist">
                            <li class="nav-item" role="presentation"><button class="nav-link active" id="ru-text-tab" data-bs-toggle="tab" data-bs-target="#ru-texts" type="button" role="tab">Русский (RU)</button></li>
                            <li class="nav-item" role="presentation"><button class="nav-link" id="en-text-tab" data-bs-toggle="tab" data-bs-target="#en-texts" type="button" role="tab">English (EN)</button></li>
                        </ul>
                        <div class="tab-content" id="text-lang-tabs-content">
                            <div class="tab-pane fade show active" id="ru-texts" role="tabpanel"><?php render_text_accordion('ru', $botTexts); ?></div>
                            <div class="tab-pane fade" id="en-texts" role="tabpanel"><?php render_text_accordion('en', $botTexts); ?></div>
                        </div>
                    </div>
                </div>

                <div class="mt-4 mb-5">
                    <button type="submit" class="btn btn-primary w-100 p-3"><strong>Сохранить все изменения</strong></button>
                </div>
            </form>
        </main>
    </div>
</div>

<?php
function render_text_accordion($lang, $texts) {
    if (!isset($texts[$lang])) return;
    echo '<div class="accordion" id="accordion-' . $lang . '">';
    $i = 0;
    foreach ($texts[$lang] as $group_key => $group_values) {
        if (!is_array($group_values)) continue;
        echo '<div class="accordion-item">';
        echo '<h2 class="accordion-header" id="h-' . $lang . '-' . $i . '"><button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#c-' . $lang . '-' . $i . '" aria-expanded="false"><strong>' . ucfirst($group_key) . '</strong></button></h2>';
        echo '<div id="c-' . $lang . '-' . $i . '" class="accordion-collapse collapse" data-bs-parent="#accordion-' . $lang . '"><div class="accordion-body">';
        render_text_fields_recursive($lang, $group_values, $group_key);
        echo '</div></div></div>';
        $i++;
    }
    echo '</div>';
}

function render_text_fields_recursive($lang, $values, $parent_key) {
    foreach ($values as $key => $value) {
        $full_key = $parent_key . '[' . $key . ']';
        if (is_array($value)) {
            echo '<div class="ms-3 border-start ps-3 pt-2 mb-2">';
            echo '<p class="fw-bold text-primary">' . ucfirst($key) . '</p>';
            render_text_fields_recursive($lang, $value, $full_key);
            echo '</div>';
        } else {
            $id = 'text_' . $lang . '_' . str_replace(['[', ']'], ['_', ''], $full_key);
            echo '<div class="mb-3">';
            echo '<label for="' . $id . '" class="form-label"><small class="text-muted">' . $full_key . '</small></label>';
            echo '<textarea class="form-control form-control-sm" id="' . $id . '" name="texts[' . $lang . '][' . $full_key . ']" rows="2">' . htmlspecialchars($value) . '</textarea>';
            echo '</div>';
        }
    }
}

include 'templates/footer.php';
?>
