<?php
/**
 * Комплексное тестирование системы выплат (БЕЗ реальных транзакций)
 * Тестирует создание выплат, получение статусов, отображение в истории
 */

require_once 'config.php';
require_once 'FeeCalculator.php';
require_once 'NOWPaymentsAPI.php';

echo "🧪 КОМПЛЕКСНОЕ ТЕСТИРОВАНИЕ СИСТЕМЫ ВЫПЛАТ\n";
echo str_repeat("=", 60) . "\n\n";

// Тестовые данные пользователя
$testUserId = 999999999; // Тестовый ID
$testUserData = [
    $testUserId => [
        'balance' => 100000, // Достаточно для всех тестов
        'withdrawals' => []
    ]
];

// Тестовые кейсы для выплат
$testCases = [
    [
        'name' => 'ETH - Стандартная сумма',
        'coins' => 1000,
        'currency' => 'eth',
        'address' => '******************************************',
        'expected_success' => true
    ],
    [
        'name' => 'BTC - Большая сумма',
        'coins' => 5000,
        'currency' => 'btc', 
        'address' => '**********************************',
        'expected_success' => true
    ],
    [
        'name' => 'TON - Минимальная сумма',
        'coins' => 2000,
        'currency' => 'ton',
        'address' => 'EQD4FPq-PRDieyQKkizFTRtSDyucUIqrj0v_zXJmqaDp6_0t',
        'expected_success' => true
    ],
    [
        'name' => 'USDT TRC20 - Средняя сумма',
        'coins' => 15000,
        'currency' => 'usdttrc20',
        'address' => 'TLa2f6VPqDgRE67v1736s7bJ8Ray5wYjU7',
        'expected_success' => true
    ],
    [
        'name' => 'ETH - Недостаточная сумма',
        'coins' => 100,
        'currency' => 'eth',
        'address' => '******************************************',
        'expected_success' => false
    ],
    [
        'name' => 'BTC - Неверный адрес',
        'coins' => 2000,
        'currency' => 'btc',
        'address' => 'invalid_address_123',
        'expected_success' => false
    ]
];

echo "🎯 ТЕСТ 1: СОЗДАНИЕ ТЕСТОВЫХ ВЫПЛАТ\n";
echo str_repeat("-", 40) . "\n\n";

$testResults = [];
$feeCalculator = FeeCalculator::getInstance();

foreach ($testCases as $i => $testCase) {
    echo "📊 Тест " . ($i + 1) . ": {$testCase['name']}\n";
    echo "   💰 Сумма: {$testCase['coins']} монет\n";
    echo "   🪙 Валюта: {$testCase['currency']}\n";
    echo "   📍 Адрес: " . substr($testCase['address'], 0, 20) . "...\n";
    
    // 1. Проверяем расчет через единый калькулятор
    echo "\n   1️⃣ Расчет через FeeCalculator:\n";
    $calculationResult = $feeCalculator->calculateWithdrawalAmount($testCase['coins'], $testCase['currency']);
    
    if ($calculationResult['success']) {
        echo "      ✅ Расчет успешен\n";
        echo "      💰 К получению: {$calculationResult['crypto_amount']} {$testCase['currency']}\n";
        echo "      💵 USD: \${$calculationResult['usd_amount']}\n";
        echo "      💸 Комиссия: {$calculationResult['nowpayments_fee']} {$testCase['currency']}\n";
        
        $calculatedAmount = $calculationResult['crypto_amount'];
    } else {
        echo "      ❌ Ошибка расчета: {$calculationResult['error']}\n";
        $calculatedAmount = null;
    }
    
    // 2. Тестируем создание выплаты (имитация)
    echo "\n   2️⃣ Создание тестовой выплаты:\n";
    $withdrawalResult = testCreateWithdrawal(
        $testUserId,
        $testCase['coins'],
        $testCase['currency'],
        $testCase['address'],
        $calculatedAmount,
        $testUserData
    );
    
    if ($withdrawalResult['success']) {
        echo "      ✅ Выплата создана успешно\n";
        echo "      🆔 ID выплаты: {$withdrawalResult['withdrawal_id']}\n";
        echo "      📊 Статус: {$withdrawalResult['status']}\n";
        echo "      💰 Сумма: {$withdrawalResult['crypto_amount']} {$testCase['currency']}\n";
        
        // Сохраняем результат для дальнейших тестов
        $testResults[] = [
            'test_case' => $testCase,
            'calculation' => $calculationResult,
            'withdrawal' => $withdrawalResult,
            'success' => true
        ];
        
    } else {
        echo "      ❌ Ошибка создания: {$withdrawalResult['error']}\n";
        
        $testResults[] = [
            'test_case' => $testCase,
            'calculation' => $calculationResult,
            'withdrawal' => $withdrawalResult,
            'success' => false
        ];
    }
    
    // 3. Проверяем соответствие ожиданиям
    echo "\n   3️⃣ Проверка ожиданий:\n";
    $actualSuccess = $withdrawalResult['success'];
    $expectedSuccess = $testCase['expected_success'];
    
    if ($actualSuccess === $expectedSuccess) {
        echo "      ✅ Результат соответствует ожиданиям\n";
    } else {
        echo "      ⚠️ Результат НЕ соответствует ожиданиям!\n";
        echo "      📊 Ожидалось: " . ($expectedSuccess ? 'успех' : 'ошибка') . "\n";
        echo "      📊 Получено: " . ($actualSuccess ? 'успех' : 'ошибка') . "\n";
    }
    
    echo "\n" . str_repeat("=", 60) . "\n\n";
}

// Сводка результатов
echo "📈 СВОДКА РЕЗУЛЬТАТОВ ТЕСТИРОВАНИЯ\n";
echo str_repeat("-", 40) . "\n";

$successfulTests = array_filter($testResults, function($result) {
    return $result['success'] === $result['test_case']['expected_success'];
});

$totalTests = count($testResults);
$passedTests = count($successfulTests);

echo "📊 Всего тестов: {$totalTests}\n";
echo "✅ Прошло успешно: {$passedTests}\n";
echo "❌ Провалилось: " . ($totalTests - $passedTests) . "\n";
echo "📈 Процент успеха: " . round(($passedTests / $totalTests) * 100, 1) . "%\n\n";

if ($passedTests === $totalTests) {
    echo "🎉 ВСЕ ТЕСТЫ ПРОШЛИ УСПЕШНО!\n";
} else {
    echo "⚠️ Некоторые тесты провалились, требуется проверка\n";
}

/**
 * Имитирует создание выплаты без реальных API вызовов
 */
function testCreateWithdrawal($userId, $coinsAmount, $currency, $address, $calculatedAmount, &$userData) {
    try {
        // Базовая валидация
        if ($coinsAmount <= 0) {
            return ['success' => false, 'error' => 'Некорректная сумма'];
        }
        
        if (empty($address)) {
            return ['success' => false, 'error' => 'Не указан адрес'];
        }
        
        // Валидация адреса
        if (!validateCryptoAddress($address, $currency)) {
            return ['success' => false, 'error' => 'Неверный формат адреса'];
        }
        
        // Проверка баланса
        if (!isset($userData[$userId]) || $userData[$userId]['balance'] < $coinsAmount) {
            return ['success' => false, 'error' => 'Недостаточно средств'];
        }
        
        // Проверка минимальной суммы через калькулятор
        $feeCalculator = FeeCalculator::getInstance();
        $calculation = $feeCalculator->calculateWithdrawalAmount($coinsAmount, $currency);
        
        if (!$calculation['success']) {
            return ['success' => false, 'error' => $calculation['error']];
        }
        
        // Имитируем успешное создание выплаты
        $withdrawalId = 'test_' . time() . '_' . rand(1000, 9999);
        $status = 'waiting'; // Начальный статус
        
        // Списываем монеты с баланса
        $userData[$userId]['balance'] -= $coinsAmount;
        
        // Добавляем выплату в историю
        $withdrawalData = [
            'id' => $withdrawalId,
            'payout_id' => $withdrawalId,
            'status' => $status,
            'coins_amount' => $coinsAmount,
            'usd_amount' => $calculation['usd_amount'],
            'crypto_amount' => $calculatedAmount ?: $calculation['crypto_amount'],
            'crypto_amount_gross' => $calculation['crypto_amount_gross'],
            'nowpayments_fee' => $calculation['nowpayments_fee'],
            'currency' => $currency,
            'address' => $address,
            'calculation_method' => 'unified_fee_calculator',
            'timestamp' => time(),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
            'test_mode' => true
        ];
        
        $userData[$userId]['withdrawals'][] = $withdrawalData;
        
        return [
            'success' => true,
            'withdrawal_id' => $withdrawalId,
            'status' => $status,
            'crypto_amount' => $withdrawalData['crypto_amount'],
            'new_balance' => $userData[$userId]['balance'],
            'withdrawal_data' => $withdrawalData
        ];
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => 'Ошибка: ' . $e->getMessage()];
    }
}

/**
 * Валидация адресов криптовалют
 */
function validateCryptoAddress($address, $currency) {
    switch ($currency) {
        case 'eth':
            return preg_match('/^0x[a-fA-F0-9]{40}$/', $address);
        case 'btc':
            return preg_match('/^(1|3|bc1)[a-zA-Z0-9]{25,58}$/', $address);
        case 'ton':
            return preg_match('/^[UEkuek]Q[A-Za-z0-9_-]{42,46}$/', $address) ||
                   preg_match('/^[0-9a-fA-F]{64}$/', $address);
        case 'usdttrc20':
            return preg_match('/^T[a-zA-Z0-9]{33}$/', $address);
        default:
            return strlen($address) >= 20;
    }
}

echo "\n🎯 ТЕСТ СОЗДАНИЯ ВЫПЛАТ ЗАВЕРШЕН!\n";
?>
