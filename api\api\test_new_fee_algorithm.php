<?php
/**
 * Тест нового алгоритма комиссий
 * Проверяет что калькулятор показывает правильные суммы и NOWPayments получает правильные данные
 */

// Подключаем зависимости
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/NOWPaymentsAPI.php';
require_once __DIR__ . '/FeeCalculator.php';

echo "<h1>🧪 Тест нового алгоритма комиссий</h1>\n";
echo "<style>body{font-family:Arial;margin:20px;} .test{border:1px solid #ddd;padding:10px;margin:10px 0;} .success{background:#e8f5e9;} .error{background:#ffebee;} .info{background:#e3f2fd;}</style>\n";

// Тестовые данные
$testCases = [
    ['coins' => 1000, 'currency' => 'ton', 'description' => '1000 монет -> TON'],
    ['coins' => 5000, 'currency' => 'eth', 'description' => '5000 монет -> ETH'],
    ['coins' => 2000, 'currency' => 'btc', 'description' => '2000 монет -> BTC'],
    ['coins' => 3000, 'currency' => 'usdttrc20', 'description' => '3000 монет -> USDT']
];

try {
    // Создаем экземпляры
    $api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);
    $calculator = new FeeCalculator($api);
    
    echo "<div class='info test'><strong>Тестируем новый алгоритм комиссий:</strong><br>";
    echo "✅ Калькулятор показывает сумму ПОСЛЕ вычета комиссии (что получит пользователь)<br>";
    echo "✅ В NOWPayments отправляется сумма + комиссия<br>";
    echo "✅ Пользователь получает именно ту сумму, которую видел в калькуляторе</div>";
    
    foreach ($testCases as $test) {
        echo "<div class='test'>";
        echo "<h3>📊 {$test['description']}</h3>";
        
        // Тестируем новый калькулятор
        $result = $calculator->calculateWithdrawalAmount($test['coins'], $test['currency']);
        
        if ($result['success']) {
            echo "<div class='success'>";
            echo "<strong>✅ Расчет успешен (метод: {$result['calculation_method']})</strong><br>";
            echo "💰 Пользователь получит: <strong>{$result['crypto_amount']} {$result['currency']}</strong><br>";
            
            if (isset($result['crypto_amount_for_nowpayments'])) {
                echo "🏦 Отправим в NOWPayments: <strong>{$result['crypto_amount_for_nowpayments']} {$result['currency']}</strong><br>";
                echo "💸 Комиссия NOWPayments: <strong>{$result['nowpayments_fee']} {$result['currency']}</strong><br>";
                
                // Проверяем математику
                $userAmount = $result['crypto_amount'];
                $nowpaymentsAmount = $result['crypto_amount_for_nowpayments'];
                $fee = $result['nowpayments_fee'];
                $calculatedUserAmount = $nowpaymentsAmount - $fee;
                
                echo "<br><strong>🔍 Проверка математики:</strong><br>";
                echo "NOWPayments получит: {$nowpaymentsAmount}<br>";
                echo "Комиссия: {$fee}<br>";
                echo "Пользователь получит: {$calculatedUserAmount}<br>";
                echo "Ожидаемая сумма пользователя: {$userAmount}<br>";
                
                $difference = abs($calculatedUserAmount - $userAmount);
                if ($difference < 0.00001) {
                    echo "<span style='color:green'>✅ Математика сходится! (разница: {$difference})</span><br>";
                } else {
                    echo "<span style='color:red'>❌ Ошибка в расчетах! (разница: {$difference})</span><br>";
                }
            } else {
                echo "<span style='color:orange'>⚠️ Старая логика - crypto_amount_for_nowpayments не найдено</span><br>";
            }
            
            if (isset($result['fee_handling'])) {
                echo "<br><strong>📋 Детали обработки комиссий:</strong><br>";
                echo "• {$result['fee_handling']['note']}<br>";
            }
            
            echo "</div>";
        } else {
            echo "<div class='error'>";
            echo "<strong>❌ Ошибка расчета:</strong> {$result['error']}<br>";
            if (isset($result['error_code'])) {
                echo "Код ошибки: {$result['error_code']}<br>";
            }
            echo "</div>";
        }
        
        echo "</div>";
    }
    
    // Тест API вызова
    echo "<div class='test'>";
    echo "<h3>🔌 Тест API вызова (симуляция)</h3>";
    echo "<div class='info'>";
    echo "<strong>Симулируем создание выплаты с новой логикой:</strong><br>";
    
    // Берем первый успешный результат для теста
    $testResult = $calculator->calculateWithdrawalAmount(1000, 'ton');
    if ($testResult['success'] && isset($testResult['crypto_amount_for_nowpayments'])) {
        $userAmount = $testResult['crypto_amount'];
        $nowpaymentsAmount = $testResult['crypto_amount_for_nowpayments'];
        
        echo "1. Калькулятор показал пользователю: <strong>{$userAmount} TON</strong><br>";
        echo "2. Система отправит в NOWPayments: <strong>{$nowpaymentsAmount} TON</strong><br>";
        echo "3. NOWPayments вычтет комиссию и отправит пользователю: <strong>~{$userAmount} TON</strong><br>";
        echo "<br><span style='color:green'>✅ Новый алгоритм работает правильно!</span>";
    } else {
        echo "<span style='color:red'>❌ Не удалось получить данные для теста API</span>";
    }
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error test'>";
    echo "<strong>❌ Критическая ошибка:</strong> " . $e->getMessage();
    echo "</div>";
}

echo "<div class='info test'>";
echo "<h3>📝 Резюме нового алгоритма:</h3>";
echo "1. <strong>Калькулятор</strong> показывает пользователю сумму ПОСЛЕ вычета комиссии<br>";
echo "2. <strong>Система</strong> автоматически добавляет комиссию к сумме для NOWPayments<br>";
echo "3. <strong>NOWPayments</strong> получает сумму + комиссия, вычитает комиссию и отправляет пользователю точную сумму<br>";
echo "4. <strong>Пользователь</strong> получает именно ту сумму, которую видел в калькуляторе<br>";
echo "<br><strong>🎯 Цель достигнута:</strong> Комиссия NOWPayments теперь висит на пользователе, но прозрачно!";
echo "</div>";
?>
