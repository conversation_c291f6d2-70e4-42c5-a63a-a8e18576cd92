// === referral-system.js ===
// Файл: js/referral-system.js
// Описание: Управляет реферальной системой.

class ReferralManager {
  constructor() {
    this.isInitialized = false;
    this.currentUserId = null;
    this.botUsername = window.AppConfig ? window.AppConfig.BOT_USERNAME : 'uniqpaid_bot';

    this.elements = {
      shareAppButton: document.getElementById('share-app-button'),
      referralLinkInput: document.getElementById('referral-link-input') || document.getElementById('referral-link'),
      copyReferralButton: document.getElementById('copy-referral-button'),
      referralsCount: document.getElementById('referrals-count'),
      referralEarnings: document.getElementById('referral-earnings'),
      referralsList: document.getElementById('referrals-list'),
      refreshStatsButton: document.getElementById('refresh-stats-button'),
      referrerInfo: document.getElementById('referrer-info'),
    };

    // Экспорт переменных для обратной совместимости (из оригинала)
    window.referralLinkInput = this.elements.referralLinkInput;
    window.copyReferralButton = this.elements.copyReferralButton;
    window.shareAppButton = this.elements.shareAppButton;
    window.currentUserId = this.currentUserId;
  }

  init() {
    if (this.isInitialized) {
      console.log('[ReferralManager] Уже инициализирован.');
      return;
    }

    console.log('[ReferralManager] Инициализация реферальной системы...');
    this.setupEventListeners();
    this.extractAndSetUserId();
    this.generateReferralLink();
    this.registerAsReferral();

    // Загружаем статистику рефералов и информацию о реферере
    setTimeout(() => {
      this.loadReferralStats();
    }, 1000);

    this.isInitialized = true;
  }

  /**
   * Извлекает и устанавливает ID пользователя (из оригинала)
   */
  extractAndSetUserId() {
    const userId = window.Telegram?.WebApp?.initDataUnsafe?.user?.id;
    if (userId) {
      this.currentUserId = userId;
      window.currentUserId = userId; // Для обратной совместимости
      console.log('[ReferralManager] ID пользователя установлен:', userId);
    } else {
      console.warn('[ReferralManager] Не удалось получить ID пользователя');
    }
  }
  
  setupEventListeners() {
    if (this.elements.shareAppButton) {
      this.elements.shareAppButton.addEventListener('click', () => this.handleShareAppClick());
    }
    if (this.elements.copyReferralButton) {
      this.elements.copyReferralButton.addEventListener('click', () => this.copyReferralLink());
    }
    if (this.elements.refreshStatsButton) {
      this.elements.refreshStatsButton.addEventListener('click', () => this.loadReferralStats());
    }
  }

  generateReferralLink() {
    console.log('[ReferralManager] Генерация реферальной ссылки...');

    const userId = this.currentUserId || window.Telegram?.WebApp?.initDataUnsafe?.user?.id;
    const botUsername = window.AppConfig?.BOT_USERNAME || this.botUsername;

    console.log('[ReferralManager] userId:', userId, 'botUsername:', botUsername);

    if (userId && this.elements.referralLinkInput) {
      const link = `https://t.me/${botUsername}?start=ref${userId}`;
      this.elements.referralLinkInput.value = link;
      if (this.elements.copyReferralButton) this.elements.copyReferralButton.disabled = false;
      console.log('[ReferralManager] ✅ Реферальная ссылка сгенерирована:', link);
    } else if (this.elements.referralLinkInput) {
      this.elements.referralLinkInput.value = window.appLocalization ?
        window.appLocalization.get('status.generating_link') :
        "Генерация ссылки...";
      if (this.elements.copyReferralButton) this.elements.copyReferralButton.disabled = true;
      console.warn('[ReferralManager] ❌ Не удалось сгенерировать ссылку. userId:', userId, 'input:', !!this.elements.referralLinkInput);
    }
  }

  copyReferralLink() {
    const textToCopy = this.elements.referralLinkInput.value;

    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard.writeText(textToCopy).then(() => {
        this.showCopySuccessFeedback();
      }).catch(err => {
        console.warn('Ошибка копирования через Clipboard API, пробуем execCommand:', err);
        this.fallbackCopyTextToClipboard(textToCopy);
      });
    } else {
      console.log('Clipboard API не поддерживается, используем execCommand.');
      this.fallbackCopyTextToClipboard(textToCopy);
    }
  }

  fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    
    // Стили, чтобы элемент не был виден
    textArea.style.position = "fixed";
    textArea.style.top = 0;
    textArea.style.left = 0;
    textArea.style.width = "2em";
    textArea.style.height = "2em";
    textArea.style.padding = 0;
    textArea.style.border = "none";
    textArea.style.outline = "none";
    textArea.style.boxShadow = "none";
    textArea.style.background = "transparent";

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
      const successful = document.execCommand('copy');
      if (successful) {
        this.showCopySuccessFeedback();
      } else {
        throw new Error('Copy command was not successful.');
      }
    } catch (err) {
      console.error('Ошибка копирования через execCommand:', err);
      window.appUtils.showAlert('Не удалось скопировать. Пожалуйста, выделите и скопируйте вручную.');
    }

    document.body.removeChild(textArea);
  }

  showCopySuccessFeedback() {
    window.appUtils.vibrate('success');
    const successMessage = window.appLocalization ?
      window.appLocalization.get('friends.link_copied') :
      "Ссылка скопирована!";
    window.appUtils.showStatus(successMessage, "success");
    if (this.elements.copyReferralButton) {
      const originalText = this.elements.copyReferralButton.innerHTML;
      this.elements.copyReferralButton.innerHTML = "✅";
      setTimeout(() => {
        this.elements.copyReferralButton.innerHTML = originalText;
      }, 1500);
    }
  }

  shareApp() {
    const userId = window.Telegram?.WebApp?.initDataUnsafe?.user?.id;
    const shareUrl = `https://t.me/${window.AppConfig.BOT_USERNAME}?start=ref${userId}`;
    const shareText = window.appLocalization ?
      window.appLocalization.get('friends.share_text') :
      "Просто смотри рекламу и получай крипту на карту. Мгновенный вывод на кошелёк:";
    const telegramShareUrl = `https://t.me/share/url?url=${encodeURIComponent(shareUrl)}&text=${encodeURIComponent(shareText)}`;

    window.appUtils.vibrate('light');
    window.Telegram.WebApp.openTelegramLink(telegramShareUrl);
  }

  async loadReferralStats() {
    const initData = window.appUtils.getInitData();
    if (!initData) return window.appUtils.showStatus("Ошибка аутентификации.", "error");

    if (this.elements.refreshStatsButton) this.elements.refreshStatsButton.disabled = true;
    window.appUtils.showStatus("Загрузка статистики...");

    try {
      const response = await fetch(`${window.API_BASE_URL}/getReferralStats.php`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ initData }),
      });
      const data = await response.json();
      if (data.error) throw new Error(data.error);

      if (this.elements.referralsCount) this.elements.referralsCount.textContent = data.referralsCount || 0;
      if (this.elements.referralEarnings) {
        const coinsText = window.appLocalization ? window.appLocalization.get('currency.coins') : 'монет';
        this.elements.referralEarnings.textContent = `${data.totalEarned || 0} ${coinsText}`;
      }

      if (this.elements.referralsList) {
          this.elements.referralsList.innerHTML = ""; // Очистка
          if (data.referrals && data.referrals.length > 0) {
              data.referrals.forEach(ref => {
                  const item = document.createElement('div');
                  item.className = 'referral-item';
                  const balanceLabel = window.appLocalization ?
                    window.appLocalization.get('friends.balance') :
                    'Баланс';
                  item.innerHTML = `<div class="referral-name">${ref.display_name || `ID: ${ref.id}`}</div><div class="referral-info">${balanceLabel}: ${ref.balance}</div>`;
                  this.elements.referralsList.appendChild(item);
              });
          } else {
              const noReferralsText = window.appLocalization ?
                window.appLocalization.get('friends.no_referrals') :
                'У вас пока нет рефералов.';
              this.elements.referralsList.innerHTML = `<p class='hint'>${noReferralsText}</p>`;
          }
      }

      // Отображаем информацию о реферере
      if (this.elements.referrerInfo) {
          if (data.referrerInfo && data.referrer) {
              this.elements.referrerInfo.innerHTML = `
                  <div style="background: rgba(0, 255, 100, 0.1); border: 1px solid #00ff64; border-radius: 10px; padding: 20px; text-align: center;">
                      <svg style="width: 24px; height: 24px; margin-right: 8px; color: #00ff64; vertical-align: middle;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                          <path d="M16 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2"/>
                          <circle cx="8.5" cy="7" r="4"/>
                          <path d="M20 8v6"/>
                          <path d="M23 11l-3-3-3 3"/>
                      </svg>
                      <div style="color: #00ff64; font-weight: bold; margin-bottom: 5px;">
                          ${data.referrerInfo.display_name || `Пользователь #${data.referrer}`}
                      </div>
                      <small style="opacity: 0.8; color: var(--cyber-text-secondary);">
                          ${window.appLocalization ? window.appLocalization.get('friends.invited_you') : 'Этот пользователь пригласил вас в приложение'}
                      </small>
                  </div>
              `;
          } else {
              this.elements.referrerInfo.innerHTML = `
                  <div style="background: rgba(255, 165, 0, 0.1); border: 1px solid #FFA500; border-radius: 10px; padding: 20px; text-align: center;">
                      <svg style="width: 24px; height: 24px; margin-right: 8px; color: #FFA500; vertical-align: middle;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                          <path d="M16 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2"/>
                          <circle cx="8.5" cy="7" r="4"/>
                          <path d="M20 8v6"/>
                          <path d="M23 11l-3-3-3 3"/>
                      </svg>
                      <div style="color: #FFA500; font-weight: bold; margin-bottom: 5px;">
                          ${window.appLocalization ? window.appLocalization.get('friends.no_referrer') : 'У вас нет реферера'}
                      </div>
                      <small style="opacity: 0.8; color: var(--cyber-text-secondary);">
                          ${window.appLocalization ? window.appLocalization.get('friends.joined_independently') : 'Вы присоединились к приложению самостоятельно'}
                      </small>
                  </div>
              `;
          }
      }
      const statsUpdatedText = window.appLocalization ?
        window.appLocalization.get('friends.stats_updated') :
        'Статистика обновлена';
      window.appUtils.showStatus(statsUpdatedText, "success");
    } catch (error) {
      window.appUtils.showStatus(`Ошибка: ${error.message}`, "error");
    } finally {
      if (this.elements.refreshStatsButton) this.elements.refreshStatsButton.disabled = false;
    }
  }

  async registerAsReferral() {
    const startParam = new URLSearchParams(window.location.search).get('start');
    const initData = window.appUtils.getInitData();
    if (!startParam || !startParam.startsWith('ref') || !initData) return;

    const referrerId = startParam.substring(3);
    await fetch(`${window.API_BASE_URL}/registerReferral.php`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ initData, referrerId }),
    });
  }
  // Методы для обратной совместимости
  handleShareAppClick() {
    this.shareApp();
  }
}

window.referralManager = new ReferralManager();

// Экспорт функций для обратной совместимости (из оригинала)
window.generateReferralLink = () => window.referralManager.generateReferralLink();
window.copyReferralLink = () => window.referralManager.copyReferralLink();
window.handleShareAppClick = () => window.referralManager.handleShareAppClick();
window.loadReferralStats = () => window.referralManager.loadReferralStats();
window.registerAsReferral = () => window.referralManager.registerAsReferral();

console.log('👥 [ReferralManager] Реферальная система загружена с полной интеграцией.');