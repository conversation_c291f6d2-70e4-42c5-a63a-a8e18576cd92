<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест счетчиков рекламы</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 1px solid #444;
        }
        .button {
            background: #FFC107;
            color: #000;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        .button:hover {
            background: #FFD54F;
        }
        .counter-display {
            background: #333;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
        }
        .ad-button {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: linear-gradient(145deg, #FFD54F, #FFC107);
            color: #000;
            border: none;
            padding: 15px 20px;
            margin: 10px 0;
            border-radius: 15px;
            cursor: pointer;
            font-weight: bold;
        }
        .button-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
        }
        .button-text {
            font-size: 16px;
            margin-bottom: 3px;
        }
        .ad-counter {
            font-size: 12px;
            opacity: 0.8;
        }
        .ad-counter.limit-reached {
            color: #ff6464;
            opacity: 1;
        }
        .reward-badge {
            background: transparent;
            border: 2px solid #000;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>🧪 Тест счетчиков рекламы</h1>

    <div class="test-section">
        <h2>📊 Кнопки с счетчиками</h2>
        
        <button class="ad-button" id="test-native-banner">
            <div class="button-content">
                <span class="button-text">Открыть ссылку</span>
                <span class="ad-counter" id="native-banner-counter">осталось 20 показов</span>
            </div>
            <div class="reward-badge">+10</div>
        </button>

        <button class="ad-button" id="test-rewarded-video">
            <div class="button-content">
                <span class="button-text">Смотреть видео</span>
                <span class="ad-counter" id="rewarded-video-counter">осталось 20 показов</span>
            </div>
            <div class="reward-badge">+1</div>
        </button>

        <button class="ad-button" id="test-interstitial">
            <div class="button-content">
                <span class="button-text">Кликнуть по баннеру</span>
                <span class="ad-counter" id="interstitial-counter">осталось 20 показов</span>
            </div>
            <div class="reward-badge">+10</div>
        </button>
    </div>

    <div class="test-section">
        <h2>🎮 Управление</h2>
        <button class="button" onclick="simulateAdView('native_banner')">Симулировать просмотр баннера</button>
        <button class="button" onclick="simulateAdView('rewarded_video')">Симулировать просмотр видео</button>
        <button class="button" onclick="simulateAdView('interstitial')">Симулировать интерстициальную рекламу</button>
        <br>
        <button class="button" onclick="switchToEnglish()">Switch to English</button>
        <button class="button" onclick="switchToRussian()">Переключить на русский</button>
        <br>
        <button class="button" onclick="resetCounters()">🔄 Сбросить счетчики</button>
        <button class="button" onclick="showLimitsInfo()">📊 Показать информацию о лимитах</button>
        <button class="button" onclick="testDailyReset()">🕒 Тест ежедневного сброса</button>
        <button class="button" onclick="simulateNextDay()">📅 Симулировать следующий день</button>
    </div>

    <div class="test-section">
        <h2>📈 Информация о лимитах</h2>
        <div class="counter-display" id="limits-info">
            Загрузка информации о лимитах...
        </div>
    </div>

    <div class="test-section">
        <h2>📝 Лог событий</h2>
        <div class="counter-display" id="event-log" style="height: 200px; overflow-y: auto;">
            Лог событий будет отображаться здесь...
        </div>
    </div>

    <!-- Подключаем необходимые модули -->
    <script>
        // Мини-версия локализации для тестирования
        window.appLocalization = {
            isLoaded: true,
            currentLanguage: 'ru',
            translations: {
                'ru': {
                    'tasks.ad_views_left': 'осталось {count} показов',
                    'tasks.ad_views_left_single': 'остался {count} показ',
                    'tasks.ad_views_left_few': 'осталось {count} показа'
                },
                'en': {
                    'tasks.ad_views_left': '{count} ad views left'
                }
            },
            get: function(key, params = {}) {
                const translation = this.translations[this.currentLanguage]?.[key] || key;
                return translation.replace(/\{(\w+)\}/g, (match, param) => params[param] || match);
            }
        };

        // Функции для тестирования
        function simulateAdView(adType) {
            if (window.adCountersManager) {
                window.adCountersManager.incrementCounter(adType);
                updateLimitsInfo();
                logEvent(`Симулирован просмотр рекламы: ${adType}`);
            }
        }

        function switchToEnglish() {
            window.appLocalization.currentLanguage = 'en';
            if (window.adCountersManager) {
                window.adCountersManager.updateLanguage('en');
            }
            logEvent('Язык переключен на английский');
        }

        function switchToRussian() {
            window.appLocalization.currentLanguage = 'ru';
            if (window.adCountersManager) {
                window.adCountersManager.updateLanguage('ru');
            }
            logEvent('Язык переключен на русский');
        }

        function resetCounters() {
            if (window.adCountersManager) {
                window.adCountersManager.resetAllCounters();
                updateLimitsInfo();
                logEvent('Все счетчики сброшены');
            }
        }

        function showLimitsInfo() {
            updateLimitsInfo();
        }

        function updateLimitsInfo() {
            if (window.adCountersManager) {
                const info = window.adCountersManager.getAllLimitsInfo();
                const infoDiv = document.getElementById('limits-info');
                infoDiv.innerHTML = '<pre>' + JSON.stringify(info, null, 2) + '</pre>';
            }
        }

        function testDailyReset() {
            if (window.adCountersManager) {
                const resetInfo = window.adCountersManager.getTimeUntilNextDayUTC();
                logEvent(`Время до сброса: ${resetInfo.hours}ч ${resetInfo.minutes}мин`);
                logEvent(`Следующий сброс: ${resetInfo.nextResetTime}`);
                logEvent(`Текущая UTC дата: ${window.adCountersManager.getCurrentUTCDate()}`);
                updateLimitsInfo();
            }
        }

        function simulateNextDay() {
            // Создаем счетчики для "завтрашнего" дня
            const tomorrow = new Date();
            tomorrow.setUTCDate(tomorrow.getUTCDate() + 1);
            const tomorrowStr = tomorrow.getUTCFullYear() + '-' +
                String(tomorrow.getUTCMonth() + 1).padStart(2, '0') + '-' +
                String(tomorrow.getUTCDate()).padStart(2, '0');

            // Временно переопределяем функцию получения даты
            const originalGetCurrentUTCDate = window.adCountersManager.getCurrentUTCDate;
            window.adCountersManager.getCurrentUTCDate = () => tomorrowStr;

            // Обновляем счетчики
            window.adCountersManager.updateAllCounters();
            updateLimitsInfo();

            logEvent(`Симуляция следующего дня: ${tomorrowStr}`);
            logEvent('Счетчики должны показать полные лимиты (20/20/20)');

            // Восстанавливаем оригинальную функцию через 5 секунд
            setTimeout(() => {
                window.adCountersManager.getCurrentUTCDate = originalGetCurrentUTCDate;
                window.adCountersManager.updateAllCounters();
                updateLimitsInfo();
                logEvent('Восстановлена текущая дата');
            }, 5000);
        }

        function logEvent(message) {
            const logDiv = document.getElementById('event-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        // Инициализация после загрузки страницы
        window.addEventListener('load', () => {
            logEvent('Страница загружена, ожидание загрузки модулей...');
            
            // Ждем загрузки модуля счетчиков
            const checkCounters = setInterval(() => {
                if (window.adCountersManager) {
                    clearInterval(checkCounters);
                    window.adCountersManager.init().then(() => {
                        logEvent('Менеджер счетчиков инициализирован');
                        updateLimitsInfo();
                    });
                }
            }, 100);
        });
    </script>

    <!-- Подключаем модуль счетчиков -->
    <script src="js/ad-counters.js"></script>
</body>
</html>
