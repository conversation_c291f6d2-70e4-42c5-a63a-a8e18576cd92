// === withdrawal-manager.js ===
// Файл: js/withdrawal-manager.js
// Описание: Управляет историей выплат.

class WithdrawalManager {
  constructor() {
    this.isInitialized = false;
    this.withdrawals = [];
    this.statusMonitorInterval = null;
    this.forceUpdateInterval = null;

    // Элементы интерфейса (из оригинала)
    this.elements = {
      historyContainer: document.querySelector('.placeholder-list') || document.querySelector('#withdrawal-history-list'),
      refreshHistoryButton: document.querySelector('.refresh-history-btn') || document.getElementById('refresh-history-button')
    };
  }

  init() {
    if (this.isInitialized) {
      console.log('[WithdrawalManager] Уже инициализирован.');
      return;
    }
    console.log('[WithdrawalManager] Инициализация истории выплат...');
    this.setupEventListeners();
    this.loadAndDisplayHistory();
    this.startStatusMonitoring();
    this.isInitialized = true;
  }

  setupEventListeners() {
    if (this.elements.refreshHistoryButton) {
      this.elements.refreshHistoryButton.addEventListener('click', () => this.forceLoadHistory());
    }
  }

  /**
   * Загружает и отображает историю выплат (из оригинала)
   */
  async loadAndDisplayHistory() {
    console.log('[WithdrawalManager] Загружаем историю выплат');

    let userData = this.getUserDataForAPI();
    console.log('[WithdrawalManager] Данные пользователя:', userData);

    if (!userData) {
      console.log('[WithdrawalManager] Нет данных пользователя - используем принудительную загрузку');
      // ПРИНУДИТЕЛЬНАЯ ЗАГРУЗКА для тестирования
      userData = { initData: 'fallback_test' };
    }

    // Сначала принудительно обновляем статусы
    console.log('[WithdrawalManager] Обновляем статусы перед загрузкой истории');
    await this.forceUpdateWithdrawalStatuses();

    try {
      const response = await fetch(`${window.API_BASE_URL}/getWithdrawalHistory.php`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(userData)
      });

      if (response.ok) {
        const data = await response.json();
        console.log('[WithdrawalManager] Ответ сервера:', data);

        if (data.withdrawals && data.withdrawals.length > 0) {
          console.log('[WithdrawalManager] Отображаем', data.withdrawals.length, 'выплат');
          this.displayHistory(data.withdrawals);
        } else {
          console.log('[WithdrawalManager] Нет выплат для отображения');
          console.log('[WithdrawalManager] Данные ответа:', JSON.stringify(data));
          this.showEmptyHistory();
        }
      } else {
        throw new Error(`HTTP ${response.status}`);
      }
    } catch (error) {
      console.error('[WithdrawalManager] Ошибка загрузки истории выплат:', error);
      if (window.appUtils) {
        window.appUtils.showStatus('Ошибка загрузки истории выплат', 'error');
      }
      this.showEmptyHistory();
    }
  }

  /**
   * Отображает историю выплат (из оригинала)
   */
  displayHistory(withdrawals) {
    if (!this.elements.historyContainer) {
      console.warn("Не найден контейнер для истории выплат");
      return;
    }

    // Очищаем контейнер
    this.elements.historyContainer.innerHTML = "";

    if (withdrawals && withdrawals.length > 0) {
      // Создаем список выплат
      withdrawals.forEach(withdrawal => {
        const withdrawalItem = document.createElement("div");
        withdrawalItem.className = "withdrawal-item";

        // Определяем статус и его стиль
        const statusText = this.getStatusText(withdrawal.status);
        const statusClass = this.getStatusClass(withdrawal.status);

        // ИСПРАВЛЕНИЕ: Обрабатываем дату точно как в оригинале
        let date;
        if (withdrawal.timestamp) {
          date = new Date(withdrawal.timestamp * 1000);
        } else if (withdrawal.created_at) {
          date = new Date(withdrawal.created_at);
        } else {
          date = new Date();
        }
        const dateStr = date.toLocaleDateString('ru-RU');
        const timeStr = date.toLocaleTimeString('ru-RU', { hour: '2-digit', minute: '2-digit' });

        // ИСПРАВЛЕНИЕ: Используем правильные поля точно как в оригинале
        const coinsAmount = withdrawal.coins_amount || withdrawal.amount || 0;
        const currency = withdrawal.currency || 'unknown';
        const address = withdrawal.wallet_address || withdrawal.address || 'Адрес не указан';

        // ИСПРАВЛЕНИЕ: Создаем HTML точно как в оригинале
        withdrawalItem.innerHTML = `
          <div class="withdrawal-header">
            <span class="withdrawal-amount">${coinsAmount} ${window.appLocalization ? window.appLocalization.get('currency.coins') : 'монет'}</span>
            <span class="withdrawal-status ${statusClass}">${statusText}</span>
          </div>
          <div class="withdrawal-details">
            <div class="withdrawal-currency">${currency.toUpperCase()}</div>
            <div class="withdrawal-date">${dateStr} ${timeStr}</div>
            ${withdrawal.payout_id ? `<div class="withdrawal-payout-id">ID: ${withdrawal.payout_id}</div>` : ''}
          </div>
          <div class="withdrawal-address">${address}</div>
        `;

        // Добавляем обработчик для кнопки отмены
        const cancelBtn = withdrawalItem.querySelector('.cancel-withdrawal-btn');
        if (cancelBtn) {
          cancelBtn.addEventListener('click', () => this.cancelWithdrawal(withdrawal.id));
        }

        this.elements.historyContainer.appendChild(withdrawalItem);
      });
    } else {
      this.showEmptyHistory();
    }
  }

  /**
   * Показывает пустую историю с красивым дизайном
   */
  showEmptyHistory() {
    if (this.elements.historyContainer) {
      this.elements.historyContainer.innerHTML = `
        <div class="empty-history-state">
          <div class="empty-history-icon">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
              <path d="M12 2v10"/>
              <path d="M8 8l4-4 4 4"/>
              <rect x="3" y="14" width="18" height="6" rx="2"/>
              <circle cx="7" cy="17" r="1"/>
              <circle cx="17" cy="17" r="1"/>
            </svg>
          </div>
          <div class="empty-history-content">
            <h4 class="empty-history-title">${window.appLocalization ? window.appLocalization.get('empty_states.history_empty_title') : 'История выплат пуста'}</h4>
            <p class="empty-history-description">
              ${window.appLocalization ? window.appLocalization.get('empty_states.history_empty_description') : 'Ваши запросы на вывод будут отображаться здесь'}
            </p>
            <div class="empty-history-hint">
              <span class="hint-icon">💡</span>
              <span>${window.appLocalization ? window.appLocalization.get('empty_states.history_empty_hint') : 'Перейдите в калькулятор, чтобы рассчитать и запросить первый вывод'}</span>
            </div>
          </div>
        </div>
      `;
    }
  }

  /**
   * Получает текст статуса выплаты на русском языке (из оригинала)
   */
  getStatusText(status) {
    if (window.appLocalization) {
      const statusTranslations = {
        'waiting': 'withdrawal_status.waiting',
        'processing': 'withdrawal_status.processing',
        'sending': 'withdrawal_status.sending',
        'finished': 'withdrawal_status.finished',
        'completed': 'withdrawal_status.completed',
        'confirmed': 'withdrawal_status.confirmed',
        'failed': 'withdrawal_status.failed',
        'rejected': 'withdrawal_status.rejected',
        'pending': 'withdrawal_status.pending',
        'cancelled': 'withdrawal_status.cancelled',
        'expired': 'withdrawal_status.expired'
      };

      const translationKey = statusTranslations[status];
      if (translationKey) {
        return window.appLocalization.get(translationKey);
      }
    }

    // Fallback статусы
    const statusMap = {
      'waiting': 'Ожидание обработки',
      'processing': 'Обрабатывается',
      'sending': 'Отправляется на кошелек',
      'finished': 'Отправлено на кошелек',
      'completed': 'Отправлено на кошелек',
      'confirmed': 'Подтверждено в блокчейне',
      'failed': 'Ошибка выплаты',
      'rejected': 'Отклонено системой',
      'pending': 'В обработке',
      'cancelled': 'Отменено',
      'expired': 'Истекло'
    };
    return statusMap[status] || status || 'Неизвестно';
  }

  /**
   * Получает CSS класс для статуса (из оригинала)
   */
  getStatusClass(status) {
    const classMap = {
      // Определяем CSS класс на основе статуса (из оригинала)
      'waiting': 'status-pending',
      'pending': 'status-pending',
      'processing': 'status-processing',
      'sending': 'status-processing',
      'completed': 'status-completed',
      'finished': 'status-completed',
      'confirmed': 'status-completed',
      'failed': 'status-failed',
      'cancelled': 'status-failed',
      'rejected': 'status-failed',
      'expired': 'status-expired'
    };
    return classMap[status] || 'status-unknown';
  }
  /**
   * Принудительная загрузка истории (для кнопки в интерфейсе) (из оригинала)
   */
  forceLoadHistory() {
    console.log('[WithdrawalManager] Принудительная загрузка истории выплат');

    // Показываем индикатор загрузки
    if (this.elements.historyContainer) {
      this.elements.historyContainer.innerHTML = '<div class="history-item placeholder">🔄 Загрузка истории выплат...</div>';
    }

    // Принудительно загружаем историю
    this.loadAndDisplayHistory();

    // Дополнительно через 2 секунды
    setTimeout(() => {
      console.log('[WithdrawalManager] Повторная загрузка через 2 секунды');
      this.loadAndDisplayHistory();
    }, 2000);
  }

  /**
   * Автоматически проверяет статусы выплат каждые 30 секунд (из оригинала)
   */
  startStatusMonitoring() {
    console.log('[WithdrawalManager] Запуск мониторинга статусов выплат');

    // Проверяем сразу при запуске
    this.checkWithdrawalStatusUpdates();

    // Затем проверяем каждые 30 секунд
    this.statusMonitorInterval = setInterval(() => {
      this.checkWithdrawalStatusUpdates();
    }, 30000);

    // Дополнительно проверяем каждые 5 минут через принудительное обновление
    this.forceUpdateInterval = setInterval(() => {
      console.log('[WithdrawalManager] Принудительное обновление статусов через API');
      this.forceUpdateWithdrawalStatuses();
    }, 300000); // 5 минут
  }

  /**
   * Проверяет обновления статусов выплат (из оригинала)
   */
  async checkWithdrawalStatusUpdates() {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);

      try {
        const response = await fetch(`${window.API_BASE_URL}/checkUserWithdrawals.php`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-API-Key": window.API_KEY || "default_key"
          },
          signal: controller.signal,
          body: JSON.stringify({
            initData: window.Telegram?.WebApp?.initData,
            timestamp: Date.now()
          })
        });
        clearTimeout(timeoutId);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('[WithdrawalManager] Ошибка API:', {
            status: response.status,
            url: response.url,
            error: errorText
          });

          if (response.status === 500) {
            if (window.appUtils) {
              window.appUtils.showStatus('Временные проблемы с сервером', 'warning');
            }
          }
          return 0;
        }

        const data = await response.json();
        if (!data || typeof data !== 'object') {
          throw new Error('Некорректный формат ответа сервера');
        }

        if (data.success) {
          console.log(`[WithdrawalManager] Проверено выплат: ${data.checked}, обновлено: ${data.updated}`);

          if (data.updated > 0) {
            setTimeout(() => this.loadAndDisplayHistory(), 1000);
            if (window.appUtils) {
              window.appUtils.showStatus(`Обновлено статусов: ${data.updated}`, 'success');
            }
          }
          return data.updated;
        } else {
          console.warn('[WithdrawalManager] Ошибка API:', data.error);
          return 0;
        }
      } catch (error) {
        clearTimeout(timeoutId);
        console.warn('[WithdrawalManager] Ошибка сети:', error.message);
        if (window.appUtils) {
          window.appUtils.showStatus('Оффлайн режим: данные могут быть неактуальны', 'warning');
        }
        return 0;
      }
    } catch (error) {
      console.warn('[WithdrawalManager] Внешняя ошибка:', error.message);
      return 0;
    }
  }

  /**
   * Принудительно обновляет статусы выплат (из оригинала)
   */
  async forceUpdateWithdrawalStatuses() {
    try {
      const response = await fetch(`${window.API_BASE_URL}/forceUpdateWithdrawalStatuses.php`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ initData: window.Telegram?.WebApp?.initData })
      });

      if (response.ok) {
        const data = await response.json();
        console.log('[WithdrawalManager] Принудительное обновление статусов:', data);

        // Если есть обновления, перезагружаем историю
        if (data.updated && data.updated > 0) {
          console.log(`[WithdrawalManager] Принудительное обновление: найдено ${data.updated} изменений статусов`);
          setTimeout(() => this.loadAndDisplayHistory(), 1000);

          if (window.appUtils) {
            window.appUtils.showStatus(`Принудительно обновлено статусов: ${data.updated}`, 'success');
          }
        }

        return data;
      } else {
        console.warn('[WithdrawalManager] API принудительного обновления недоступен (HTTP:', response.status, ')');
      }
    } catch (error) {
      console.warn('[WithdrawalManager] API принудительного обновления недоступен:', error.message);
    }
    return null;
  }

  /**
   * Получает данные пользователя для API (из оригинала)
   */
  getUserDataForAPI() {
    const initData = window.Telegram?.WebApp?.initData;
    if (initData) {
      return { initData };
    }
    return null;
  }

  /**
   * Останавливает мониторинг статусов
   */
  stopStatusMonitoring() {
    if (this.statusMonitorInterval) {
      clearInterval(this.statusMonitorInterval);
      this.statusMonitorInterval = null;
    }
    if (this.forceUpdateInterval) {
      clearInterval(this.forceUpdateInterval);
      this.forceUpdateInterval = null;
    }
  }

  // Методы для обратной совместимости
  renderHistory(withdrawals) {
    this.displayHistory(withdrawals);
  }

  getWithdrawalStatusText(status) {
    return this.getStatusText(status);
  }

  async checkAndUpdateStatuses(initData) {
    return this.checkWithdrawalStatusUpdates();
  }
}

window.withdrawalManager = new WithdrawalManager();

// Экспорт функций для обратной совместимости (из оригинала)
window.loadAndDisplayWithdrawalHistory = () => window.withdrawalManager.loadAndDisplayHistory();
window.displayWithdrawalHistory = (withdrawals) => window.withdrawalManager.displayHistory(withdrawals);
window.startWithdrawalStatusMonitoring = () => window.withdrawalManager.startStatusMonitoring();
window.checkWithdrawalStatusUpdates = () => window.withdrawalManager.checkWithdrawalStatusUpdates();
window.forceLoadHistory = () => window.withdrawalManager.forceLoadHistory();
window.getWithdrawalStatusText = (status) => window.withdrawalManager.getStatusText(status);

console.log('📜 [WithdrawalManager] Менеджер истории выплат загружен с полной интеграцией.');