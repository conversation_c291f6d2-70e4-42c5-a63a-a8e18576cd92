<?php
/**
 * Cron-скрипт для обновления курсов валют, комиссий и минимумов.
 * Этот скрипт теперь является оберткой для централизованной функции обновления.
 * Запускать каждый час: 0 * * * * php /path/to/api/cron_update_rates.php
 */

require_once __DIR__ . '/update_cache.php';

// Логирование для cron
function cronLog($message) {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] CRON (cron_update_rates.php): {$message}\n";
    file_put_contents(__DIR__ . '/cron.log', $logMessage, FILE_APPEND | LOCK_EX);
    error_log("CRON: {$message}");
}

cronLog("=== Запуск централизованного обновления кэша ===");

try {
    $result = force_update_currency_cache();
    
    if ($result['success']) {
        cronLog("✅ Успешно обновлено: " . implode(', ', $result['updated_currencies']));
    } else {
        cronLog("❌ Ошибка обновления: " . $result['error']);
    }

} catch (Exception $e) {
    cronLog("💥 КРИТИЧЕСКАЯ ОШИБКА: " . $e->getMessage());
}

cronLog("=== Скрипт завершен ===\n");

?>
