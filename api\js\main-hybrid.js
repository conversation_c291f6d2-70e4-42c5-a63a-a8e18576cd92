/**
 * main-hybrid.js
 * ГИБРИДНАЯ ВЕРСИЯ - Постепенный переход к модульной системе
 * Сохраняет ВСЮ функциональность оригинала + добавляет модульность
 */

// === ИМПОРТЫ МОДУЛЕЙ (постепенно будем добавлять) ===
import { 
  API_BASE_URL, 
  DEBUG_MODE, 
  BOT_USERNAME,
  MY_PUB_ID,
  MY_APP_ID,
  AD_TYPES,
  updateCurrentUserId,
  updateAdsController,
  appSettings,
  calculatorData
} from './config.js';

// === ОРИГИНАЛЬНЫЙ КОД (временно, будем постепенно заменять на модули) ===

// --- Глобальные Константы и Переменные ---
const PAGE_TRANSITION_DURATION = 300; // мс, для CSS (должно совпадать)
const AUTO_RELOAD_AFTER_COUNTDOWN = true; // 🔄 Автоматический reload после таймера для обновления рекламы
const USE_SOFT_REFRESH = false; // 🔄 Использовать мягкое обновление (переинициализация SDK) вместо полного reload

// Функция для форматирования чисел с двумя знаками после запятой
function formatCurrency(value) {
  return parseFloat(value).toFixed(2);
}

// Ссылки на элементы DOM (все страницы/секции)
const mainContentEl = document.getElementById("main-content");
const earnSectionEl = document.getElementById("earn-section");
const friendsSectionEl = document.getElementById("friends-section");
const allPages = [mainContentEl, earnSectionEl, friendsSectionEl]; // Массив всех страниц

// Остальные элементы
const userNameEl = document.getElementById("user-name");
const balanceAmountEl = document.getElementById("balance-amount");
const headerBalanceInfoEl = document.getElementById("header-balance-info");
const watchAdButton = document.getElementById("openLinkButton");
const watchVideoButton = document.getElementById("watchVideoButton");
const openLinkButton = document.getElementById("openAdButton");
const statusMessageEl = document.getElementById("status-message");
const shareAppButton = document.getElementById("share-app-button");
const referralLinkInput = document.getElementById("referral-link-input");
const copyReferralButton = document.getElementById("copy-referral-button");
const earnBalanceAmountEl = document.getElementById("earn-balance-amount");
const availableWithdrawalEl = document.getElementById("available-withdrawal");
const minWithdrawalEl = document.getElementById("min-withdrawal");
const withdrawalAmountInput = document.getElementById("withdrawal-amount");
const withdrawalAddressInput = document.getElementById("withdrawal-address");
const cryptoCurrencySelect = document.getElementById("crypto-currency");
const requestWithdrawalButton = document.getElementById("request-withdrawal-button");
const withdrawalErrorEl = document.getElementById("withdrawal-error");
const navHomeButton = document.getElementById("nav-home");
const navEarnButton = document.getElementById("nav-earn");
const navFriendsButton = document.getElementById("nav-friends");

// Глобальные переменные
const tg = window.Telegram.WebApp;
let adsController = null;
let currentUserId = null;
let currentPageElement = mainContentEl; // Отслеживаем текущую страницу
let isTransitioning = false; // Флаг анимации

// Защищенная переменная баланса с валидацией
let _currentUserBalance = 0;
Object.defineProperty(window, 'currentUserBalance', {
  get: function() {
    return _currentUserBalance;
  },
  set: function(value) {
    // Предупреждение о попытке изменения баланса
    if (typeof value === 'number' && value !== _currentUserBalance) {
      console.warn('🚨 БЕЗОПАСНОСТЬ: Попытка изменения баланса через консоль заблокирована!');
      console.warn('💡 Баланс можно изменить только через серверные операции.');
      return;
    }
    _currentUserBalance = value;
  },
  configurable: false,
  enumerable: true
});

// Настройки вывода средств (получаем с сервера)
let minWithdrawalAmount = 0; // Нет минимальной суммы для вывода
let minBalanceForWithdrawal = 1; // Минимальный баланс для доступа к выводу (снижен)

// Переменные для отслеживания состояния рекламы
let lastAdShownTime = 0;
let adCooldownTime = 3000; // 3 секунды между показами рекламы
let isAdShowing = false; // Флаг показа рекламы

// Переменные для счетчика обратного отсчета
let countdownTimer = null;
let isButtonPressed = false;
let coinValue = 0.001; // $0.001 за монету (должно соответствовать серверному значению CONVERSION_RATE)

// === ОСНОВНЫЕ ФУНКЦИИ ===

/**
 * Отображает статусное сообщение.
 * @param {string} message Текст сообщения.
 * @param {'info' | 'success' | 'error'} type Тип сообщения.
 */
function showStatus(message, type = "info") {
  if (!statusMessageEl) {
    console.warn("Элемент status-message не найден.");
    return;
  }
  statusMessageEl.textContent = message;
  statusMessageEl.className = "status-message";
  if (type === "success") {
    statusMessageEl.classList.add("success");
  } else if (type === "error") {
    statusMessageEl.classList.add("error");
  }
  console.log(`Status [${type}]: ${message}`);
}

/**
 * Обновляет отображение баланса в нескольких местах.
 * @param {number | string} newBalance Новый баланс.
 */
function updateBalanceDisplay(newBalance) {
  // Безопасное обновление баланса (только для серверных операций)
  _currentUserBalance = parseInt(newBalance) || 0;
  if (balanceAmountEl) balanceAmountEl.textContent = _currentUserBalance;
  if (earnBalanceAmountEl) earnBalanceAmountEl.textContent = _currentUserBalance;
  if (availableWithdrawalEl) {
    availableWithdrawalEl.textContent = _currentUserBalance;
    console.log(`💰 Элемент "Доступно для вывода" обновлен: ${_currentUserBalance} монет`);
  } else {
    console.warn('⚠️ Элемент "available-withdrawal" не найден!');
  }

  // Обновляем баланс в калькуляторе
  const calcBalance = document.getElementById('calc-balance');
  if (calcBalance) {
    calcBalance.textContent = `${_currentUserBalance} монет`;
  }

  // Обновляем расчеты в калькуляторе, если есть введенная сумма
  const calcAmountInput = document.getElementById('calc-amount');
  if (calcAmountInput && calcAmountInput.value) {
    const amount = parseInt(calcAmountInput.value) || 0;
    updateCalculatorDisplay(amount);
    updateBalanceCheck(amount);
  }

  // Повторно валидируем основную форму вывода, так как изменение баланса может повлиять на ее валидность
  validateWithdrawalForm();

  console.log(`🎯 Баланс обновлен: ${_currentUserBalance}`);
}

/**
 * Инициализация приложения
 */
async function initApp() {
  try {
    console.log("🚀 Запуск гибридного приложения UniQPaid...");
    showStatus("Инициализация приложения...", "info");

    // 1. Инициализация Telegram WebApp
    if (tg) {
      tg.ready();
      tg.expand();
      
      const user = tg.initDataUnsafe?.user;
      if (user) {
        currentUserId = user.id.toString();
        updateCurrentUserId(currentUserId);
        
        if (userNameEl) {
          const displayName = user.first_name + (user.last_name ? ` ${user.last_name}` : '');
          userNameEl.textContent = displayName;
        }
        
        console.log(`👤 Пользователь: ${user.first_name} (ID: ${currentUserId})`);
      }
    } else {
      console.warn("⚠️ Telegram WebApp недоступен, работаем в режиме отладки");
      currentUserId = "debug_user_123";
      updateCurrentUserId(currentUserId);
      if (userNameEl) {
        userNameEl.textContent = "Debug User";
      }
    }

    // 2. Загрузка настроек и данных
    showStatus("Загрузка настроек...", "info");
    await loadAppSettings();
    
    // 3. Загрузка данных пользователя
    if (currentUserId) {
      showStatus("Загрузка данных пользователя...", "info");
      await loadUserData();
    }

    // 4. Инициализация RichAds
    showStatus("Инициализация рекламы...", "info");
    initializeRichAds();

    // 5. Инициализация обработчиков событий
    initEventHandlers();

    // 6. Показ главной страницы
    showMainContent();

    // 7. Финализация
    console.log("✅ Гибридное приложение UniQPaid успешно инициализировано!");
    showStatus("Приложение готово к работе", "success");
    
    setTimeout(() => {
      showStatus("");
    }, 3000);

  } catch (error) {
    console.error("❌ Критическая ошибка инициализации:", error);
    showStatus("Ошибка инициализации приложения", "error");
  }
}

// Экспорт функций для глобального доступа
window.showStatus = showStatus;
window.updateBalanceDisplay = updateBalanceDisplay;

// Запуск приложения при загрузке DOM
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initApp);
} else {
  initApp();
}
