<?php
/**
 * api/admin/design_settings.php
 * Страница настроек дизайна в админке
 */

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../error.log');
error_reporting(E_ALL);

// Подключаем файл с функциями аутентификации и авторизации
require_once __DIR__ . '/auth.php';

// Проверяем аутентификацию
session_start();
if (!isAuthenticated()) {
    header('Location: login.php');
    exit;
}

// Подключение зависимостей
require_once __DIR__ . '/../config.php';

// Файл для хранения настроек дизайна
$designSettingsFile = __DIR__ . '/../../design_settings.json';

// Альтернативный файл в директории админки
$altDesignSettingsFile = __DIR__ . '/design_settings.json';

// Настройки дизайна по умолчанию
$defaultDesignSettings = [
    'colors' => [
        'primary_dark' => '#2a2a2a',
        'accent_orange' => '#ff6b35',
        'accent_neon' => '#00ffff',
        'accent_green' => '#39ff14',
        'bg_card' => '#3a3a3a',
        'bg_secondary' => '#333333',
        'text_primary' => '#ffffff',
        'text_secondary' => '#cccccc',
        'border_color' => '#4a4a4a',
        'cyber_border' => '#00ffff',
        'shadow_orange' => '#ff6b35',
        'shadow_neon' => '#00ffff'
    ],
    'effects' => [
        'crypto_opacity' => 0.6,
        'crypto_size' => 100,
        'crypto_speed' => 15,
        'crypto_amplitude' => 80,
        'crypto_glow_intensity' => 0.4,
        'crypto_blur' => 2,
        'enable_crypto_icons' => true,
        'enable_glitch_effects' => false,
        'enable_glow_effects' => true,
        'enable_animations' => true,
        'background_blur' => 2,
        'button_glow' => true,
        'card_borders' => true,
        'text_shadows' => true
    ],
    'layout' => [
        'border_radius' => 12,
        'button_radius' => 8,
        'card_padding' => 20,
        'spacing' => 16
    ],
    'theme' => 'cyberpunk'
];

// Загружаем текущие настройки
function loadDesignSettings($file, $defaults) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        $settings = json_decode($content, true);
        if ($settings) {
            return array_merge_recursive($defaults, $settings);
        }
    }
    return $defaults;
}

// Сохраняем настройки
function saveDesignSettings($file, $settings) {
    try {
        // Проверяем, что директория существует и доступна для записи
        $dir = dirname($file);
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }

        if (!is_writable($dir)) {
            chmod($dir, 0755);
        }

        $json = json_encode($settings, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        $result = file_put_contents($file, $json);

        if ($result !== false) {
            chmod($file, 0644);
            return true;
        }

        return false;
    } catch (Exception $e) {
        error_log("Ошибка сохранения настроек дизайна: " . $e->getMessage());
        return false;
    }
}

// Обработка AJAX запросов
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['ajax'])) {
    header('Content-Type: application/json');

    try {
        // Логируем входящие данные для отладки
        error_log("Design settings POST data: " . print_r($_POST, true));

        $currentSettings = loadDesignSettings($designSettingsFile, $defaultDesignSettings);

        // Обновляем настройки из POST данных
        if (isset($_POST['colors'])) {
            foreach ($_POST['colors'] as $key => $value) {
                if (isset($currentSettings['colors'][$key])) {
                    // Валидируем цвет
                    if (preg_match('/^#[0-9A-Fa-f]{6}$/', $value)) {
                        $currentSettings['colors'][$key] = $value;
                    }
                }
            }
        }

        if (isset($_POST['effects'])) {
            foreach ($_POST['effects'] as $key => $value) {
                if (isset($currentSettings['effects'][$key])) {
                    if (is_bool($currentSettings['effects'][$key])) {
                        // Обрабатываем чекбоксы
                        $currentSettings['effects'][$key] = ($value === 'on' || $value === '1' || $value === true);
                    } else {
                        $currentSettings['effects'][$key] = is_numeric($value) ? (float)$value : $value;
                    }
                }
            }
        }

        if (isset($_POST['layout'])) {
            foreach ($_POST['layout'] as $key => $value) {
                if (isset($currentSettings['layout'][$key])) {
                    $currentSettings['layout'][$key] = is_numeric($value) ? (int)$value : $value;
                }
            }
        }

        if (isset($_POST['theme'])) {
            $allowedThemes = ['cyberpunk', 'minimal', 'geometric'];
            if (in_array($_POST['theme'], $allowedThemes)) {
                $currentSettings['theme'] = $_POST['theme'];
            }
        }

        // Проверяем права на запись
        if (!is_writable(dirname($designSettingsFile))) {
            throw new Exception('Нет прав на запись в директорию: ' . dirname($designSettingsFile));
        }

        // Сохраняем настройки (пробуем основной файл, потом альтернативный)
        $savedFile = null;
        if (saveDesignSettings($designSettingsFile, $currentSettings)) {
            $savedFile = $designSettingsFile;
        } elseif (saveDesignSettings($altDesignSettingsFile, $currentSettings)) {
            $savedFile = $altDesignSettingsFile;
        }

        if ($savedFile) {
            error_log("Design settings saved successfully to: " . $savedFile);
            echo json_encode([
                'success' => true,
                'message' => 'Настройки успешно сохранены!',
                'file' => $savedFile,
                'settings' => $currentSettings
            ]);
        } else {
            $error = error_get_last();
            throw new Exception('Не удалось записать файл ни в одну из директорий. Последняя ошибка: ' . ($error['message'] ?? 'неизвестно'));
        }
    } catch (Exception $e) {
        error_log("Design settings save error: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'message' => 'Ошибка: ' . $e->getMessage(),
            'file' => $designSettingsFile,
            'writable' => is_writable(dirname($designSettingsFile))
        ]);
    }
    exit;
}

// Загружаем текущие настройки для отображения
$currentSettings = loadDesignSettings($designSettingsFile, $defaultDesignSettings);
if (!file_exists($designSettingsFile) && file_exists($altDesignSettingsFile)) {
    $currentSettings = loadDesignSettings($altDesignSettingsFile, $defaultDesignSettings);
}

// Подключаем шаблон заголовка
include 'templates/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'templates/sidebar.php'; ?>

        <!-- Основное содержимое -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">🎨 Настройки дизайна</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-success" onclick="saveDesignSettings()">
                            <i class="bi bi-check-lg"></i> Сохранить
                        </button>
                        <button type="button" class="btn btn-sm btn-secondary" onclick="resetToDefaults()">
                            <i class="bi bi-arrow-clockwise"></i> Сбросить
                        </button>
                    </div>
                </div>
            </div>

            <!-- Уведомления -->
            <div id="alert-container"></div>

            <!-- Форма настроек -->
            <form id="design-settings-form">
                <!-- Основные цвета -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-palette"></i> Основные цвета</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="accent_orange" class="form-label">🔥 Основной акцентный цвет</label>
                                    <div class="input-group">
                                        <input type="color" class="form-control form-control-color" id="accent_orange"
                                               name="colors[accent_orange]" value="<?php echo $currentSettings['colors']['accent_orange']; ?>">
                                        <input type="text" class="form-control" value="<?php echo $currentSettings['colors']['accent_orange']; ?>" readonly>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="primary_dark" class="form-label">🌑 Основной темный цвет</label>
                                    <div class="input-group">
                                        <input type="color" class="form-control form-control-color" id="primary_dark"
                                               name="colors[primary_dark]" value="<?php echo $currentSettings['colors']['primary_dark']; ?>">
                                        <input type="text" class="form-control" value="<?php echo $currentSettings['colors']['primary_dark']; ?>" readonly>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="bg_card" class="form-label">📄 Фон карточек</label>
                                    <div class="input-group">
                                        <input type="color" class="form-control form-control-color" id="bg_card"
                                               name="colors[bg_card]" value="<?php echo $currentSettings['colors']['bg_card']; ?>">
                                        <input type="text" class="form-control" value="<?php echo $currentSettings['colors']['bg_card']; ?>" readonly>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="bg_secondary" class="form-label">🎨 Вторичный фон</label>
                                    <div class="input-group">
                                        <input type="color" class="form-control form-control-color" id="bg_secondary"
                                               name="colors[bg_secondary]" value="<?php echo $currentSettings['colors']['bg_secondary']; ?>">
                                        <input type="text" class="form-control" value="<?php echo $currentSettings['colors']['bg_secondary']; ?>" readonly>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="accent_neon" class="form-label">💎 Неоновый акцент</label>
                                    <div class="input-group">
                                        <input type="color" class="form-control form-control-color" id="accent_neon"
                                               name="colors[accent_neon]" value="<?php echo $currentSettings['colors']['accent_neon']; ?>">
                                        <input type="text" class="form-control" value="<?php echo $currentSettings['colors']['accent_neon']; ?>" readonly>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="accent_green" class="form-label">💚 Зеленый акцент</label>
                                    <div class="input-group">
                                        <input type="color" class="form-control form-control-color" id="accent_green"
                                               name="colors[accent_green]" value="<?php echo $currentSettings['colors']['accent_green']; ?>">
                                        <input type="text" class="form-control" value="<?php echo $currentSettings['colors']['accent_green']; ?>" readonly>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="text_primary" class="form-label">📝 Основной текст</label>
                                    <div class="input-group">
                                        <input type="color" class="form-control form-control-color" id="text_primary"
                                               name="colors[text_primary]" value="<?php echo $currentSettings['colors']['text_primary']; ?>">
                                        <input type="text" class="form-control" value="<?php echo $currentSettings['colors']['text_primary']; ?>" readonly>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="text_secondary" class="form-label">📄 Вторичный текст</label>
                                    <div class="input-group">
                                        <input type="color" class="form-control form-control-color" id="text_secondary"
                                               name="colors[text_secondary]" value="<?php echo $currentSettings['colors']['text_secondary']; ?>">
                                        <input type="text" class="form-control" value="<?php echo $currentSettings['colors']['text_secondary']; ?>" readonly>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="border_color" class="form-label">🔲 Цвет границ</label>
                                    <div class="input-group">
                                        <input type="color" class="form-control form-control-color" id="border_color"
                                               name="colors[border_color]" value="<?php echo $currentSettings['colors']['border_color']; ?>">
                                        <input type="text" class="form-control" value="<?php echo $currentSettings['colors']['border_color']; ?>" readonly>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="cyber_border" class="form-label">⚡ Кибер границы</label>
                                    <div class="input-group">
                                        <input type="color" class="form-control form-control-color" id="cyber_border"
                                               name="colors[cyber_border]" value="<?php echo $currentSettings['colors']['cyber_border']; ?>">
                                        <input type="text" class="form-control" value="<?php echo $currentSettings['colors']['cyber_border']; ?>" readonly>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="shadow_orange" class="form-label">🌟 Оранжевое свечение</label>
                                    <div class="input-group">
                                        <input type="color" class="form-control form-control-color" id="shadow_orange"
                                               name="colors[shadow_orange]" value="<?php echo $currentSettings['colors']['shadow_orange']; ?>">
                                        <input type="text" class="form-control" value="<?php echo $currentSettings['colors']['shadow_orange']; ?>" readonly>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="shadow_neon" class="form-label">✨ Неоновое свечение</label>
                                    <div class="input-group">
                                        <input type="color" class="form-control form-control-color" id="shadow_neon"
                                               name="colors[shadow_neon]" value="<?php echo $currentSettings['colors']['shadow_neon']; ?>">
                                        <input type="text" class="form-control" value="<?php echo $currentSettings['colors']['shadow_neon']; ?>" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Криптоиконки -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-currency-bitcoin"></i> Настройки криптоиконок</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="crypto_opacity" class="form-label">💫 Прозрачность криптоиконок</label>
                                    <input type="range" class="form-range" id="crypto_opacity"
                                           name="effects[crypto_opacity]" min="0.1" max="1" step="0.1"
                                           value="<?php echo $currentSettings['effects']['crypto_opacity']; ?>">
                                    <div class="form-text">Текущее значение: <span id="crypto_opacity_value"><?php echo $currentSettings['effects']['crypto_opacity']; ?></span></div>
                                </div>
                                <div class="mb-3">
                                    <label for="crypto_size" class="form-label">📏 Размер криптоиконок (px)</label>
                                    <input type="range" class="form-range" id="crypto_size"
                                           name="effects[crypto_size]" min="50" max="200" step="10"
                                           value="<?php echo $currentSettings['effects']['crypto_size']; ?>">
                                    <div class="form-text">Текущее значение: <span id="crypto_size_value"><?php echo $currentSettings['effects']['crypto_size']; ?></span>px</div>
                                </div>
                                <div class="mb-3">
                                    <label for="crypto_speed" class="form-label">⚡ Скорость анимации (сек)</label>
                                    <input type="range" class="form-range" id="crypto_speed"
                                           name="effects[crypto_speed]" min="5" max="30" step="1"
                                           value="<?php echo $currentSettings['effects']['crypto_speed']; ?>">
                                    <div class="form-text">Текущее значение: <span id="crypto_speed_value"><?php echo $currentSettings['effects']['crypto_speed']; ?></span>с</div>
                                </div>
                                <div class="mb-3">
                                    <label for="crypto_amplitude" class="form-label">🌊 Амплитуда движения (px)</label>
                                    <input type="range" class="form-range" id="crypto_amplitude"
                                           name="effects[crypto_amplitude]" min="20" max="150" step="10"
                                           value="<?php echo $currentSettings['effects']['crypto_amplitude']; ?>">
                                    <div class="form-text">Текущее значение: <span id="crypto_amplitude_value"><?php echo $currentSettings['effects']['crypto_amplitude']; ?></span>px</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="crypto_glow_intensity" class="form-label">✨ Интенсивность свечения</label>
                                    <input type="range" class="form-range" id="crypto_glow_intensity"
                                           name="effects[crypto_glow_intensity]" min="0" max="1" step="0.1"
                                           value="<?php echo $currentSettings['effects']['crypto_glow_intensity']; ?>">
                                    <div class="form-text">Текущее значение: <span id="crypto_glow_intensity_value"><?php echo $currentSettings['effects']['crypto_glow_intensity']; ?></span></div>
                                </div>
                                <div class="mb-3">
                                    <label for="crypto_blur" class="form-label">🌫️ Размытие криптоиконок</label>
                                    <input type="range" class="form-range" id="crypto_blur"
                                           name="effects[crypto_blur]" min="0" max="10" step="1"
                                           value="<?php echo $currentSettings['effects']['crypto_blur']; ?>">
                                    <div class="form-text">Текущее значение: <span id="crypto_blur_value"><?php echo $currentSettings['effects']['crypto_blur']; ?></span>px</div>
                                </div>
                                <div class="mb-3">
                                    <label for="background_blur" class="form-label">🎭 Размытие фона</label>
                                    <input type="range" class="form-range" id="background_blur"
                                           name="effects[background_blur]" min="0" max="10" step="1"
                                           value="<?php echo $currentSettings['effects']['background_blur']; ?>">
                                    <div class="form-text">Текущее значение: <span id="background_blur_value"><?php echo $currentSettings['effects']['background_blur']; ?></span>px</div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enable_crypto_icons"
                                               name="effects[enable_crypto_icons]" <?php echo $currentSettings['effects']['enable_crypto_icons'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="enable_crypto_icons">
                                            🪙 Включить криптоиконки
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Визуальные эффекты -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-stars"></i> Визуальные эффекты</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enable_glitch_effects"
                                               name="effects[enable_glitch_effects]" <?php echo $currentSettings['effects']['enable_glitch_effects'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="enable_glitch_effects">
                                            ⚡ Включить глитч эффекты
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enable_glow_effects"
                                               name="effects[enable_glow_effects]" <?php echo $currentSettings['effects']['enable_glow_effects'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="enable_glow_effects">
                                            ✨ Включить эффекты свечения
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enable_animations"
                                               name="effects[enable_animations]" <?php echo $currentSettings['effects']['enable_animations'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="enable_animations">
                                            🎬 Включить анимации
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="button_glow"
                                               name="effects[button_glow]" <?php echo $currentSettings['effects']['button_glow'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="button_glow">
                                            🔘 Свечение кнопок
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="card_borders"
                                               name="effects[card_borders]" <?php echo $currentSettings['effects']['card_borders'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="card_borders">
                                            🎨 Цветные границы карточек
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="text_shadows"
                                               name="effects[text_shadows]" <?php echo $currentSettings['effects']['text_shadows'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="text_shadows">
                                            📝 Тени текста
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Макет и размеры -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-grid-3x3"></i> Макет и размеры</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="border_radius" class="form-label">🔲 Радиус границ (px)</label>
                                    <input type="range" class="form-range" id="border_radius"
                                           name="layout[border_radius]" min="0" max="30" step="2"
                                           value="<?php echo $currentSettings['layout']['border_radius']; ?>">
                                    <div class="form-text">Текущее значение: <span id="border_radius_value"><?php echo $currentSettings['layout']['border_radius']; ?></span>px</div>
                                </div>
                                <div class="mb-3">
                                    <label for="button_radius" class="form-label">🔘 Радиус кнопок (px)</label>
                                    <input type="range" class="form-range" id="button_radius"
                                           name="layout[button_radius]" min="0" max="25" step="1"
                                           value="<?php echo $currentSettings['layout']['button_radius']; ?>">
                                    <div class="form-text">Текущее значение: <span id="button_radius_value"><?php echo $currentSettings['layout']['button_radius']; ?></span>px</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="card_padding" class="form-label">📦 Отступы карточек (px)</label>
                                    <input type="range" class="form-range" id="card_padding"
                                           name="layout[card_padding]" min="10" max="40" step="2"
                                           value="<?php echo $currentSettings['layout']['card_padding']; ?>">
                                    <div class="form-text">Текущее значение: <span id="card_padding_value"><?php echo $currentSettings['layout']['card_padding']; ?></span>px</div>
                                </div>
                                <div class="mb-3">
                                    <label for="spacing" class="form-label">📏 Общие отступы (px)</label>
                                    <input type="range" class="form-range" id="spacing"
                                           name="layout[spacing]" min="8" max="32" step="2"
                                           value="<?php echo $currentSettings['layout']['spacing']; ?>">
                                    <div class="form-text">Текущее значение: <span id="spacing_value"><?php echo $currentSettings['layout']['spacing']; ?></span>px</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Тема -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-brush"></i> Тема оформления</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="theme" class="form-label">🎭 Выберите тему</label>
                                    <select class="form-select" id="theme" name="theme">
                                        <option value="cyberpunk" <?php echo $currentSettings['theme'] === 'cyberpunk' ? 'selected' : ''; ?>>🤖 Cyberpunk - Неоновый киберпанк стиль</option>
                                        <option value="minimal" <?php echo $currentSettings['theme'] === 'minimal' ? 'selected' : ''; ?>>🎯 Minimal - Минималистичный дизайн</option>
                                        <option value="geometric" <?php echo $currentSettings['theme'] === 'geometric' ? 'selected' : ''; ?>>📐 Geometric - Геометрические формы</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Кнопки управления -->
                <div class="card mb-4">
                    <div class="card-body text-center">
                        <button type="button" class="btn btn-success btn-lg me-3" onclick="saveDesignSettings()">
                            <i class="bi bi-check-lg"></i> Сохранить настройки
                        </button>
                        <button type="button" class="btn btn-secondary btn-lg me-3" onclick="resetToDefaults()">
                            <i class="bi bi-arrow-clockwise"></i> Сбросить к умолчаниям
                        </button>
                        <a href="../../index.html" target="_blank" class="btn btn-primary btn-lg">
                            <i class="bi bi-box-arrow-up-right"></i> Предварительный просмотр
                        </a>
                    </div>
                </div>
            </form>

            <!-- Предварительный просмотр -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-eye"></i> Предварительный просмотр</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i>
                        Для просмотра изменений откройте приложение в новой вкладке: 
                        <a href="../../index.html" target="_blank" class="btn btn-sm btn-primary ms-2">
                            <i class="bi bi-box-arrow-up-right"></i> Открыть приложение
                        </a>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
// AJAX сохранение настроек дизайна
function saveDesignSettings() {
    const form = document.getElementById('design-settings-form');
    const formData = new FormData(form);
    formData.append('ajax', '1');
    
    // Показываем индикатор загрузки
    showAlert('info', 'Сохранение настроек...', false);
    
    fetch('design_settings.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'Ошибка при сохранении настроек');
    });
}

// Сброс к настройкам по умолчанию
function resetToDefaults() {
    if (confirm('Вы уверены, что хотите сбросить все настройки к значениям по умолчанию?')) {
        // Цвета
        document.getElementById('accent_orange').value = '#ff6b35';
        document.getElementById('primary_dark').value = '#2a2a2a';
        document.getElementById('accent_neon').value = '#00ffff';
        document.getElementById('accent_green').value = '#39ff14';
        document.getElementById('bg_card').value = '#3a3a3a';
        document.getElementById('bg_secondary').value = '#333333';
        document.getElementById('text_primary').value = '#ffffff';
        document.getElementById('text_secondary').value = '#cccccc';
        document.getElementById('border_color').value = '#4a4a4a';
        document.getElementById('cyber_border').value = '#00ffff';
        document.getElementById('shadow_orange').value = '#ff6b35';
        document.getElementById('shadow_neon').value = '#00ffff';

        // Эффекты
        document.getElementById('crypto_opacity').value = '0.6';
        document.getElementById('crypto_size').value = '100';
        document.getElementById('crypto_speed').value = '15';
        document.getElementById('crypto_amplitude').value = '80';
        document.getElementById('crypto_glow_intensity').value = '0.4';
        document.getElementById('crypto_blur').value = '2';
        document.getElementById('background_blur').value = '2';

        // Переключатели
        document.getElementById('enable_crypto_icons').checked = true;
        document.getElementById('enable_glitch_effects').checked = false;
        document.getElementById('enable_glow_effects').checked = true;
        document.getElementById('enable_animations').checked = true;
        document.getElementById('button_glow').checked = true;
        document.getElementById('card_borders').checked = true;
        document.getElementById('text_shadows').checked = true;

        // Макет
        document.getElementById('border_radius').value = '12';
        document.getElementById('button_radius').value = '8';
        document.getElementById('card_padding').value = '20';
        document.getElementById('spacing').value = '16';

        // Тема
        document.getElementById('theme').value = 'cyberpunk';

        // Обновляем отображаемые значения и цветовые поля
        updateSliderValues();
        updateColorInputs();

        showAlert('info', 'Настройки сброшены к значениям по умолчанию. Нажмите "Сохранить" для применения.');
    }
}

// Показ уведомлений
function showAlert(type, message, autoHide = true) {
    const container = document.getElementById('alert-container');
    const alertId = 'alert-' + Date.now();
    
    const alertHtml = `
        <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    container.innerHTML = alertHtml;
    
    if (autoHide && type !== 'danger') {
        setTimeout(() => {
            const alert = document.getElementById(alertId);
            if (alert) {
                alert.remove();
            }
        }, 3000);
    }
}

// Обновление значений слайдеров
function updateSliderValues() {
    const sliders = [
        'crypto_opacity', 'crypto_size', 'crypto_speed', 'crypto_amplitude',
        'crypto_glow_intensity', 'crypto_blur', 'background_blur',
        'border_radius', 'button_radius', 'card_padding', 'spacing'
    ];

    sliders.forEach(sliderId => {
        const slider = document.getElementById(sliderId);
        const valueSpan = document.getElementById(sliderId + '_value');

        if (slider && valueSpan) {
            let suffix = '';
            if (sliderId.includes('size') || sliderId.includes('amplitude') || sliderId.includes('blur') ||
                sliderId.includes('radius') || sliderId.includes('padding') || sliderId.includes('spacing')) {
                suffix = 'px';
            } else if (sliderId.includes('speed')) {
                suffix = 'с';
            }

            valueSpan.textContent = slider.value + suffix;

            slider.addEventListener('input', function() {
                valueSpan.textContent = this.value + suffix;
            });
        }
    });
}

// Обновление текстовых полей цветов
function updateColorInputs() {
    const colorInputs = document.querySelectorAll('input[type="color"]');

    colorInputs.forEach(colorInput => {
        const textInput = colorInput.parentElement.querySelector('input[type="text"]');
        if (textInput) {
            textInput.value = colorInput.value;

            colorInput.addEventListener('input', function() {
                textInput.value = this.value;
            });
        }
    });
}

// Инициализация при загрузке страницы
document.addEventListener('DOMContentLoaded', function() {
    updateSliderValues();
    updateColorInputs();

    // Добавляем обработчики для автосохранения (опционально)
    const form = document.getElementById('design-settings-form');
    if (form) {
        // Можно добавить автосохранение при изменении
        // form.addEventListener('change', saveDesignSettings);
    }
});
</script>

<?php include 'templates/footer.php'; ?>
