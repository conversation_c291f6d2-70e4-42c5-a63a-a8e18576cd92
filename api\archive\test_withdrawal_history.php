<?php
/**
 * Тестирование истории выплат пользователя
 */

require_once 'api/config.php';
require_once 'api/db_mock.php';
require_once 'api/validate_initdata.php';
require_once 'api/getWithdrawalHistory.php';

// Имитируем запрос пользователя
$initData = 'query_id=AAHdF6JSAAAAAG0g8dC8dK0R&user=%7B%22id%22%3A5880288830%2C%22first_name%22%3A%22%D0%90%D0%BB%D1%8C%D1%82%D0%B5%D1%80%22%2C%22last_name%22%3A%22%D0%AD%D0%B3%D0%BE%22%2C%22username%22%3A%22alter_mega_ego%22%7D&auth_date=1749057489&hash=cc5d4e147e81d1655d6c5f15b62a2433c7c5f3f5a5e5e5e5e5e5e5e5e5e5e5e5e';

// Создаем входные данные
$input = ['initData' => $initData];

// Вызываем функцию обработки напрямую
$response = getWithdrawalHistory($input);

// Выводим результат теста
echo "=== Тест истории выплат ===\n";
echo "Успех: " . ($response['success'] ? 'Да' : 'Нет') . "\n";
echo "Сообщение: " . ($response['message'] ?? '') . "\n";
echo "Количество выплат: " . ($response['total_count'] ?? 0) . "\n\n";

if (!empty($response['withdrawals'])) {
    echo "История выплат:\n";
    foreach ($response['withdrawals'] as $withdrawal) {
        echo "ID: " . ($withdrawal['id'] ?? '') . "\n";
        echo "Статус: " . ($withdrawal['status'] ?? '') . "\n";
        echo "Сумма: " . ($withdrawal['coins_amount'] ?? '') . " монет\n";
        echo "Валюта: " . ($withdrawal['currency'] ?? '') . "\n";
        echo "Адрес: " . ($withdrawal['address'] ?? '') . "\n";
        echo "Дата: " . ($withdrawal['created_at'] ?? '') . "\n";
        echo "TXID: " . ($withdrawal['txid'] ?? 'отсутствует') . "\n";
        echo str_repeat('-', 40) . "\n";
    }
} else {
    echo "История выплат пуста\n";
}

/**
 * Прямой вызов функции API (копия из getWithdrawalHistory.php)
 */
function getWithdrawalHistory($input) {
    $initData = $input['initData'] ?? '';
    
    // Валидация initData
    $validatedData = validateTelegramInitData($initData);
    if ($validatedData === false) {
        return ['success' => false, 'error' => 'Ошибка: Неверные данные'];
    }
    $userId = intval($validatedData['user']['id']);

    // Загрузка данных пользователя
    $userData = loadUserData();
    if (!is_array($userData) || !isset($userData[$userId])) {
        return ['success' => false, 'error' => 'Ошибка: Пользователь не найден'];
    }

    // Получение истории выплат
    $withdrawals = $userData[$userId]['withdrawals'] ?? [];

    // Сортируем по времени (от новых к старым)
    usort($withdrawals, function($a, $b) {
        return ($b['timestamp'] ?? 0) - ($a['timestamp'] ?? 0);
    });

    return [
        'success' => true,
        'withdrawals' => $withdrawals,
        'total_count' => count($withdrawals)
    ];
}
?>