<?php
/**
 * Тестирование синхронизации данных между калькулятором и выплатами
 */

require_once 'config.php';
require_once 'FeeCalculator.php';

echo "🔄 ТЕСТИРОВАНИЕ СИНХРОНИЗАЦИИ ДАННЫХ\n";
echo str_repeat("=", 60) . "\n\n";

// Тестовые сценарии для проверки синхронизации
$syncTestCases = [
    [
        'name' => 'ETH - Стандартная сумма',
        'coins' => 1000,
        'currency' => 'eth',
        'address' => '******************************************'
    ],
    [
        'name' => 'BTC - Большая сумма',
        'coins' => 5000,
        'currency' => 'btc',
        'address' => '**********************************'
    ],
    [
        'name' => 'TON - Средняя сумма',
        'coins' => 3000,
        'currency' => 'ton',
        'address' => 'EQD4FPq-PRDieyQKkizFTRtSDyucUIqrj0v_zXJmqaDp6_0t'
    ],
    [
        'name' => 'USDT TRC20 - Крупная сумма',
        'coins' => 20000,
        'currency' => 'usdttrc20',
        'address' => 'TLa2f6VPqDgRE67v1736s7bJ8Ray5wYjU7'
    ]
];

echo "🎯 ТЕСТ 1: СИНХРОНИЗАЦИЯ КАЛЬКУЛЯТОРА И ВЫПЛАТ\n";
echo str_repeat("-", 40) . "\n\n";

$feeCalculator = FeeCalculator::getInstance();
$syncResults = [];

foreach ($syncTestCases as $i => $testCase) {
    echo "📊 Тест " . ($i + 1) . ": {$testCase['name']}\n";
    echo "   💰 Сумма: {$testCase['coins']} монет\n";
    echo "   🪙 Валюта: {$testCase['currency']}\n";
    
    // 1. Расчет через калькулятор
    echo "\n   1️⃣ Расчет через калькулятор:\n";
    $calculatorResult = $feeCalculator->calculateWithdrawalAmount($testCase['coins'], $testCase['currency']);
    
    if ($calculatorResult['success']) {
        echo "      ✅ Успех\n";
        echo "      💰 К получению: {$calculatorResult['crypto_amount']} {$testCase['currency']}\n";
        echo "      💵 USD: \${$calculatorResult['usd_amount']}\n";
        echo "      💸 Комиссия: {$calculatorResult['nowpayments_fee']} {$testCase['currency']}\n";
        echo "      🔧 Метод: {$calculatorResult['calculation_method']}\n";
    } else {
        echo "      ❌ Ошибка: {$calculatorResult['error']}\n";
        continue;
    }
    
    // 2. Имитация создания выплаты
    echo "\n   2️⃣ Создание выплаты:\n";
    $withdrawalResult = simulateWithdrawalCreation($testCase, $calculatorResult);
    
    if ($withdrawalResult['success']) {
        echo "      ✅ Выплата создана\n";
        echo "      🆔 ID: {$withdrawalResult['withdrawal_id']}\n";
        echo "      💰 Сумма в выплате: {$withdrawalResult['crypto_amount']} {$testCase['currency']}\n";
        echo "      📊 Статус: {$withdrawalResult['status']}\n";
    } else {
        echo "      ❌ Ошибка создания: {$withdrawalResult['error']}\n";
        continue;
    }
    
    // 3. Проверка синхронизации
    echo "\n   3️⃣ Проверка синхронизации:\n";
    $syncCheck = checkDataSynchronization($calculatorResult, $withdrawalResult);
    
    echo "      💰 Суммы синхронизированы: " . ($syncCheck['amounts_match'] ? "✅" : "❌") . "\n";
    echo "      💵 USD синхронизированы: " . ($syncCheck['usd_match'] ? "✅" : "❌") . "\n";
    echo "      💸 Комиссии синхронизированы: " . ($syncCheck['fees_match'] ? "✅" : "❌") . "\n";
    echo "      🔧 Методы синхронизированы: " . ($syncCheck['methods_match'] ? "✅" : "❌") . "\n";
    
    if ($syncCheck['fully_synchronized']) {
        echo "      🎉 Полная синхронизация!\n";
    } else {
        echo "      ⚠️ Обнаружены расхождения:\n";
        foreach ($syncCheck['differences'] as $diff) {
            echo "         - {$diff}\n";
        }
    }
    
    // Сохраняем результат
    $syncResults[] = [
        'test_case' => $testCase,
        'calculator' => $calculatorResult,
        'withdrawal' => $withdrawalResult,
        'sync_check' => $syncCheck
    ];
    
    echo "\n" . str_repeat("=", 60) . "\n\n";
}

echo "🎯 ТЕСТ 2: ПРОВЕРКА КОНСИСТЕНТНОСТИ ДАННЫХ\n";
echo str_repeat("-", 40) . "\n\n";

// Проверяем консистентность между множественными вызовами
echo "📊 Тестирование консистентности расчетов:\n";

$consistencyTests = [
    ['coins' => 1000, 'currency' => 'eth'],
    ['coins' => 2500, 'currency' => 'btc']
];

foreach ($consistencyTests as $test) {
    echo "\n   🔄 Тест консистентности: {$test['coins']} монет → {$test['currency']}\n";
    
    $results = [];
    for ($i = 0; $i < 5; $i++) {
        $result = $feeCalculator->calculateWithdrawalAmount($test['coins'], $test['currency']);
        if ($result['success']) {
            $results[] = $result['crypto_amount'];
        }
    }
    
    if (count($results) === 5) {
        $allSame = count(array_unique($results)) === 1;
        echo "      " . ($allSame ? "✅" : "⚠️") . " Консистентность: " . ($allSame ? "ДА" : "НЕТ") . "\n";
        
        if (!$allSame) {
            echo "      📊 Результаты: " . implode(', ', $results) . "\n";
            $variance = max($results) - min($results);
            echo "      📊 Разброс: " . number_format($variance, 8) . "\n";
        }
    } else {
        echo "      ❌ Не все расчеты успешны\n";
    }
}

echo "\n🎯 ТЕСТ 3: ПРОВЕРКА ВРЕМЕННОЙ СТАБИЛЬНОСТИ\n";
echo str_repeat("-", 40) . "\n\n";

// Тестируем стабильность расчетов во времени
echo "📊 Тестирование временной стабильности:\n";

$timeTest = ['coins' => 1000, 'currency' => 'eth'];
$timeResults = [];

echo "   🕐 Выполняем расчеты с интервалом...\n";

for ($i = 0; $i < 3; $i++) {
    $result = $feeCalculator->calculateWithdrawalAmount($timeTest['coins'], $timeTest['currency']);
    if ($result['success']) {
        $timeResults[] = [
            'time' => time(),
            'amount' => $result['crypto_amount'],
            'fee' => $result['nowpayments_fee']
        ];
        echo "      ⏰ Расчет " . ($i + 1) . ": {$result['crypto_amount']} ETH (комиссия: {$result['nowpayments_fee']})\n";
    }
    
    if ($i < 2) sleep(2); // Пауза между расчетами
}

if (count($timeResults) === 3) {
    $amounts = array_column($timeResults, 'amount');
    $fees = array_column($timeResults, 'fee');
    
    $amountStable = count(array_unique($amounts)) <= 2; // Допускаем небольшие колебания
    $feeStable = count(array_unique($fees)) <= 2;
    
    echo "   " . ($amountStable ? "✅" : "⚠️") . " Стабильность сумм: " . ($amountStable ? "ДА" : "НЕТ") . "\n";
    echo "   " . ($feeStable ? "✅" : "⚠️") . " Стабильность комиссий: " . ($feeStable ? "ДА" : "НЕТ") . "\n";
}

echo "\n🎯 ТЕСТ 4: ПРОВЕРКА ПРОИЗВОДИТЕЛЬНОСТИ СИНХРОНИЗАЦИИ\n";
echo str_repeat("-", 40) . "\n\n";

$startTime = microtime(true);
$batchSize = 50;

echo "📊 Тестирование производительности ({$batchSize} операций):\n";

$performanceResults = [];
for ($i = 0; $i < $batchSize; $i++) {
    $testCoins = 1000 + ($i * 100);
    $testCurrency = ['eth', 'btc', 'ton', 'usdttrc20'][$i % 4];
    
    $calcStart = microtime(true);
    $calcResult = $feeCalculator->calculateWithdrawalAmount($testCoins, $testCurrency);
    $calcTime = microtime(true) - $calcStart;
    
    if ($calcResult['success']) {
        $withdrawalStart = microtime(true);
        $withdrawalResult = simulateWithdrawalCreation(
            ['coins' => $testCoins, 'currency' => $testCurrency, 'address' => 'test_address'],
            $calcResult
        );
        $withdrawalTime = microtime(true) - $withdrawalStart;
        
        $performanceResults[] = [
            'calc_time' => $calcTime,
            'withdrawal_time' => $withdrawalTime,
            'total_time' => $calcTime + $withdrawalTime
        ];
    }
}

$endTime = microtime(true);
$totalTime = $endTime - $startTime;

if (count($performanceResults) > 0) {
    $avgCalcTime = array_sum(array_column($performanceResults, 'calc_time')) / count($performanceResults);
    $avgWithdrawalTime = array_sum(array_column($performanceResults, 'withdrawal_time')) / count($performanceResults);
    $avgTotalTime = array_sum(array_column($performanceResults, 'total_time')) / count($performanceResults);
    
    echo "   ⏱️ Общее время: " . number_format($totalTime, 3) . " сек\n";
    echo "   📊 Среднее время расчета: " . number_format($avgCalcTime * 1000, 2) . " мс\n";
    echo "   📊 Среднее время создания выплаты: " . number_format($avgWithdrawalTime * 1000, 2) . " мс\n";
    echo "   📊 Среднее общее время: " . number_format($avgTotalTime * 1000, 2) . " мс\n";
    echo "   🚀 Операций в секунду: " . number_format($batchSize / $totalTime, 0) . "\n";
    
    if ($avgTotalTime < 0.1) {
        echo "   ✅ Производительность отличная\n";
    } elseif ($avgTotalTime < 0.5) {
        echo "   ✅ Производительность хорошая\n";
    } else {
        echo "   ⚠️ Производительность требует оптимизации\n";
    }
}

// Финальная сводка
echo "\n📈 ФИНАЛЬНАЯ СВОДКА СИНХРОНИЗАЦИИ\n";
echo str_repeat("-", 40) . "\n";

$totalSyncTests = count($syncResults);
$fullySyncedTests = count(array_filter($syncResults, function($result) {
    return $result['sync_check']['fully_synchronized'];
}));

echo "📊 Всего тестов синхронизации: {$totalSyncTests}\n";
echo "✅ Полностью синхронизированы: {$fullySyncedTests}\n";
echo "⚠️ С расхождениями: " . ($totalSyncTests - $fullySyncedTests) . "\n";
echo "📈 Процент синхронизации: " . round(($fullySyncedTests / $totalSyncTests) * 100, 1) . "%\n";

if ($fullySyncedTests === $totalSyncTests) {
    echo "\n🎉 ВСЕ ДАННЫЕ ПОЛНОСТЬЮ СИНХРОНИЗИРОВАНЫ!\n";
    echo "✅ Калькулятор и система выплат работают согласованно\n";
    echo "✅ Пользователи получают именно то, что показывает калькулятор\n";
} else {
    echo "\n⚠️ Обнаружены расхождения в синхронизации\n";
    echo "🔧 Требуется дополнительная настройка системы\n";
}

echo "\n🎉 ТЕСТИРОВАНИЕ СИНХРОНИЗАЦИИ ЗАВЕРШЕНО!\n";

/**
 * Имитирует создание выплаты на основе данных калькулятора
 */
function simulateWithdrawalCreation($testCase, $calculatorResult) {
    try {
        $withdrawalId = 'sync_test_' . time() . '_' . rand(1000, 9999);
        
        // Используем данные из калькулятора
        $withdrawalData = [
            'id' => $withdrawalId,
            'payout_id' => $withdrawalId,
            'status' => 'waiting',
            'coins_amount' => $testCase['coins'],
            'usd_amount' => $calculatorResult['usd_amount'],
            'crypto_amount' => $calculatorResult['crypto_amount'],
            'crypto_amount_gross' => $calculatorResult['crypto_amount_gross'],
            'nowpayments_fee' => $calculatorResult['nowpayments_fee'],
            'currency' => $testCase['currency'],
            'address' => $testCase['address'] ?? 'test_address',
            'calculation_method' => $calculatorResult['calculation_method'],
            'timestamp' => time(),
            'created_at' => date('Y-m-d H:i:s'),
            'test_mode' => true
        ];
        
        return [
            'success' => true,
            'withdrawal_id' => $withdrawalId,
            'status' => 'waiting',
            'crypto_amount' => $withdrawalData['crypto_amount'],
            'withdrawal_data' => $withdrawalData
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => 'Ошибка создания выплаты: ' . $e->getMessage()
        ];
    }
}

/**
 * Проверяет синхронизацию данных между калькулятором и выплатой
 */
function checkDataSynchronization($calculatorResult, $withdrawalResult) {
    $differences = [];
    
    // Проверяем суммы
    $amountDiff = abs($calculatorResult['crypto_amount'] - $withdrawalResult['crypto_amount']);
    $amountsMatch = $amountDiff < 0.00000001; // Очень маленький допуск
    
    if (!$amountsMatch) {
        $differences[] = "Суммы не совпадают: калькулятор {$calculatorResult['crypto_amount']}, выплата {$withdrawalResult['crypto_amount']}";
    }
    
    // Проверяем USD суммы
    $withdrawalData = $withdrawalResult['withdrawal_data'];
    $usdDiff = abs($calculatorResult['usd_amount'] - $withdrawalData['usd_amount']);
    $usdMatch = $usdDiff < 0.01;
    
    if (!$usdMatch) {
        $differences[] = "USD суммы не совпадают: калькулятор {$calculatorResult['usd_amount']}, выплата {$withdrawalData['usd_amount']}";
    }
    
    // Проверяем комиссии
    $feeDiff = abs($calculatorResult['nowpayments_fee'] - $withdrawalData['nowpayments_fee']);
    $feesMatch = $feeDiff < 0.00000001;
    
    if (!$feesMatch) {
        $differences[] = "Комиссии не совпадают: калькулятор {$calculatorResult['nowpayments_fee']}, выплата {$withdrawalData['nowpayments_fee']}";
    }
    
    // Проверяем методы расчета
    $methodsMatch = ($calculatorResult['calculation_method'] === $withdrawalData['calculation_method']);
    
    if (!$methodsMatch) {
        $differences[] = "Методы расчета не совпадают: калькулятор {$calculatorResult['calculation_method']}, выплата {$withdrawalData['calculation_method']}";
    }
    
    return [
        'amounts_match' => $amountsMatch,
        'usd_match' => $usdMatch,
        'fees_match' => $feesMatch,
        'methods_match' => $methodsMatch,
        'fully_synchronized' => $amountsMatch && $usdMatch && $feesMatch && $methodsMatch,
        'differences' => $differences
    ];
}
?>
