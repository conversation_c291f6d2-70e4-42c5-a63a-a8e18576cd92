<?php
/**
 * api/admin/support_chat.php
 * Страница просмотра отдельного чата поддержки
 */

// Подключаем файл с функциями аутентификации и авторизации
require_once __DIR__ . '/auth.php';

// Проверяем аутентификацию
session_start();
if (!isAuthenticated()) {
    header('Location: login.php');
    exit;
}

require_once __DIR__ . '/support_config.php';
require_once __DIR__ . '/support_data.php';

// Получаем ID чата
$chatId = $_GET['chat_id'] ?? '';
if (empty($chatId)) {
    header('Location: support.php');
    exit;
}

// Получаем информацию о чате
$chatInfo = getSupportChatInfo($chatId);
if (!$chatInfo) {
    header('Location: support.php');
    exit;
}

// Получаем сообщения чата
$messages = getChatMessages($chatId);

// Обработка отправки нового сообщения
$sendResult = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['message_text'])) {
    $messageText = trim($_POST['message_text']);
    if (!empty($messageText)) {
        // Отправляем сообщение пользователю через бота
        $result = sendSupportMessage($chatInfo['user_id'], $messageText);
        
        if ($result) {
            // Сохраняем сообщение в базе
            addSupportMessage($chatId, $result['result']['message_id'], false, $messageText);
            $sendResult = 'success';
            
            // Обновляем список сообщений
            $messages = getChatMessages($chatId);
        } else {
            $sendResult = 'error';
        }
    }
}

// Формируем отображаемое имя пользователя
$displayName = $chatInfo['first_name'];
if ($chatInfo['last_name']) {
    $displayName .= ' ' . $chatInfo['last_name'];
}
if ($chatInfo['username']) {
    $displayName .= ' (@' . $chatInfo['username'] . ')';
}

// Подключаем шаблон заголовка
include 'templates/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'templates/sidebar.php'; ?>

        <!-- Основное содержимое -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="bi bi-chat-dots me-2"></i>
                    Чат с <?php echo htmlspecialchars($displayName); ?>
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="support.php" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> Назад к списку
                        </a>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                            <i class="bi bi-arrow-clockwise"></i> Обновить
                        </button>
                    </div>
                </div>
            </div>

            <!-- Информация о пользователе -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Информация о пользователе</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Имя:</strong> <?php echo htmlspecialchars($displayName); ?></p>
                            <p><strong>Telegram ID:</strong> <?php echo $chatInfo['user_id']; ?></p>
                            <p><strong>Язык:</strong>
                                <?php
                                $userLanguage = $chatInfo['language'] ?? 'en';
                                $languageFlag = $userLanguage === 'ru' ? '🇷🇺' : '🇺🇸';
                                $languageName = $userLanguage === 'ru' ? 'Русский' : 'English';
                                echo $languageFlag . ' ' . $languageName . ' (' . strtoupper($userLanguage) . ')';
                                ?>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Первое обращение:</strong> <?php echo date('d.m.Y H:i', strtotime($chatInfo['created_at'])); ?></p>
                            <p><strong>Последняя активность:</strong> <?php echo date('d.m.Y H:i', strtotime($chatInfo['updated_at'])); ?></p>
                            <p><strong>Всего сообщений:</strong> <?php echo count($messages); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Результат отправки сообщения -->
            <?php if ($sendResult === 'success'): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle"></i> Сообщение успешно отправлено пользователю!
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php elseif ($sendResult === 'error'): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle"></i> Ошибка при отправке сообщения. Попробуйте еще раз.
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Чат -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Переписка</h6>
                </div>
                <div class="card-body" style="max-height: 500px; overflow-y: auto;" id="chat-container">
                    <?php if (empty($messages)): ?>
                        <div class="text-center py-4">
                            <i class="bi bi-chat" style="font-size: 3rem; color: #ccc;"></i>
                            <p class="mt-3 text-muted">Пока нет сообщений в этом чате</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($messages as $message): ?>
                            <div class="message-item mb-3 <?php echo $message['from_user'] ? 'user-message' : 'support-message'; ?>">
                                <div class="message-bubble">
                                    <div class="message-text">
                                        <?php echo nl2br(htmlspecialchars($message['text'])); ?>
                                    </div>
                                    <div class="message-meta">
                                        <small class="text-muted">
                                            <?php echo $message['from_user'] ? 'Пользователь' : 'Поддержка'; ?> • 
                                            <?php echo date('d.m.Y H:i', strtotime($message['created_at'])); ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Форма отправки сообщения -->
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Отправить сообщение</h6>
                </div>
                <div class="card-body">
                    <form id="message-form" method="POST" action="">
                        <div class="mb-3">
                            <textarea class="form-control" id="message-text" name="message_text" rows="4"
                                      placeholder="Введите ваш ответ пользователю..." required></textarea>
                        </div>
                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-primary" id="send-button">
                                <i class="bi bi-send"></i> Отправить сообщение
                            </button>
                            <small class="text-muted align-self-center">
                                Сообщение будет отправлено через бота @<?php echo SUPPORT_BOT_USERNAME; ?>
                            </small>
                        </div>
                    </form>

                    <!-- Область для уведомлений -->
                    <div id="message-alerts" class="mt-3"></div>
                </div>
            </div>
        </main>
    </div>
</div>

<style>
.message-item {
    display: flex;
}

.user-message {
    justify-content: flex-start;
}

.support-message {
    justify-content: flex-end;
}

.message-bubble {
    max-width: 70%;
    padding: 12px 16px;
    border-radius: 18px;
    word-wrap: break-word;
}

.user-message .message-bubble {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
}

.support-message .message-bubble {
    background-color: #007bff;
    color: white;
}

.support-message .message-meta small {
    color: rgba(255, 255, 255, 0.8) !important;
}

.message-text {
    margin-bottom: 4px;
}

.message-meta {
    font-size: 0.8em;
}

#chat-container {
    background-color: #fafafa;
    border-radius: 8px;
    padding: 15px;
}
</style>

<script>
// Автоматическая прокрутка к последнему сообщению
document.addEventListener('DOMContentLoaded', function() {
    const chatContainer = document.getElementById('chat-container');
    chatContainer.scrollTop = chatContainer.scrollHeight;
});

// AJAX отправка сообщений
document.getElementById('message-form').addEventListener('submit', function(e) {
    e.preventDefault();

    const messageText = document.getElementById('message-text').value.trim();
    const sendButton = document.getElementById('send-button');
    const alertsContainer = document.getElementById('message-alerts');

    if (!messageText) {
        showAlert('Введите текст сообщения', 'danger');
        return;
    }

    // Блокируем кнопку отправки
    sendButton.disabled = true;
    sendButton.innerHTML = '<i class="bi bi-hourglass-split"></i> Отправка...';

    // Отправляем AJAX запрос
    fetch('send_support_message.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            chat_id: '<?php echo $chatId; ?>',
            message_text: messageText
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('Сообщение успешно отправлено!', 'success');
            document.getElementById('message-text').value = '';

            // Обновляем чат через 1 секунду
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showAlert('Ошибка: ' + (data.error || 'Неизвестная ошибка'), 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Ошибка сети. Попробуйте еще раз.', 'danger');
    })
    .finally(() => {
        // Разблокируем кнопку
        sendButton.disabled = false;
        sendButton.innerHTML = '<i class="bi bi-send"></i> Отправить сообщение';
    });
});

// Функция показа уведомлений
function showAlert(message, type) {
    const alertsContainer = document.getElementById('message-alerts');
    const alertId = 'alert-' + Date.now();

    const alertHtml = `
        <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    alertsContainer.innerHTML = alertHtml;

    // Автоматически скрываем уведомление через 5 секунд
    setTimeout(() => {
        const alertElement = document.getElementById(alertId);
        if (alertElement) {
            alertElement.remove();
        }
    }, 5000);
}

// Автообновление чата каждые 30 секунд
setInterval(function() {
    location.reload();
}, 30000);
</script>

<?php include 'templates/footer.php'; ?>
