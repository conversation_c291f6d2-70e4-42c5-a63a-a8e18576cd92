<?php
/**
 * Тест вызова API getWithdrawalHistory.php
 */

header('Content-Type: text/plain; charset=utf-8');

echo "=== ТЕСТ API getWithdrawalHistory.php ===\n\n";

// Создаем тестовые данные initData
$testInitData = "user=" . urlencode(json_encode([
    'id' => 5880288830,
    'first_name' => 'Альтер',
    'last_name' => 'Эго',
    'username' => 'alter_mega_ego',
    'language_code' => 'ru'
])) . "&auth_date=" . time() . "&hash=test";

echo "Тестовые initData:\n";
echo $testInitData . "\n\n";

// Отправляем POST запрос
$postData = json_encode(['initData' => $testInitData]);

$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => 'Content-Type: application/json',
        'content' => $postData
    ]
]);

echo "Отправляем запрос...\n";

try {
    $response = file_get_contents('http://argun-defolt.loc/api/getWithdrawalHistory.php', false, $context);
    
    echo "Ответ API:\n";
    echo $response . "\n\n";
    
    $decoded = json_decode($response, true);
    if ($decoded) {
        echo "Декодированный ответ:\n";
        if (isset($decoded['withdrawals'])) {
            echo "✅ Количество выплат: " . count($decoded['withdrawals']) . "\n";
            foreach ($decoded['withdrawals'] as $i => $withdrawal) {
                echo "Выплата " . ($i + 1) . ":\n";
                echo "  Сумма: " . ($withdrawal['coins_amount'] ?? 'N/A') . " монет\n";
                echo "  Статус: " . ($withdrawal['status'] ?? 'N/A') . "\n";
                echo "  Валюта: " . ($withdrawal['currency'] ?? 'N/A') . "\n";
                echo "  Адрес: " . ($withdrawal['wallet_address'] ?? $withdrawal['address'] ?? 'N/A') . "\n";
                echo "  Дата: " . (isset($withdrawal['timestamp']) ? date('Y-m-d H:i:s', $withdrawal['timestamp']) : 'N/A') . "\n";
                echo "\n";
            }
        } else {
            echo "❌ Нет поля 'withdrawals' в ответе\n";
        }
        
        if (isset($decoded['error'])) {
            echo "❌ Ошибка в ответе: " . $decoded['error'] . "\n";
        }
    } else {
        echo "❌ Не удалось декодировать JSON ответ\n";
    }
    
} catch (Exception $e) {
    echo "❌ Ошибка запроса: " . $e->getMessage() . "\n";
}

echo "\n=== ТЕСТ ЗАВЕРШЕН ===\n";
?>
