# 🌈 Инструкция по установке супер крутого баннера

## ✨ Что создано

Создан **супер крутой жёлтый баннер с розовыми и салатовыми элементами** для Telegram бота:

### 📁 Файлы:
- `images/bot_welcome_golden_banner.svg` - исходный SVG файл
- `bot/update_golden_banner.php` - скрипт обновления
- `bot/webhook.php` - обновлен для использования нового баннера

## 🎨 Особенности дизайна

### 🌞 Супер яркий жёлтый дизайн:
- **🌞 Яркий жёлтый фон** вместо темного
- **💛 Жёлтые криптовалюты** с объемными градиентами
- **🌸 Розовые элементы:** линии, ромбы, блоки, угловые акценты
- **🍃 Салатовые элементы:** звездочки, круги, соединения
- Объемные 3D эффекты без размытий
- Четкие контуры и тени

### 💛 Жёлтые криптовалюты с иконками:
- **TON** (Telegram) - приоритет #1 с стильной иконкой
- **USDT** (TRC20) - самая популярная стейблкоин с логотипом
- **Bitcoin** (BTC) - классика с символом ₿
- **Ethereum** (ETH) - с характерным ромбом
- Все криптовалюты в ярких жёлтых тонах

### 🏆 Супер крутые элементы:
- **UniQPaid** - крупное название бота в заголовке
- **Центральная жёлтая монета** с розово-салатовой обводкой
- **🌸 Розовые соединительные линии** и декоративные элементы
- **🍃 Салатовые звездочки** и геометрические фигуры
- **Цветные информационные блоки:** розовый, салатовый, жёлтый
- Дизайнерские угловые акценты в розовых и салатовых тонах
- Треугольники, ромбы и круги для украшения

## 🔧 Установка

### Шаг 1: Конвертация SVG в PNG

**Важно:** Telegram Bot API НЕ поддерживает SVG!

#### Способ 1: Онлайн конвертер (рекомендуется)
1. Откройте: https://convertio.co/svg-png/
2. Загрузите файл `images/bot_welcome_golden_banner.svg`
3. Установите размер: **800x400 пикселей** или больше
4. Скачайте PNG файл
5. Переименуйте в `bot_welcome_golden_banner.png`

#### Способ 2: Photoshop/GIMP
1. Откройте `images/bot_welcome_golden_banner.svg`
2. Установите размер: 800x400px
3. Экспортируйте как PNG
4. Сохраните как `bot_welcome_golden_banner.png`

#### Способ 3: Командная строка
```bash
# Если установлен ImageMagick
convert images/bot_welcome_golden_banner.svg -resize 800x400 images/bot_welcome_golden_banner.png

# Если установлен Inkscape
inkscape --export-type=png --export-width=800 --export-height=400 --export-filename=bot_welcome_golden_banner.png bot_welcome_golden_banner.svg
```

### Шаг 2: Загрузка на сервер
1. Загрузите `bot_welcome_golden_banner.png` в папку `images/`
2. Убедитесь, что файл доступен по URL:
   ```
   https://app.uniqpaid.com/test3/images/bot_welcome_golden_banner.png
   ```

### Шаг 3: Получение chat_id
1. Отправьте любое сообщение боту @uniqpaid_paid_bot
2. Посмотрите логи в `bot/bot.log`
3. Найдите строку: `INFO: Сообщение от пользователя 123456789`
4. Число `123456789` - это ваш chat_id

### Шаг 4: Тестирование
Откройте в браузере (замените YOUR_CHAT_ID):
```
https://app.uniqpaid.com/test3/bot/update_golden_banner.php?chat_id=YOUR_CHAT_ID
```

## ✅ Проверка

### 1. Проверьте доступность картинки
Откройте в браузере:
```
https://app.uniqpaid.com/test3/images/bot_welcome_golden_banner.png
```

### 2. Протестируйте бота
1. Отправьте `/start` боту @uniqpaid_paid_bot
2. Должен появиться новый золотистый баннер!

## 🎯 Результат

После установки пользователи будут видеть:

### 🌟 Премиальный золотистый баннер с:
- Роскошными золотыми градиентами
- Актуальными криптовалютами
- Объемными 3D эффектами
- Элегантным дизайном

### 💬 Приветственное сообщение:
```
🎉 Добро пожаловать в UniQPaid!

💰 Просто смотри рекламу сейчас и получай крипту сразу!
🚀 Не нужно ждать листинга! Моментальные автовыплаты на кошелёк!

📊 Как это работает:
• Смотрите рекламу и получайте до 10 монет за каждый просмотр
• Приглашайте друзей и получайте 10% от их заработка  
• Выводите заработанные средства на криптокошельки

💎 Курс: 10 монет = $0.01 USD
🚀 Начните зарабатывать прямо сейчас!
```

## 🔄 Откат к предыдущему баннеру

Если нужно вернуть старый баннер, измените в `bot/webhook.php`:
```php
// Вместо:
$logoUrl = 'https://app.uniqpaid.com/test3/images/bot_welcome_golden_banner.png?v=' . $timestamp;

// Используйте:
$logoUrl = 'https://app.uniqpaid.com/test3/images/bot_welcome_super_banner.png?v=' . $timestamp;
```

## 🎨 Кастомизация

Для изменения дизайна отредактируйте `images/bot_welcome_golden_banner.svg`:

### Цвета:
- `#FFD700` - основной золотой
- `#FFC107` - средний золотой  
- `#FF8F00` - темный золотой
- `#FFEB3B` - светлый золотой

### Валюты:
Можете заменить на другие актуальные криптовалюты в SVG файле.

---

**🌟 Наслаждайтесь новым премиальным золотистым дизайном!**
