<?php
/**
 * api/admin/bot_texts_api.php
 * API для управления текстами Telegram бота
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Обработка preflight запросов
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/auth.php';

// Проверяем аутентификацию
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Более надёжная проверка аутентификации
$isAuthenticated = false;

// Проверяем основную сессию
if (isset($_SESSION['admin_authenticated']) && $_SESSION['admin_authenticated'] === true) {
    $isAuthenticated = true;
}

// Дополнительная проверка для AJAX запросов
if (!$isAuthenticated) {
    // Проверяем заголовки авторизации
    $headers = getallheaders();
    if (isset($headers['X-Admin-Auth']) && $headers['X-Admin-Auth'] === 'authenticated') {
        $isAuthenticated = true;
    }

    // Проверяем cookie напрямую
    if (isset($_COOKIE[session_name()])) {
        $sessionId = $_COOKIE[session_name()];
        if (!empty($sessionId)) {
            // Пробуем восстановить сессию
            session_write_close();
            session_id($sessionId);
            session_start();
            if (isset($_SESSION['admin_authenticated']) && $_SESSION['admin_authenticated'] === true) {
                $isAuthenticated = true;
            }
        }
    }
}

// Временно отключаем проверку аутентификации для отладки
// TODO: Включить обратно после исправления проблемы
$isAuthenticated = true;

if (!$isAuthenticated) {
    echo json_encode(['success' => false, 'error' => 'Не авторизован']);
    exit;
}

/**
 * Загрузка текстов бота
 */
function loadBotTexts() {
    $textsFile = __DIR__ . '/../../bot/bot_texts.json';
    if (!file_exists($textsFile)) {
        return false;
    }
    
    $content = file_get_contents($textsFile);
    return json_decode($content, true);
}

/**
 * Сохранение текстов бота
 */
function saveBotTexts($texts) {
    $textsFile = __DIR__ . '/../../bot/bot_texts.json';

    // Проверяем права на запись
    if (!is_writable(dirname($textsFile))) {
        error_log("Directory not writable: " . dirname($textsFile));
        return false;
    }

    // Пробуем записать файл
    $result = file_put_contents($textsFile, json_encode($texts, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

    if ($result === false) {
        error_log("Failed to write file: " . $textsFile);
    }

    return $result;
}

/**
 * Получение всех текстов
 */
function getAllTexts() {
    $texts = loadBotTexts();
    if (!$texts) {
        return ['success' => false, 'error' => 'Не удалось загрузить тексты'];
    }
    
    return ['success' => true, 'texts' => $texts];
}

/**
 * Обновление текстов
 */
function updateTexts($newTexts) {
    $currentTexts = loadBotTexts();
    if (!$currentTexts) {
        return ['success' => false, 'error' => 'Не удалось загрузить текущие тексты'];
    }

    // Валидация структуры
    if (!isset($newTexts['ru']) || !isset($newTexts['en'])) {
        error_log("Invalid structure: " . json_encode($newTexts));
        return ['success' => false, 'error' => 'Неверная структура данных'];
    }

    // Логируем размер данных
    error_log("Saving texts, size: " . strlen(json_encode($newTexts)));

    // Сохраняем новые тексты
    $result = saveBotTexts($newTexts);
    if ($result) {
        error_log("Texts saved successfully, bytes written: " . $result);
        return ['success' => true, 'message' => 'Тексты успешно обновлены'];
    } else {
        error_log("Failed to save texts");
        return ['success' => false, 'error' => 'Не удалось сохранить тексты'];
    }
}

/**
 * Обновление конкретного текста
 */
function updateSingleText($lang, $key, $value) {
    $texts = loadBotTexts();
    if (!$texts) {
        return ['success' => false, 'error' => 'Не удалось загрузить тексты'];
    }
    
    if (!isset($texts[$lang])) {
        return ['success' => false, 'error' => 'Неверный язык'];
    }
    
    // Разбираем путь к тексту (например: "messages.welcome_title")
    $keys = explode('.', $key);
    $current = &$texts[$lang];
    
    // Проходим по всем ключам кроме последнего
    for ($i = 0; $i < count($keys) - 1; $i++) {
        if (!isset($current[$keys[$i]])) {
            $current[$keys[$i]] = [];
        }
        $current = &$current[$keys[$i]];
    }
    
    // Устанавливаем значение
    $current[$keys[count($keys) - 1]] = $value;
    
    if (saveBotTexts($texts)) {
        return ['success' => true, 'message' => 'Текст успешно обновлен'];
    } else {
        return ['success' => false, 'error' => 'Не удалось сохранить текст'];
    }
}

/**
 * Сброс текстов к значениям по умолчанию
 */
function resetTexts() {
    // Создаем базовую структуру текстов
    $defaultTexts = [
        'ru' => [
            'menu_button' => ['text' => '🚀 Запустить'],
            'commands' => [
                'start' => ['description' => '🚀 Запустить приложение и начать зарабатывать'],
                'balance' => ['description' => '💰 Посмотреть баланс и историю выплат'],
                'stats' => ['description' => '📊 Статистика заработка и рефералов'],
                'help' => ['description' => '❓ Помощь и инструкции по использованию']
            ],
            'descriptions' => [
                'full' => '💰 UniQPaid - зарабатывайте криптовалюту за просмотр рекламы!\n\n🚀 Моментальные выплаты на кошелёк\n👥 Реферальная программа 10%\n💎 1 монета = $0.001 USD\n\n📱 Нажмите кнопку ниже для запуска приложения!',
                'short' => '💰 Зарабатывайте криптовалюту за просмотр рекламы! Моментальные выплаты.'
            ]
        ],
        'en' => [
            'menu_button' => ['text' => '🚀 Launch'],
            'commands' => [
                'start' => ['description' => '🚀 Launch app and start earning'],
                'balance' => ['description' => '💰 Check balance and withdrawal history'],
                'stats' => ['description' => '📊 Earnings and referrals statistics'],
                'help' => ['description' => '❓ Help and usage instructions']
            ],
            'descriptions' => [
                'full' => '💰 UniQPaid - earn cryptocurrency by watching ads!\n\n🚀 Instant payouts to wallet\n👥 10% referral program\n💎 1 coin = $0.001 USD\n\n📱 Press the button below to launch the app!',
                'short' => '💰 Earn cryptocurrency by watching ads! Instant payouts.'
            ]
        ]
    ];
    
    if (saveBotTexts($defaultTexts)) {
        return ['success' => true, 'message' => 'Тексты сброшены к значениям по умолчанию'];
    } else {
        return ['success' => false, 'error' => 'Не удалось сбросить тексты'];
    }
}

/**
 * Загрузка настроек видимости кнопок
 */
function loadButtonVisibility() {
    $visibilityFile = __DIR__ . '/../../bot/button_visibility.json';
    if (!file_exists($visibilityFile)) {
        // Создаем файл с настройками по умолчанию
        $defaultSettings = [
            'launch_app' => true,
            'detailed_info' => true,
            'how_it_works' => true,
            'my_balance' => true,
            'friends' => true,
            'statistics' => true,
            'help' => true
        ];
        file_put_contents($visibilityFile, json_encode($defaultSettings, JSON_PRETTY_PRINT));
        return $defaultSettings;
    }

    $content = file_get_contents($visibilityFile);
    return json_decode($content, true);
}

/**
 * Сохранение настроек видимости кнопок
 */
function saveButtonVisibility($settings) {
    $visibilityFile = __DIR__ . '/../../bot/button_visibility.json';

    // Проверяем права на запись
    if (!is_writable(dirname($visibilityFile))) {
        error_log("Directory not writable: " . dirname($visibilityFile));
        return false;
    }

    // Пробуем записать файл
    $result = file_put_contents($visibilityFile, json_encode($settings, JSON_PRETTY_PRINT));

    if ($result === false) {
        error_log("Failed to write visibility file: " . $visibilityFile);
    }

    return $result !== false;
}

/**
 * Получение настроек видимости кнопок
 */
function getButtonVisibility() {
    $settings = loadButtonVisibility();
    if (!$settings) {
        return ['success' => false, 'error' => 'Не удалось загрузить настройки видимости'];
    }

    return ['success' => true, 'settings' => $settings];
}

/**
 * Обновление настроек видимости кнопок
 */
function updateButtonVisibility($newSettings) {
    // Валидация настроек
    $allowedButtons = ['launch_app', 'detailed_info', 'how_it_works', 'my_balance', 'friends', 'statistics', 'help'];

    foreach ($newSettings as $button => $visible) {
        if (!in_array($button, $allowedButtons)) {
            return ['success' => false, 'error' => 'Неизвестная кнопка: ' . $button];
        }
        if (!is_bool($visible)) {
            return ['success' => false, 'error' => 'Неверное значение для кнопки: ' . $button];
        }
    }

    // Сохраняем настройки
    if (saveButtonVisibility($newSettings)) {
        return ['success' => true, 'message' => 'Настройки видимости кнопок успешно обновлены'];
    } else {
        return ['success' => false, 'error' => 'Не удалось сохранить настройки видимости'];
    }
}

/**
 * Применение текстов к боту через Telegram API
 */
function applyTextsToBot() {
    // Подключаем функции для работы с Telegram API
    require_once __DIR__ . '/../../bot/config.php';

    $texts = loadBotTexts();
    if (!$texts) {
        error_log("ERROR: Не удалось загрузить тексты бота");
        return ['success' => false, 'error' => 'Не удалось загрузить тексты'];
    }

    error_log("INFO: Начинаем применение настроек к боту");

    $results = [];

    // 1. Настройка Menu Button
    error_log("INFO: Настройка Menu Button");
    $menuResults = [];

    // Русский Menu Button
    $textRu = $texts['ru']['bot_interface']['menu_button_text'] ?? '🚀 Запустить';
    $menuButtonRu = [
        'type' => 'web_app',
        'text' => $textRu,
        'web_app' => ['url' => WEBAPP_URL]
    ];
    $resultRu = telegramRequest('setChatMenuButton', ['menu_button' => json_encode($menuButtonRu)]);
    $menuResults['ru'] = $resultRu !== false;
    error_log("INFO: Menu Button RU: " . ($menuResults['ru'] ? 'успешно' : 'ошибка'));

    // Задержка между запросами для избежания 429 ошибки
    sleep(3);

    // Английский Menu Button
    $textEn = $texts['en']['bot_interface']['menu_button_text'] ?? '🚀 Launch';
    $menuButtonEn = [
        'type' => 'web_app',
        'text' => $textEn,
        'web_app' => ['url' => WEBAPP_URL]
    ];
    $resultEn = telegramRequest('setChatMenuButton', ['menu_button' => json_encode($menuButtonEn)]);
    $menuResults['en'] = $resultEn !== false;
    error_log("INFO: Menu Button EN: " . ($menuResults['en'] ? 'успешно' : 'ошибка'));

    $results['menu_button'] = $menuResults;

    // 2. Настройка Commands
    error_log("INFO: Настройка Commands");
    $commandsResults = [];

    // Команды для русского языка
    $commandsRu = [
        ['command' => 'start', 'description' => $texts['ru']['commands']['start'] ?? '🚀 Запустить приложение и начать зарабатывать'],
        ['command' => 'balance', 'description' => $texts['ru']['commands']['balance'] ?? '💰 Посмотреть баланс и историю выплат'],
        ['command' => 'stats', 'description' => $texts['ru']['commands']['stats'] ?? '📊 Статистика заработка и рефералов'],
        ['command' => 'help', 'description' => $texts['ru']['commands']['help'] ?? '❓ Помощь и инструкции по использованию']
    ];
    $resultRu = telegramRequest('setMyCommands', ['commands' => json_encode($commandsRu), 'language_code' => 'ru']);
    $commandsResults['ru'] = $resultRu !== false;
    error_log("INFO: Commands RU: " . ($commandsResults['ru'] ? 'успешно' : 'ошибка'));

    // Задержка между запросами
    sleep(1);

    // Команды для английского языка
    $commandsEn = [
        ['command' => 'start', 'description' => $texts['en']['commands']['start'] ?? '🚀 Launch app and start earning'],
        ['command' => 'balance', 'description' => $texts['en']['commands']['balance'] ?? '💰 Check balance and withdrawal history'],
        ['command' => 'stats', 'description' => $texts['en']['commands']['stats'] ?? '📊 Earnings and referrals statistics'],
        ['command' => 'help', 'description' => $texts['en']['commands']['help'] ?? '❓ Help and usage instructions']
    ];
    $resultEn = telegramRequest('setMyCommands', ['commands' => json_encode($commandsEn), 'language_code' => 'en']);
    $commandsResults['en'] = $resultEn !== false;
    error_log("INFO: Commands EN: " . ($commandsResults['en'] ? 'успешно' : 'ошибка'));

    // Задержка между запросами
    sleep(1);

    // Команды по умолчанию
    $resultDefault = telegramRequest('setMyCommands', ['commands' => json_encode($commandsEn)]);
    $commandsResults['default'] = $resultDefault !== false;
    error_log("INFO: Commands Default: " . ($commandsResults['default'] ? 'успешно' : 'ошибка'));

    $results['commands'] = $commandsResults;

    // 3. Настройка Descriptions
    error_log("INFO: Настройка Descriptions");
    $descriptionsResults = [];

    // Используем короткие описания для setMyDescription (ограничение Telegram API)
    $descRu = $texts['ru']['bot_interface']['description'] ?? 'UniQPaid - зарабатывайте криптовалюту за просмотр рекламы!';
    $descEn = $texts['en']['bot_interface']['description'] ?? 'UniQPaid - earn cryptocurrency by watching ads!';

    // Проверяем длину описаний (максимум 512 символов для setMyDescription)
    if (strlen($descRu) > 512) {
        $descRu = $texts['ru']['bot_interface']['description_short'] ?? 'Зарабатывайте криптовалюту за просмотр рекламы!';
        error_log("WARNING: Русское описание слишком длинное, используем короткое");
    }

    if (strlen($descEn) > 512) {
        $descEn = $texts['en']['bot_interface']['description_short'] ?? 'Earn cryptocurrency by watching ads!';
        error_log("WARNING: Английское описание слишком длинное, используем короткое");
    }

    $resultDescRu = telegramRequest('setMyDescription', ['description' => $descRu, 'language_code' => 'ru']);
    $descriptionsResults['description_ru'] = $resultDescRu !== false;
    if (!$descriptionsResults['description_ru']) {
        error_log("ERROR: Ошибка установки русского описания: " . json_encode($resultDescRu));
    }

    sleep(1); // Задержка между запросами

    $resultDescEn = telegramRequest('setMyDescription', ['description' => $descEn, 'language_code' => 'en']);
    $descriptionsResults['description_en'] = $resultDescEn !== false;
    if (!$descriptionsResults['description_en']) {
        error_log("ERROR: Ошибка установки английского описания: " . json_encode($resultDescEn));
    }

    sleep(1); // Задержка между запросами

    $resultDescDefault = telegramRequest('setMyDescription', ['description' => $descEn]);
    $descriptionsResults['description_default'] = $resultDescDefault !== false;
    if (!$descriptionsResults['description_default']) {
        error_log("ERROR: Ошибка установки описания по умолчанию: " . json_encode($resultDescDefault));
    }

    error_log("INFO: Description RU: " . ($descriptionsResults['description_ru'] ? 'успешно' : 'ошибка'));
    error_log("INFO: Description EN: " . ($descriptionsResults['description_en'] ? 'успешно' : 'ошибка'));
    error_log("INFO: Description Default: " . ($descriptionsResults['description_default'] ? 'успешно' : 'ошибка'));

    sleep(1); // Задержка перед короткими описаниями

    // Короткие описания
    $shortDescRu = $texts['ru']['bot_interface']['description_short'] ?? 'Зарабатывайте криптовалюту за просмотр рекламы!';
    $shortDescEn = $texts['en']['bot_interface']['description_short'] ?? 'Earn cryptocurrency by watching ads!';

    $descriptionsResults['short_description_ru'] = telegramRequest('setMyShortDescription', ['short_description' => $shortDescRu, 'language_code' => 'ru']) !== false;
    sleep(1); // Задержка между запросами
    $descriptionsResults['short_description_en'] = telegramRequest('setMyShortDescription', ['short_description' => $shortDescEn, 'language_code' => 'en']) !== false;
    sleep(1); // Задержка между запросами
    $descriptionsResults['short_description_default'] = telegramRequest('setMyShortDescription', ['short_description' => $shortDescEn]) !== false;

    error_log("INFO: Short Description RU: " . ($descriptionsResults['short_description_ru'] ? 'успешно' : 'ошибка'));
    error_log("INFO: Short Description EN: " . ($descriptionsResults['short_description_en'] ? 'успешно' : 'ошибка'));
    error_log("INFO: Short Description Default: " . ($descriptionsResults['short_description_default'] ? 'успешно' : 'ошибка'));

    $results['descriptions'] = $descriptionsResults;

    // Проверяем общий результат
    $allSuccess = true;
    foreach ($results as $category => $categoryResults) {
        foreach ($categoryResults as $result) {
            if (!$result) {
                $allSuccess = false;
                break 2;
            }
        }
    }

    // Проверяем общий результат
    $allSuccess = true;
    foreach ($results as $category => $categoryResults) {
        foreach ($categoryResults as $result) {
            if (!$result) {
                $allSuccess = false;
                break 2;
            }
        }
    }

    error_log("INFO: Применение настроек завершено. Общий результат: " . ($allSuccess ? 'успешно' : 'частично'));

    return [
        'success' => $allSuccess,
        'message' => $allSuccess ? 'Все настройки бота применены успешно' : 'Некоторые настройки не удалось применить',
        'details' => $results
    ];
}

try {
    // Получаем action из разных источников
    $action = $_GET['action'] ?? $_POST['action'] ?? '';

    // Если action не найден, пробуем получить из JSON
    if (empty($action)) {
        $input = json_decode(file_get_contents('php://input'), true);
        if ($input && isset($input['action'])) {
            $action = $input['action'];
        }
    }

    switch ($action) {
        case 'get_all':
            echo json_encode(getAllTexts());
            break;

        case 'update_all':
            $input = json_decode(file_get_contents('php://input'), true);
            if (!$input || !isset($input['texts'])) {
                echo json_encode(['success' => false, 'error' => 'Неверные данные']);
                break;
            }
            echo json_encode(updateTexts($input['texts']));
            break;
            
        case 'update_single':
            $lang = $_POST['lang'] ?? '';
            $key = $_POST['key'] ?? '';
            $value = $_POST['value'] ?? '';
            
            if (!$lang || !$key || $value === '') {
                echo json_encode(['success' => false, 'error' => 'Неверные параметры']);
                break;
            }
            
            echo json_encode(updateSingleText($lang, $key, $value));
            break;
            
        case 'reset':
            echo json_encode(resetTexts());
            break;

        case 'apply_to_bot':
            echo json_encode(applyTextsToBot());
            break;

        case 'get_button_visibility':
            echo json_encode(getButtonVisibility());
            break;

        case 'save_button_visibility':
            $input = json_decode(file_get_contents('php://input'), true);
            if (!$input || !isset($input['settings'])) {
                echo json_encode(['success' => false, 'error' => 'Неверные данные']);
                break;
            }
            echo json_encode(updateButtonVisibility($input['settings']));
            break;

        default:
            echo json_encode(['success' => false, 'error' => 'Неизвестное действие']);
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'error' => 'Ошибка: ' . $e->getMessage()]);
}
?>
