<?php

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/currency_cache.php';

function logMessage($message) {
    $timestamp = date('Y-m-d H:i:s');
    error_log("[{$timestamp}] Currency Cache Update: {$message}");
    echo "[{$timestamp}] {$message}\n";
}

logMessage("Начинаем обновление кеша валют...");

// Список поддерживаемых валют
$supportedCurrencies = [
    'ton' => 'TON',
    'eth' => 'ETH', 
    'btc' => 'BTC',
    'usdttrc20' => 'USDTTRC20',
    'ltc' => 'LTC',
    'bch' => 'BCH',
    'xrp' => 'XRP',
    'ada' => 'ADA',
    'dot' => 'DOT'
];

// Загружаем существующий кеш
$cache = loadCurrencyCache();
$updatedCount = 0;
$errorCount = 0;

foreach ($supportedCurrencies as $currency => $ticker) {
    logMessage("Обновляем данные для {$ticker}...");
    
    try {
        // 1. Получаем курс валюты (estimate от 1 USD)
        $estimateUrl = NOWPAYMENTS_API_URL . "/estimate?amount=1&currency_from=usd&currency_to={$currency}";
        $estimateContext = stream_context_create([
            'http' => [
                'method' => 'GET',
                'header' => [
                    'x-api-key: ' . NOWPAYMENTS_API_KEY,
                    'Content-Type: application/json'
                ],
                'timeout' => 30
            ]
        ]);
        
        $estimateResponse = @file_get_contents($estimateUrl, false, $estimateContext);
        if ($estimateResponse === false) {
            throw new Exception("Не удалось получить курс для {$currency}");
        }
        
        $estimateData = json_decode($estimateResponse, true);
        if (!$estimateData || !isset($estimateData['estimated_amount'])) {
            throw new Exception("Некорректный ответ API для курса {$currency}");
        }
        
        $cryptoPerUsd = $estimateData['estimated_amount'];
        $usdPerCrypto = 1 / $cryptoPerUsd; // Курс в USD за 1 единицу криптовалюты
        
        logMessage("Курс {$ticker}: 1 USD = {$cryptoPerUsd} {$ticker} (1 {$ticker} = \${$usdPerCrypto})");
        
        // 2. Получаем минимум для выводов
        $minWithdrawalUrl = NOWPAYMENTS_API_URL . "/payout-withdrawal/min-amount/{$currency}";
        $minWithdrawalContext = stream_context_create([
            'http' => [
                'method' => 'GET',
                'header' => [
                    'x-api-key: ' . NOWPAYMENTS_API_KEY,
                    'Content-Type: application/json'
                ],
                'timeout' => 30
            ]
        ]);
        
        $minWithdrawalResponse = @file_get_contents($minWithdrawalUrl, false, $minWithdrawalContext);
        $minWithdrawal = null;
        
        if ($minWithdrawalResponse !== false) {
            $minWithdrawalData = json_decode($minWithdrawalResponse, true);
            if ($minWithdrawalData && isset($minWithdrawalData['min_amount'])) {
                $minWithdrawal = $minWithdrawalData['min_amount'];
                logMessage("Минимум вывода {$ticker}: {$minWithdrawal}");
            }
        }
        
        // 3. Получаем минимум для платежей (fallback если нет минимума вывода)
        if ($minWithdrawal === null) {
            $minPaymentUrl = NOWPAYMENTS_API_URL . "/min-amount?currency_from=usd&currency_to={$currency}";
            $minPaymentContext = stream_context_create([
                'http' => [
                    'method' => 'GET',
                    'header' => [
                        'x-api-key: ' . NOWPAYMENTS_API_KEY,
                        'Content-Type: application/json'
                    ],
                    'timeout' => 30
                ]
            ]);
            
            $minPaymentResponse = @file_get_contents($minPaymentUrl, false, $minPaymentContext);
            if ($minPaymentResponse !== false) {
                $minPaymentData = json_decode($minPaymentResponse, true);
                if ($minPaymentData && isset($minPaymentData['min_amount'])) {
                    $minPaymentUsd = $minPaymentData['min_amount'];
                    $minWithdrawal = $minPaymentUsd * $cryptoPerUsd; // Конвертируем в криптовалюту
                    logMessage("Минимум платежа {$ticker}: {$minPaymentUsd} USD = {$minWithdrawal} {$ticker}");
                }
            }
        }
        
        // 4. Устанавливаем fallback минимумы если API не вернул данные
        if ($minWithdrawal === null) {
            $fallbackMinimums = [
                'ton' => 0.5,
                'eth' => 0.0001,
                'btc' => 0.000005,
                'usdttrc20' => 8.58,
                'ltc' => 0.001,
                'bch' => 0.001,
                'xrp' => 1.0,
                'ada' => 1.0,
                'dot' => 0.1
            ];
            
            $minWithdrawal = $fallbackMinimums[$currency] ?? 0.001;
            logMessage("Используем fallback минимум для {$ticker}: {$minWithdrawal}");
        }
        
        // 5. Рассчитываем сетевую комиссию (примерно 1-2% от минимума в USD)
        $minWithdrawalUsd = $minWithdrawal * $usdPerCrypto;
        $networkFeeUsd = max(0.1, $minWithdrawalUsd * 0.02); // Минимум $0.1 или 2% от суммы
        
        // Специальные комиссии для известных сетей
        $networkFees = [
            'ton' => 0.15,
            'eth' => 0.25,
            'btc' => 0.50,
            'usdttrc20' => 5.58, // TRC20 дорогая
            'ltc' => 0.30,
            'bch' => 0.35,
            'xrp' => 0.10,
            'ada' => 0.15,
            'dot' => 0.20
        ];
        
        $networkFeeUsd = $networkFees[$currency] ?? $networkFeeUsd;
        
        // 6. Определяем статус валюты
        $status = 'available';
        if ($networkFeeUsd > 3.0) {
            $status = 'expensive';
        } elseif ($usdPerCrypto > 1000) {
            $status = 'best'; // Дорогие валюты обычно более стабильные
        }
        
        // 7. Обновляем кеш
        $success = updateCurrencyInCache(
            $currency,
            $usdPerCrypto,
            $minWithdrawal,
            $networkFeeUsd,
            $status
        );
        
        if ($success) {
            $updatedCount++;
            logMessage("✅ {$ticker} обновлен: курс \${$usdPerCrypto}, минимум {$minWithdrawal}, комиссия \${$networkFeeUsd}");
        } else {
            throw new Exception("Не удалось сохранить данные в кеш");
        }
        
        // Небольшая пауза между запросами
        usleep(500000); // 0.5 секунды
        
    } catch (Exception $e) {
        $errorCount++;
        logMessage("❌ Ошибка обновления {$ticker}: " . $e->getMessage());
        
        // Сохраняем старые данные если они есть
        if (isset($cache['currencies'][$currency])) {
            logMessage("Сохраняем старые данные для {$ticker}");
        }
    }
}

// Обновляем метку времени кеша
$cache = loadCurrencyCache();
$cache['last_updated'] = time();
saveCurrencyCache($cache);

logMessage("Обновление завершено. Успешно: {$updatedCount}, ошибок: {$errorCount}");

if ($updatedCount > 0) {
    logMessage("🎉 Кеш валют успешно обновлен!");
} else {
    logMessage("⚠️ Не удалось обновить ни одной валюты");
}

?>
