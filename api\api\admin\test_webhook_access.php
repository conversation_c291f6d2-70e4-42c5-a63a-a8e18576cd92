<?php
/**
 * Тест доступности webhook
 */

// Логируем все запросы
$logFile = __DIR__ . '/webhook_test.log';
$timestamp = date('Y-m-d H:i:s');

// Получаем метод запроса
$method = $_SERVER['REQUEST_METHOD'];

// Получаем данные
$input = file_get_contents('php://input');
$headers = getallheaders();

// Логируем
$logEntry = "[{$timestamp}] METHOD: {$method}\n";
$logEntry .= "[{$timestamp}] INPUT: {$input}\n";
$logEntry .= "[{$timestamp}] HEADERS: " . json_encode($headers) . "\n";
$logEntry .= "[{$timestamp}] GET: " . json_encode($_GET) . "\n";
$logEntry .= "[{$timestamp}] POST: " . json_encode($_POST) . "\n";
$logEntry .= "---\n";

file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);

// Отвечаем
http_response_code(200);
echo "OK - " . $timestamp;
?>
