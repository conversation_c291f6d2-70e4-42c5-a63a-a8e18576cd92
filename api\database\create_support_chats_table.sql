CREATE TABLE IF NOT EXISTS support_chats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT 'Telegram User ID',
    username VA<PERSON><PERSON><PERSON>(255) NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(255) NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(255) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS support_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    chat_id INT NOT NULL,
    message_id BIGINT NOT NULL COMMENT 'Telegram Message ID',
    from_user BOOLEAN NOT NULL DEFAULT 0 COMMENT '0=user, 1=support',
    text TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREI<PERSON><PERSON> KEY (chat_id) REFERENCES support_chats(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE INDEX idx_user_id ON support_chats(user_id);
CREATE INDEX idx_chat_id ON support_messages(chat_id);