<?php
/**
 * Система ограничения запросов к NOWPayments API
 * Решает проблему 429 Too Many Requests
 */

class RateLimiter {
    private $cacheFile;
    private $requestLog;
    private $maxRequestsPerMinute = 30; // NOWPayments лимит
    private $maxRequestsPerHour = 1000;
    private $cacheTimeout = 300; // 5 минут кэш
    
    public function __construct() {
        $this->cacheFile = __DIR__ . '/api_cache.json';
        $this->requestLog = __DIR__ . '/request_log.json';
        $this->loadCache();
        $this->loadRequestLog();
    }
    
    /**
     * Проверить, можно ли сделать запрос
     */
    public function canMakeRequest($endpoint = '') {
        $now = time();
        $minuteAgo = $now - 60;
        $hourAgo = $now - 3600;
        
        // Очищаем старые записи
        $this->requests = array_filter($this->requests, function($timestamp) use ($hourAgo) {
            return $timestamp > $hourAgo;
        });
        
        // Считаем запросы за последнюю минуту
        $requestsLastMinute = count(array_filter($this->requests, function($timestamp) use ($minuteAgo) {
            return $timestamp > $minuteAgo;
        }));
        
        // Считаем запросы за последний час
        $requestsLastHour = count($this->requests);
        
        if ($requestsLastMinute >= $this->maxRequestsPerMinute) {
            error_log("RateLimiter WARNING: Превышен лимит запросов в минуту: $requestsLastMinute/$this->maxRequestsPerMinute");
            return false;
        }
        
        if ($requestsLastHour >= $this->maxRequestsPerHour) {
            error_log("RateLimiter WARNING: Превышен лимит запросов в час: $requestsLastHour/$this->maxRequestsPerHour");
            return false;
        }
        
        return true;
    }
    
    /**
     * Зарегистрировать выполненный запрос
     */
    public function registerRequest($endpoint = '') {
        $this->requests[] = time();
        $this->saveRequestLog();
        
        // Добавляем задержку между запросами
        usleep(200000); // 200ms задержка
    }
    
    /**
     * Получить данные из кэша
     */
    public function getFromCache($key) {
        if (!isset($this->cache[$key])) {
            return null;
        }
        
        $item = $this->cache[$key];
        if (time() > $item['expires']) {
            unset($this->cache[$key]);
            $this->saveCache();
            return null;
        }
        
        return $item['data'];
    }
    
    /**
     * Сохранить данные в кэш
     */
    public function saveToCache($key, $data, $ttl = null) {
        if ($ttl === null) {
            $ttl = $this->cacheTimeout;
        }
        
        $this->cache[$key] = [
            'data' => $data,
            'expires' => time() + $ttl,
            'created' => time()
        ];
        
        $this->saveCache();
    }
    
    /**
     * Получить время до следующего доступного запроса
     */
    public function getWaitTime() {
        if ($this->canMakeRequest()) {
            return 0;
        }
        
        $now = time();
        $minuteAgo = $now - 60;
        
        // Находим самый старый запрос за последнюю минуту
        $recentRequests = array_filter($this->requests, function($timestamp) use ($minuteAgo) {
            return $timestamp > $minuteAgo;
        });
        
        if (empty($recentRequests)) {
            return 0;
        }
        
        $oldestRequest = min($recentRequests);
        return max(0, 60 - ($now - $oldestRequest));
    }
    
    /**
     * Загрузить кэш
     */
    private function loadCache() {
        if (file_exists($this->cacheFile)) {
            $data = json_decode(file_get_contents($this->cacheFile), true);
            $this->cache = $data ?: [];
        } else {
            $this->cache = [];
        }
    }
    
    /**
     * Сохранить кэш
     */
    private function saveCache() {
        file_put_contents($this->cacheFile, json_encode($this->cache, JSON_PRETTY_PRINT));
    }
    
    /**
     * Загрузить лог запросов
     */
    private function loadRequestLog() {
        if (file_exists($this->requestLog)) {
            $data = json_decode(file_get_contents($this->requestLog), true);
            $this->requests = $data ?: [];
        } else {
            $this->requests = [];
        }
    }
    
    /**
     * Сохранить лог запросов
     */
    private function saveRequestLog() {
        // Оставляем только последние 1000 запросов
        if (count($this->requests) > 1000) {
            $this->requests = array_slice($this->requests, -1000);
        }
        
        file_put_contents($this->requestLog, json_encode($this->requests));
    }
    
    /**
     * Получить статистику запросов
     */
    public function getStats() {
        $now = time();
        $minuteAgo = $now - 60;
        $hourAgo = $now - 3600;
        
        $requestsLastMinute = count(array_filter($this->requests, function($timestamp) use ($minuteAgo) {
            return $timestamp > $minuteAgo;
        }));
        
        $requestsLastHour = count(array_filter($this->requests, function($timestamp) use ($hourAgo) {
            return $timestamp > $hourAgo;
        }));
        
        return [
            'requests_last_minute' => $requestsLastMinute,
            'requests_last_hour' => $requestsLastHour,
            'max_per_minute' => $this->maxRequestsPerMinute,
            'max_per_hour' => $this->maxRequestsPerHour,
            'can_make_request' => $this->canMakeRequest(),
            'wait_time' => $this->getWaitTime(),
            'cache_items' => count($this->cache)
        ];
    }
    
    /**
     * Очистить кэш
     */
    public function clearCache() {
        $this->cache = [];
        $this->saveCache();
    }
}
?>
