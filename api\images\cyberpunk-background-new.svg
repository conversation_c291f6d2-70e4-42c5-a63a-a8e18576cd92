<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Градиенты для киберпанк стиля -->
    <linearGradient id="mainGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0a0a0a;stop-opacity:1" />
      <stop offset="25%" style="stop-color:#1a1a1a;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#2a2a2a;stop-opacity:1" />
      <stop offset="75%" style="stop-color:#1a1a1a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0a0a0a;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="orangeGlow" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ff6b35;stop-opacity:0" />
      <stop offset="50%" style="stop-color:#ff6b35;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#ff6b35;stop-opacity:0" />
    </linearGradient>
    
    <radialGradient id="centerGlow" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#ff6b35;stop-opacity:0.1" />
      <stop offset="70%" style="stop-color:#ff6b35;stop-opacity:0.05" />
      <stop offset="100%" style="stop-color:#ff6b35;stop-opacity:0" />
    </radialGradient>
    
    <!-- Паттерн для сетки -->
    <pattern id="grid" width="100" height="100" patternUnits="userSpaceOnUse">
      <path d="M 100 0 L 0 0 0 100" fill="none" stroke="#ff6b35" stroke-width="0.5" opacity="0.1"/>
    </pattern>
    
    <!-- Фильтры для свечения -->
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <filter id="strongGlow">
      <feGaussianBlur stdDeviation="5" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Основной фон -->
  <rect width="1920" height="1080" fill="url(#mainGradient)"/>
  
  <!-- Центральное свечение -->
  <rect width="1920" height="1080" fill="url(#centerGlow)"/>
  
  <!-- Сетка -->
  <rect width="1920" height="1080" fill="url(#grid)"/>
  
  <!-- Главные диагональные линии -->
  <g stroke="#ff6b35" stroke-width="2" fill="none" opacity="0.3" filter="url(#glow)">
    <line x1="0" y1="0" x2="1920" y2="1080"/>
    <line x1="1920" y1="0" x2="0" y2="1080"/>
    <line x1="960" y1="0" x2="960" y2="1080"/>
    <line x1="0" y1="540" x2="1920" y2="540"/>
  </g>
  
  <!-- Дополнительные геометрические линии -->
  <g stroke="#ff6b35" stroke-width="1" fill="none" opacity="0.2">
    <line x1="480" y1="0" x2="480" y2="1080"/>
    <line x1="1440" y1="0" x2="1440" y2="1080"/>
    <line x1="0" y1="270" x2="1920" y2="270"/>
    <line x1="0" y1="810" x2="1920" y2="810"/>
  </g>
  
  <!-- Киберпанк геометрические формы -->
  <g fill="none" stroke="#ff6b35" stroke-width="1.5" opacity="0.15">
    <!-- Левый верхний угол -->
    <polygon points="50,50 200,50 200,150 150,200 50,200"/>
    <polygon points="100,100 180,100 180,180 100,180"/>
    
    <!-- Правый верхний угол -->
    <polygon points="1720,50 1870,50 1870,200 1770,200 1720,150"/>
    <polygon points="1740,100 1820,100 1820,180 1740,180"/>
    
    <!-- Левый нижний угол -->
    <polygon points="50,880 200,880 200,1030 50,1030 50,930"/>
    <polygon points="100,900 180,900 180,980 100,980"/>
    
    <!-- Правый нижний угол -->
    <polygon points="1720,880 1870,880 1870,1030 1720,1030"/>
    <polygon points="1740,900 1820,900 1820,980 1740,980"/>
  </g>
  
  <!-- Центральные акцентные элементы -->
  <g fill="none" stroke="#ff6b35" stroke-width="2" opacity="0.25" filter="url(#glow)">
    <!-- Центральный ромб -->
    <polygon points="960,400 1100,540 960,680 820,540"/>
    <polygon points="960,450 1050,540 960,630 870,540"/>
    
    <!-- Боковые элементы -->
    <polygon points="300,540 400,440 500,540 400,640"/>
    <polygon points="1420,540 1520,440 1620,540 1520,640"/>
  </g>
  
  <!-- Тонкие декоративные линии -->
  <g stroke="#ff6b35" stroke-width="0.5" fill="none" opacity="0.1">
    <!-- Горизонтальные -->
    <line x1="0" y1="135" x2="1920" y2="135"/>
    <line x1="0" y1="405" x2="1920" y2="405"/>
    <line x1="0" y1="675" x2="1920" y2="675"/>
    <line x1="0" y1="945" x2="1920" y2="945"/>
    
    <!-- Вертикальные -->
    <line x1="240" y1="0" x2="240" y2="1080"/>
    <line x1="720" y1="0" x2="720" y2="1080"/>
    <line x1="1200" y1="0" x2="1200" y2="1080"/>
    <line x1="1680" y1="0" x2="1680" y2="1080"/>
  </g>
  
  <!-- Светящиеся акценты -->
  <g fill="#ff6b35" opacity="0.1">
    <circle cx="960" cy="540" r="3" filter="url(#strongGlow)"/>
    <circle cx="480" cy="270" r="2" filter="url(#glow)"/>
    <circle cx="1440" cy="270" r="2" filter="url(#glow)"/>
    <circle cx="480" cy="810" r="2" filter="url(#glow)"/>
    <circle cx="1440" cy="810" r="2" filter="url(#glow)"/>
  </g>
  
  <!-- Дополнительные киберпанк элементы -->
  <g stroke="#ff6b35" stroke-width="1" fill="none" opacity="0.08">
    <!-- Сложные геометрические формы -->
    <path d="M 100,300 L 200,300 L 250,350 L 200,400 L 100,400 L 50,350 Z"/>
    <path d="M 1670,300 L 1770,300 L 1820,350 L 1770,400 L 1670,400 L 1620,350 Z"/>
    <path d="M 100,680 L 200,680 L 250,730 L 200,780 L 100,780 L 50,730 Z"/>
    <path d="M 1670,680 L 1770,680 L 1820,730 L 1770,780 L 1670,780 L 1620,730 Z"/>
  </g>
  
  <!-- Финальные светящиеся линии -->
  <g stroke="url(#orangeGlow)" stroke-width="3" fill="none" opacity="0.4">
    <line x1="0" y1="540" x2="1920" y2="540"/>
    <line x1="960" y1="0" x2="960" y2="1080"/>
  </g>
</svg>
