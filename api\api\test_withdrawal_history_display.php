<?php
/**
 * Тестирование отображения истории выплат в интерфейсе
 */

echo "🖥️ ТЕСТИРОВАНИЕ ОТОБРАЖЕНИЯ ИСТОРИИ ВЫПЛАТ\n";
echo str_repeat("=", 60) . "\n\n";

// Тестовые данные выплат для проверки отображения
$testWithdrawals = [
    [
        'id' => 'test_001',
        'payout_id' => 'np_001',
        'status' => 'waiting',
        'coins_amount' => 1000,
        'usd_amount' => 1.0,
        'crypto_amount' => 0.0003,
        'currency' => 'eth',
        'address' => '******************************************',
        'created_at' => '2024-12-18 10:00:00',
        'updated_at' => '2024-12-18 10:00:00',
        'timestamp' => time() - 3600
    ],
    [
        'id' => 'test_002',
        'payout_id' => 'np_002',
        'status' => 'processing',
        'coins_amount' => 2500,
        'usd_amount' => 2.5,
        'crypto_amount' => 0.000025,
        'currency' => 'btc',
        'address' => '**********************************',
        'created_at' => '2024-12-18 09:30:00',
        'updated_at' => '2024-12-18 09:45:00',
        'timestamp' => time() - 1800
    ],
    [
        'id' => 'test_003',
        'payout_id' => 'np_003',
        'status' => 'sending',
        'coins_amount' => 5000,
        'usd_amount' => 5.0,
        'crypto_amount' => 1.5,
        'currency' => 'ton',
        'address' => 'EQD4FPq-PRDieyQKkizFTRtSDyucUIqrj0v_zXJmqaDp6_0t',
        'created_at' => '2024-12-18 09:00:00',
        'updated_at' => '2024-12-18 09:50:00',
        'timestamp' => time() - 600
    ],
    [
        'id' => 'test_004',
        'payout_id' => 'np_004',
        'status' => 'finished',
        'coins_amount' => 10000,
        'usd_amount' => 10.0,
        'crypto_amount' => 8.5,
        'currency' => 'usdttrc20',
        'address' => 'TLa2f6VPqDgRE67v1736s7bJ8Ray5wYjU7',
        'transaction_hash' => '******************************************',
        'created_at' => '2024-12-18 08:00:00',
        'updated_at' => '2024-12-18 08:30:00',
        'timestamp' => time() - 7200
    ],
    [
        'id' => 'test_005',
        'payout_id' => 'np_005',
        'status' => 'confirmed',
        'coins_amount' => 3000,
        'usd_amount' => 3.0,
        'crypto_amount' => 0.0009,
        'currency' => 'eth',
        'address' => '******************************************',
        'transaction_hash' => '******************************************',
        'block_height' => 18500000,
        'confirmations' => 12,
        'created_at' => '2024-12-17 15:00:00',
        'updated_at' => '2024-12-17 16:00:00',
        'timestamp' => time() - 86400
    ],
    [
        'id' => 'test_006',
        'payout_id' => 'np_006',
        'status' => 'failed',
        'coins_amount' => 500,
        'usd_amount' => 0.5,
        'crypto_amount' => 0.0001,
        'currency' => 'eth',
        'address' => '******************************************',
        'error_message' => 'Insufficient balance',
        'created_at' => '2024-12-18 07:00:00',
        'updated_at' => '2024-12-18 07:05:00',
        'timestamp' => time() - 10800
    ]
];

echo "🎯 ТЕСТ 1: ГЕНЕРАЦИЯ HTML ДЛЯ ИСТОРИИ ВЫПЛАТ\n";
echo str_repeat("-", 40) . "\n\n";

foreach ($testWithdrawals as $i => $withdrawal) {
    echo "📊 Выплата " . ($i + 1) . ": {$withdrawal['id']}\n";
    
    // Генерируем HTML для отображения
    $html = generateWithdrawalHTML($withdrawal);
    
    // Проверяем ключевые элементы HTML
    $checks = [
        'status_class' => checkStatusClass($html, $withdrawal['status']),
        'status_text' => checkStatusText($html, $withdrawal['status']),
        'amount_display' => checkAmountDisplay($html, $withdrawal),
        'date_display' => checkDateDisplay($html, $withdrawal),
        'address_display' => checkAddressDisplay($html, $withdrawal),
        'action_buttons' => checkActionButtons($html, $withdrawal),
        'additional_info' => checkAdditionalInfo($html, $withdrawal)
    ];
    
    $passedChecks = array_filter($checks);
    $totalChecks = count($checks);
    $passedCount = count($passedChecks);
    
    echo "   📊 Проверок пройдено: {$passedCount}/{$totalChecks}\n";
    
    foreach ($checks as $checkName => $result) {
        echo "   " . ($result ? "✅" : "❌") . " {$checkName}\n";
    }
    
    if ($passedCount === $totalChecks) {
        echo "   🎉 Все проверки пройдены!\n";
    } else {
        echo "   ⚠️ Некоторые проверки провалились\n";
    }
    
    echo "\n";
}

echo "🎯 ТЕСТ 2: ПРОВЕРКА CSS КЛАССОВ СТАТУСОВ\n";
echo str_repeat("-", 40) . "\n\n";

$statusClasses = [
    'waiting' => 'status-pending',
    'pending' => 'status-pending',
    'processing' => 'status-processing',
    'sending' => 'status-processing',
    'finished' => 'status-completed',
    'completed' => 'status-completed',
    'confirmed' => 'status-completed',
    'failed' => 'status-failed',
    'rejected' => 'status-failed',
    'cancelled' => 'status-failed',
    'expired' => 'status-expired'
];

echo "📝 Проверка соответствия статусов и CSS классов:\n";
foreach ($statusClasses as $status => $expectedClass) {
    $actualClass = getStatusCSSClass($status);
    $isCorrect = ($actualClass === $expectedClass);
    
    echo "   " . ($isCorrect ? "✅" : "❌") . " {$status} → {$actualClass}\n";
}

echo "\n🎯 ТЕСТ 3: ПРОВЕРКА ФУНКЦИЙ ФОРМАТИРОВАНИЯ\n";
echo str_repeat("-", 40) . "\n\n";

// Тест форматирования дат
echo "📅 Тестирование форматирования дат:\n";
$dateTests = [
    time() - 60 => '1 минуту назад',
    time() - 3600 => '1 час назад',
    time() - 86400 => '1 день назад'
];

foreach ($dateTests as $timestamp => $expected) {
    $formatted = formatRelativeTime($timestamp);
    echo "   📅 {$timestamp} → {$formatted}\n";
}

// Тест форматирования адресов
echo "\n📍 Тестирование сокращения адресов:\n";
$addressTests = [
    '******************************************' => '0x742d...C8C8',
    '**********************************' => '1A1zP...fNa',
    'TLa2f6VPqDgRE67v1736s7bJ8Ray5wYjU7' => 'TLa2f...YjU7'
];

foreach ($addressTests as $fullAddress => $expected) {
    $shortened = shortenAddress($fullAddress);
    $isCorrect = (strlen($shortened) <= 12);
    echo "   " . ($isCorrect ? "✅" : "❌") . " {$fullAddress} → {$shortened}\n";
}

// Тест форматирования сумм
echo "\n💰 Тестирование форматирования сумм:\n";
$amountTests = [
    ['amount' => 0.00000123, 'currency' => 'btc', 'expected_decimals' => 8],
    ['amount' => 0.001234, 'currency' => 'eth', 'expected_decimals' => 6],
    ['amount' => 1.234567, 'currency' => 'ton', 'expected_decimals' => 4],
    ['amount' => 12.34, 'currency' => 'usdttrc20', 'expected_decimals' => 2]
];

foreach ($amountTests as $test) {
    $formatted = formatCryptoAmount($test['amount'], $test['currency']);
    echo "   💰 {$test['amount']} {$test['currency']} → {$formatted}\n";
}

echo "\n🎯 ТЕСТ 4: ПРОВЕРКА БЛОКЧЕЙН ССЫЛОК\n";
echo str_repeat("-", 40) . "\n\n";

$blockchainTests = [
    ['currency' => 'eth', 'hash' => '0x123...abc', 'expected_domain' => 'etherscan.io'],
    ['currency' => 'btc', 'hash' => 'abc123...def', 'expected_domain' => 'blockstream.info'],
    ['currency' => 'usdttrc20', 'hash' => 'tron123...456', 'expected_domain' => 'tronscan.org'],
    ['currency' => 'ton', 'hash' => 'ton123...789', 'expected_domain' => 'tonscan.org']
];

echo "🔗 Проверка генерации ссылок на блокчейн:\n";
foreach ($blockchainTests as $test) {
    $url = generateBlockchainURL($test['currency'], $test['hash']);
    $containsDomain = strpos($url, $test['expected_domain']) !== false;
    
    echo "   " . ($containsDomain ? "✅" : "❌") . 
         " {$test['currency']}: {$url}\n";
}

echo "\n🎉 ТЕСТИРОВАНИЕ ОТОБРАЖЕНИЯ ЗАВЕРШЕНО!\n";

/**
 * Генерирует HTML для отображения выплаты
 */
function generateWithdrawalHTML($withdrawal) {
    $statusClass = getStatusCSSClass($withdrawal['status']);
    $statusText = getWithdrawalStatusText($withdrawal['status']);
    $formattedDate = formatWithdrawalDate($withdrawal);
    $shortAddress = shortenAddress($withdrawal['address']);
    $formattedAmount = formatCryptoAmount($withdrawal['crypto_amount'], $withdrawal['currency']);
    
    $html = "<div class=\"withdrawal-item {$statusClass}\">";
    $html .= "<div class=\"withdrawal-header\">";
    $html .= "<span class=\"withdrawal-id\">{$withdrawal['id']}</span>";
    $html .= "<span class=\"withdrawal-status\">{$statusText}</span>";
    $html .= "</div>";
    $html .= "<div class=\"withdrawal-details\">";
    $html .= "<div class=\"amount\">{$formattedAmount} {$withdrawal['currency']}</div>";
    $html .= "<div class=\"address\">{$shortAddress}</div>";
    $html .= "<div class=\"date\">{$formattedDate}</div>";
    $html .= "</div>";
    
    // Дополнительная информация для завершенных выплат
    if (in_array($withdrawal['status'], ['finished', 'completed', 'confirmed'])) {
        $html .= "<div class=\"withdrawal-success\">✅ Средства отправлены</div>";
        
        if (isset($withdrawal['transaction_hash'])) {
            $blockchainURL = generateBlockchainURL($withdrawal['currency'], $withdrawal['transaction_hash']);
            $html .= "<div class=\"blockchain-link\"><a href=\"{$blockchainURL}\" target=\"_blank\">🔗 Блокчейн</a></div>";
        }
    }
    
    // Информация об ошибках
    if (in_array($withdrawal['status'], ['failed', 'rejected'])) {
        $html .= "<div class=\"withdrawal-error\">❌ Выплата не удалась</div>";
        if (isset($withdrawal['error_message'])) {
            $html .= "<div class=\"error-message\">{$withdrawal['error_message']}</div>";
        }
    }
    
    // Кнопки действий
    if (in_array($withdrawal['status'], ['waiting', 'processing', 'sending'])) {
        $html .= "<div class=\"withdrawal-actions\">";
        $html .= "<button class=\"cancel-btn\" onclick=\"cancelWithdrawal('{$withdrawal['id']}')\">Отменить</button>";
        $html .= "</div>";
    }
    
    $html .= "</div>";
    
    return $html;
}

/**
 * Проверяет наличие правильного CSS класса статуса
 */
function checkStatusClass($html, $status) {
    $expectedClass = getStatusCSSClass($status);
    return strpos($html, $expectedClass) !== false;
}

/**
 * Проверяет правильность отображения текста статуса
 */
function checkStatusText($html, $status) {
    $expectedText = getWithdrawalStatusText($status);
    return strpos($html, $expectedText) !== false;
}

/**
 * Проверяет отображение суммы
 */
function checkAmountDisplay($html, $withdrawal) {
    $formattedAmount = formatCryptoAmount($withdrawal['crypto_amount'], $withdrawal['currency']);
    return strpos($html, $formattedAmount) !== false;
}

/**
 * Проверяет отображение даты
 */
function checkDateDisplay($html, $withdrawal) {
    // Проверяем что дата присутствует в каком-то виде
    return strpos($html, 'date') !== false;
}

/**
 * Проверяет отображение адреса
 */
function checkAddressDisplay($html, $withdrawal) {
    $shortAddress = shortenAddress($withdrawal['address']);
    return strpos($html, $shortAddress) !== false;
}

/**
 * Проверяет наличие кнопок действий
 */
function checkActionButtons($html, $withdrawal) {
    if (in_array($withdrawal['status'], ['waiting', 'processing', 'sending'])) {
        return strpos($html, 'cancel-btn') !== false;
    }
    return true; // Для завершенных статусов кнопки не нужны
}

/**
 * Проверяет дополнительную информацию
 */
function checkAdditionalInfo($html, $withdrawal) {
    if (in_array($withdrawal['status'], ['finished', 'completed', 'confirmed'])) {
        return strpos($html, 'Средства отправлены') !== false;
    }
    if (in_array($withdrawal['status'], ['failed', 'rejected'])) {
        return strpos($html, 'не удалась') !== false;
    }
    return true;
}

/**
 * Получает CSS класс для статуса
 */
function getStatusCSSClass($status) {
    $statusClasses = [
        'waiting' => 'status-pending',
        'pending' => 'status-pending',
        'processing' => 'status-processing',
        'sending' => 'status-processing',
        'finished' => 'status-completed',
        'completed' => 'status-completed',
        'confirmed' => 'status-completed',
        'failed' => 'status-failed',
        'rejected' => 'status-failed',
        'cancelled' => 'status-failed',
        'expired' => 'status-expired'
    ];
    
    return $statusClasses[$status] ?? 'status-unknown';
}

/**
 * Получает текст статуса для отображения
 */
function getWithdrawalStatusText($status) {
    $statusTexts = [
        'waiting' => 'Ожидание',
        'processing' => 'Обработка',
        'sending' => 'Отправка',
        'finished' => 'Отправлено',
        'completed' => 'Завершено',
        'confirmed' => 'Подтверждено',
        'failed' => 'Ошибка',
        'rejected' => 'Отклонено',
        'cancelled' => 'Отменено',
        'expired' => 'Истекло'
    ];
    
    return $statusTexts[$status] ?? 'Неизвестно';
}

/**
 * Форматирует дату выплаты
 */
function formatWithdrawalDate($withdrawal) {
    if (isset($withdrawal['timestamp'])) {
        return formatRelativeTime($withdrawal['timestamp']);
    }
    if (isset($withdrawal['created_at'])) {
        return date('d.m.Y H:i', strtotime($withdrawal['created_at']));
    }
    return 'Неизвестно';
}

/**
 * Форматирует относительное время
 */
function formatRelativeTime($timestamp) {
    $diff = time() - $timestamp;
    
    if ($diff < 60) {
        return 'Только что';
    } elseif ($diff < 3600) {
        $minutes = floor($diff / 60);
        return $minutes . ' мин назад';
    } elseif ($diff < 86400) {
        $hours = floor($diff / 3600);
        return $hours . ' ч назад';
    } else {
        $days = floor($diff / 86400);
        return $days . ' дн назад';
    }
}

/**
 * Сокращает адрес для отображения
 */
function shortenAddress($address) {
    if (strlen($address) <= 12) {
        return $address;
    }
    return substr($address, 0, 6) . '...' . substr($address, -4);
}

/**
 * Форматирует сумму криптовалюты
 */
function formatCryptoAmount($amount, $currency) {
    switch (strtolower($currency)) {
        case 'btc':
            return number_format($amount, 8, '.', '');
        case 'eth':
            return number_format($amount, 6, '.', '');
        case 'ton':
            return number_format($amount, 4, '.', '');
        default:
            return number_format($amount, 2, '.', '');
    }
}

/**
 * Генерирует URL для просмотра в блокчейне
 */
function generateBlockchainURL($currency, $hash) {
    switch (strtolower($currency)) {
        case 'eth':
            return "https://etherscan.io/tx/{$hash}";
        case 'btc':
            return "https://blockstream.info/tx/{$hash}";
        case 'usdttrc20':
        case 'trx':
            return "https://tronscan.org/#/transaction/{$hash}";
        case 'ton':
            return "https://tonscan.org/tx/{$hash}";
        default:
            return "https://etherscan.io/tx/{$hash}";
    }
}
?>
