<?php
/**
 * Проверка истории выплат пользователя
 */

require_once 'api/config.php';
require_once 'api/db_mock.php';

$userId = 5880288830; // ID пользователя для проверки

// Загружаем данные пользователя
$userData = loadUserData();

if (!isset($userData[$userId])) {
    die("Пользователь $userId не найден\n");
}

$withdrawals = $userData[$userId]['withdrawals'] ?? [];

echo "=== История выплат пользователя $userId ===\n";
echo "Всего выплат: " . count($withdrawals) . "\n\n";

foreach ($withdrawals as $withdrawal) {
    echo "ID: " . $withdrawal['id'] . "\n";
    echo "Статус: " . $withdrawal['status'] . "\n";
    echo "Сумма: " . $withdrawal['coins_amount'] . " монет\n";
    echo "Валюта: " . $withdrawal['currency'] . "\n";
    echo "Адрес: " . $withdrawal['address'] . "\n";
    echo "Дата: " . $withdrawal['created_at'] . "\n";
    echo "TXID: " . ($withdrawal['txid'] ?? 'отсутствует') . "\n";
    echo "Обновлено: " . ($withdrawal['updated_at'] ?? '') . "\n";
    echo str_repeat('-', 40) . "\n";
}

// Проверяем выплату с ID 5003151124
$specificWithdrawal = null;
foreach ($withdrawals as $w) {
    if (($w['id'] ?? '') === '5003151124' || ($w['payout_id'] ?? '') === '5003151124') {
        $specificWithdrawal = $w;
        break;
    }
}

if ($specificWithdrawal) {
    echo "\n=== Проверяемая выплата ===\n";
    echo "ID: 5003151124\n";
    echo "Статус: " . $specificWithdrawal['status'] . "\n";
    echo "TXID: " . ($specificWithdrawal['txid'] ?? 'отсутствует') . "\n";
    
    if ($specificWithdrawal['status'] === 'confirmed') {
        echo "✅ Статус корректный\n";
    } else {
        echo "❌ Статус должен быть 'confirmed'\n";
    }
} else {
    echo "\n❌ Выплата 5003151124 не найдена\n";
}
?>