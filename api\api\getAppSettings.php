<?php
/**
 * api/getAppSettings.php
 * Получение настроек приложения для фронтенда
 */

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/error.log');
error_reporting(E_ALL);

// Подключаем конфигурацию
require_once __DIR__ . '/config.php';

// Устанавливаем заголовки для JSON ответа
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Обрабатываем preflight запросы
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // Возвращаем настройки приложения
    $settings = [
        'success' => true,
        'settings' => [
            'show_fees_to_user' => defined('SHOW_FEES_TO_USER') ? SHOW_FEES_TO_USER : true,
            'conversion_rate' => defined('CONVERSION_RATE') ? CONVERSION_RATE : 0.001,
            'ad_view_reward' => defined('AD_VIEW_REWARD') ? AD_VIEW_REWARD : 10,
            'min_balance_for_withdrawal' => defined('MIN_BALANCE_FOR_WITHDRAWAL') ? MIN_BALANCE_FOR_WITHDRAWAL : 100,
            'min_withdrawal_amount' => defined('MIN_WITHDRAWAL_AMOUNT') ? MIN_WITHDRAWAL_AMOUNT : 0
        ]
    ];
    
    echo json_encode($settings);
    
} catch (Exception $e) {
    error_log('Ошибка в getAppSettings.php: ' . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'error' => 'Ошибка получения настроек приложения'
    ]);
}
?>
