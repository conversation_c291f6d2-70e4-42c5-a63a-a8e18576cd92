class AdManager {
  constructor() {
    this.API_BASE_URL = "./api";
    this.lastAdShownTime = 0;
    this.adCooldownTime = 3000; // 3 seconds
    this.isAdShowing = false;
    
    // Ad types
    this.AD_TYPES = {
      NATIVE_BANNER: 'native_banner',
      INTERSTITIAL: 'interstitial',
      REWARDED_VIDEO: 'rewarded_video'
    };
  }

  init() {
    console.log('[AdManager] Инициализация менеджера рекламы');
    this.setupEventListeners();
  }

  setupEventListeners() {
    // Watch Ad button
    const watchAdButton = document.getElementById("watchVideoButton");
    if (watchAdButton) {
      watchAdButton.addEventListener("click", () => this.showAd(this.AD_TYPES.REWARDED_VIDEO));
    }
    
    // Open Link button
    const openLinkButton = document.getElementById("openAdButton");
    if (openLinkButton) {
      openLinkButton.addEventListener("click", () => this.showAd(this.AD_TYPES.INTERSTITIAL));
    }
  }

  async showAd(adType) {
    if (this.isAdShowing) {
      console.log('[AdManager] Реклама уже показывается');
      return;
    }

    const currentTime = Date.now();
    if (currentTime - this.lastAdShownTime < this.adCooldownTime) {
      console.log('[AdManager] Слишком частый показ рекламы');
      return;
    }

    this.isAdShowing = true;
    this.lastAdShownTime = currentTime;
    
    try {
      // Simulate ad loading
      console.log(`[AdManager] Показ рекламы типа: ${adType}`);
      
      // In a real app, this would show the actual ad
      // For now, we'll simulate ad completion after 1 second
      setTimeout(async () => {
        await this.handleAdComplete(adType);
        this.isAdShowing = false;
      }, 1000);
      
    } catch (error) {
      console.error('[AdManager] Ошибка показа рекламы:', error);
      this.isAdShowing = false;
    }
  }

  async handleAdComplete(adType) {
    console.log(`[AdManager] Реклама ${adType} завершена, запись просмотра...`);
    
    try {
      const success = await this.recordAdView(adType);
      if (success) {
        console.log('[AdManager] Награда за рекламу успешно зачислена');
      } else {
        console.warn('[AdManager] Не удалось зачислить награду за рекламу');
      }
    } catch (error) {
      console.error('[AdManager] Ошибка обработки рекламы:', error);
    }
  }

  async recordAdView(adType) {
    const tg = window.Telegram?.WebApp;
    if (!tg?.initData) {
      console.error('[AdManager] Нет данных Telegram');
      return false;
    }

    try {
      const response = await fetch(`${this.API_BASE_URL}/recordAdView.php`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          initData: tg.initData,
          adType: adType
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();
      if (data.error) throw new Error(data.error);

      // Update balance
      if (data.newBalance !== undefined && window.balanceManager) {
        window.balanceManager.updateBalance(data.newBalance, 'ad_reward');
      }

      // Play sound effect
      if (window.coinsAudio && data.reward) {
        window.coinsAudio.playCoinsSound(data.reward);
      }

      return true;
    } catch (error) {
      console.error('[AdManager] Ошибка записи просмотра рекламы:', error);
      return false;
    }
  }
}

// Initialize ads manager
window.initAdsManager = function() {
  window.AdsManager = new AdManager();
  window.AdsManager.init();
  return Promise.resolve();
};