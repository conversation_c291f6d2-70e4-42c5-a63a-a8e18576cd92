# Защита API директории
# Разрешаем доступ только к определенным файлам

# Настройки для работы с Telegram мини-приложением
<IfModule mod_headers.c>
    # РАЗРЕШАЕМ отображение в iframe для Telegram
    Header always unset X-Frame-Options
    Header always set X-Frame-Options "ALLOWALL"

    # Разрешаем CORS для API запросов
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS, PUT, DELETE"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With, X-Telegram-Init-Data"

    # Для preflight запросов
    Header always set Access-Control-Max-Age "3600"
</IfModule>

# Обработка preflight запросов
RewriteEngine On
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]

# Запрещаем доступ ко всем файлам по умолчанию
<Files "*">
    Order Deny,Allow
    Deny from all
</Files>

# Разрешаем доступ к публичным API endpoints
<Files "getUserData.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "recordAdView.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "requestWithdrawal.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "getWithdrawalHistory.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "getReferralStats.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "registerReferral.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "getAppSettings.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "getUserLanguage.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "getCurrencyData.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "getCachedCurrencyData.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "getAvailableCurrencies.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "manual_update_rates.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "calculateWithdrawalAmount.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "calculateCrypto.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "cancelWithdrawal.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "checkUserWithdrawals.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "check_payout_status.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "check_rejected_payouts.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "forceUpdateWithdrawalStatuses.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "validate_initdata.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "withdrawal_callback.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "test_withdrawal_fixes.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "test_withdrawal_status_system.php">
    Order Allow,Deny
    Allow from all
</Files>

# Запрещаем доступ к конфигурационным и служебным файлам
<Files "config.php">
    Order Deny,Allow
    Deny from all
</Files>

<Files "db_mock.php">
    Order Deny,Allow
    Deny from all
</Files>

<Files "security.php">
    Order Deny,Allow
    Deny from all
</Files>

<Files "NOWPaymentsAPI.php">
    Order Deny,Allow
    Deny from all
</Files>

<Files "nowpayments_api.php">
    Order Deny,Allow
    Deny from all
</Files>

# Запрещаем доступ к файлам данных
<Files "user_data.json">
    Order Deny,Allow
    Deny from all
</Files>

# Запрещаем доступ к логам
<Files "*.log">
    Order Deny,Allow
    Deny from all
</Files>

# Запрещаем доступ к backup файлам
<Files "*.bak">
    Order Deny,Allow
    Deny from all
</Files>

<Files "*.backup">
    Order Deny,Allow
    Deny from all
</Files>
