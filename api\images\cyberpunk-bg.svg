<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Утонченные градиенты -->
    <linearGradient id="refinedGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00d4ff;stop-opacity:0.4" />
      <stop offset="50%" style="stop-color:#9d4edd;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#00f5ff;stop-opacity:0.4" />
    </linearGradient>

    <linearGradient id="refinedGradient2" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#9d4edd;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#ff7b00;stop-opacity:0.3" />
    </linearGradient>

    <radialGradient id="refinedGlow" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#00d4ff;stop-opacity:0.2" />
      <stop offset="100%" style="stop-color:#00d4ff;stop-opacity:0" />
    </radialGradient>
    
    <!-- Утонченные паттерны -->
    <pattern id="refinedGrid" x="0" y="0" width="80" height="80" patternUnits="userSpaceOnUse">
      <path d="M 80 0 L 0 0 0 80" fill="none" stroke="#00d4ff" stroke-width="0.3" opacity="0.2"/>
    </pattern>

    <pattern id="refinedCircuits" x="0" y="0" width="120" height="120" patternUnits="userSpaceOnUse">
      <rect width="120" height="120" fill="none"/>
      <circle cx="30" cy="30" r="1" fill="#00d4ff" opacity="0.3"/>
      <circle cx="90" cy="90" r="1" fill="#9d4edd" opacity="0.3"/>
      <line x1="30" y1="30" x2="90" y2="90" stroke="#00d4ff" stroke-width="0.5" opacity="0.2"/>
      <line x1="30" y1="90" x2="90" y2="30" stroke="#9d4edd" stroke-width="0.5" opacity="0.2"/>
    </pattern>
    
    <!-- Анимации -->
    <animateTransform id="rotate" attributeName="transform" type="rotate" 
                      values="0 960 540;360 960 540" dur="60s" repeatCount="indefinite"/>
    
    <animate id="pulse" attributeName="opacity" values="0.3;0.8;0.3" dur="3s" repeatCount="indefinite"/>
    
    <animate id="glow" attributeName="r" values="100;200;100" dur="4s" repeatCount="indefinite"/>
  </defs>
  
  <!-- Утонченный основной фон -->
  <rect width="1920" height="1080" fill="#0a0a0a"/>

  <!-- Утонченная сетка -->
  <rect width="1920" height="1080" fill="url(#refinedGrid)"/>

  <!-- Утонченные схемы -->
  <rect width="1920" height="1080" fill="url(#refinedCircuits)" opacity="0.4"/>

  <!-- Тонкие светящиеся элементы -->
  <g opacity="0.3">
    <circle cx="300" cy="300" r="60" fill="url(#refinedGlow)">
      <animateTransform attributeName="transform" type="rotate"
                        values="0 300 300;360 300 300" dur="40s" repeatCount="indefinite"/>
    </circle>

    <circle cx="1620" cy="300" r="50" fill="url(#refinedGlow)" opacity="0.8">
      <animateTransform attributeName="transform" type="rotate"
                        values="360 1620 300;0 1620 300" dur="45s" repeatCount="indefinite"/>
    </circle>

    <circle cx="300" cy="780" r="70" fill="url(#refinedGlow)" opacity="0.6">
      <animateTransform attributeName="transform" type="rotate"
                        values="0 300 780;360 300 780" dur="50s" repeatCount="indefinite"/>
    </circle>

    <circle cx="1620" cy="780" r="55" fill="url(#refinedGlow)" opacity="0.7">
      <animateTransform attributeName="transform" type="rotate"
                        values="360 1620 780;0 1620 780" dur="35s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Утонченные геометрические фигуры -->
  <g stroke="#00d4ff" stroke-width="1" fill="none" opacity="0.2">
    <polygon points="960,150 1040,220 960,290 880,220" >
      <animateTransform attributeName="transform" type="rotate"
                        values="0 960 220;360 960 220" dur="30s" repeatCount="indefinite"/>
    </polygon>

    <polygon points="450,450 520,490 480,570 410,530" stroke="#9d4edd">
      <animateTransform attributeName="transform" type="rotate"
                        values="360 465 510;0 465 510" dur="25s" repeatCount="indefinite"/>
    </polygon>

    <polygon points="1450,600 1520,640 1480,720 1410,680" stroke="#00f5ff">
      <animateTransform attributeName="transform" type="rotate"
                        values="0 1465 660;360 1465 660" dur="35s" repeatCount="indefinite"/>
    </polygon>
  </g>

  <!-- Утонченные энергетические линии -->
  <g stroke-width="1" opacity="0.3">
    <line x1="0" y1="350" x2="1920" y2="350" stroke="url(#refinedGradient1)">
      <animate attributeName="opacity" values="0.1;0.4;0.1" dur="4s" repeatCount="indefinite"/>
    </line>

    <line x1="0" y1="650" x2="1920" y2="650" stroke="url(#refinedGradient2)">
      <animate attributeName="opacity" values="0.4;0.1;0.4" dur="5s" repeatCount="indefinite"/>
    </line>

    <line x1="0" y1="850" x2="1920" y2="850" stroke="url(#refinedGradient1)">
      <animate attributeName="opacity" values="0.2;0.5;0.2" dur="4.5s" repeatCount="indefinite"/>
    </line>
  </g>
  
  <!-- Утонченный центральный элемент -->
  <g transform="translate(960, 540)" opacity="0.2">
    <circle r="30" fill="none" stroke="#00d4ff" stroke-width="1" opacity="0.6">
      <animate attributeName="r" values="30;50;30" dur="8s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6;0.2;0.6" dur="8s" repeatCount="indefinite"/>
    </circle>

    <circle r="20" fill="none" stroke="#9d4edd" stroke-width="0.5" opacity="0.4">
      <animate attributeName="r" values="20;40;20" dur="6s" repeatCount="indefinite"/>
      <animateTransform attributeName="transform" type="rotate"
                        values="0;360" dur="20s" repeatCount="indefinite"/>
    </circle>

    <circle r="3" fill="#00f5ff" opacity="0.5">
      <animate attributeName="opacity" values="0.5;0.2;0.5" dur="3s" repeatCount="indefinite"/>
    </circle>
  </g>

  <!-- Утонченные дополнительные эффекты -->
  <g opacity="0.15">
    <rect x="150" y="200" width="100" height="1" fill="#00d4ff">
      <animate attributeName="width" values="100;250;100" dur="6s" repeatCount="indefinite"/>
    </rect>

    <rect x="1400" y="600" width="150" height="1" fill="#9d4edd">
      <animate attributeName="width" values="150;80;150" dur="7s" repeatCount="indefinite"/>
    </rect>

    <rect x="400" y="900" width="120" height="1" fill="#00f5ff">
      <animate attributeName="width" values="120;280;120" dur="5s" repeatCount="indefinite"/>
    </rect>
  </g>
</svg>
