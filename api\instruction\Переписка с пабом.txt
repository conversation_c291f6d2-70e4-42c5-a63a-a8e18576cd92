<PERSON><PERSON><PERSON><PERSON>, [10.02.2025 23:15]
А динамически нельзя подгружать скрипт? при таком добавлении не получается window.applanza вызвать

Dmitriy, [10.02.2025 23:16]
У нас на первый экран, любой скрипт замедляет загрузку, поэтому мы избегаем такого подключения в пользу динамического. Но если нельзя никак динамически или через import() то сделаю async

Макс Власов, [10.02.2025 23:23]
а на чем у вас фронт? react?

<PERSON><PERSON><PERSON><PERSON>, [10.02.2025 23:23]
Да, видео щас заработало) Почему-то)

<PERSON><PERSON><PERSON><PERSON>, [10.02.2025 23:23]
Утечка памяти тут, колбеки onEvent, offEvent должны принимать одну и ту же функцию, .bind() создает каждый раз новую

Max Voitsekhovsky, [10.02.2025 23:26]
Нет, не нужен. Сейчас для видео достаточно вызвать метод showVideo()

Max Voitsekhovsky, [10.02.2025 23:26]
Какой у вас регион?

Max Voitsekhovsky, [10.02.2025 23:28]
На скрине не видно всего контекста, но в целом нет ограничений момента подключения скрипта

Dmitriy, [10.02.2025 23:28]
Spain

Max Voitsekhovsky, [10.02.2025 23:29]
в документации примеры работают при загрузке страницы, без таймаутов

Dmitriy, [10.02.2025 23:30]
я не понимаю какой контекст тут нужно дать? Оно грузит при загрузке компоненты на useEffect по сути)

Max Voitsekhovsky, [10.02.2025 23:32]
А где вызываете методы SDK? 

applanza.showVideo() ?

Dmitriy, [10.02.2025 23:32]
Тут с динамической загрузкой такая штука, я загрузил скрипт он в body есть, но в window applanza не создается объект(

Dmitriy, [10.02.2025 23:33]
Чуть ниже в колбеке на клик

Dmitriy, [10.02.2025 23:33]
можно на ты плиз) это интернет) Если тебе ок)

Max Voitsekhovsky, [10.02.2025 23:33]
Попробуйте без window.  ?

Max Voitsekhovsky, [10.02.2025 23:33]
оке

Max Voitsekhovsky, [10.02.2025 23:33]
принял

Dmitriy, [10.02.2025 23:33]
пробовал, не получается(

Dmitriy, [10.02.2025 23:34]
у вас никаких нету завязок на DOMContentLoaded или типо того события?

Dmitriy, [10.02.2025 23:34]
просто если динамикой грузить оно не наступит)

Max Voitsekhovsky, [10.02.2025 23:34]
Нет

Dmitriy, [10.02.2025 23:35]
Тогда странно, вот скрипт добавился но не создается applanza объект

Max Voitsekhovsky, [10.02.2025 23:35]
sdk.js должен в свою очередь добавить в <head/> еще один loader.js

Max Voitsekhovsky, [10.02.2025 23:36]
и уже loader.js дает доступ к объекту  applanza

Max Voitsekhovsky, [10.02.2025 23:36]
Есть такой скрипт в разметке?

Dmitriy, [10.02.2025 23:36]
loader.js не вижу

Max Voitsekhovsky, [10.02.2025 23:36]
В консоли ошибки?

Dmitriy, [10.02.2025 23:37]
нету такого, щас попробую не в body  добавить а в хедер

Dmitriy, [10.02.2025 23:38]
если добавлять в HTML сразу то появляется

Dmitriy, [10.02.2025 23:38]
если динамически - нет

Dmitriy, [10.02.2025 23:42]
Вот ваш метод, который подписывается на это событие из-за чего не срабатывает динамич загрузка

Dmitriy, [10.02.2025 23:44]
выход такой, дать ручной метод инициализации. Вот например такой initCdTma у онклики

Dmitriy, [10.02.2025 23:44]
для простоты пока оставлю на index.html загрузку, с динамикой позже разберемся