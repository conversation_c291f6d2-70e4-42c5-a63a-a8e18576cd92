/**
 * === telegram-emulator.js ===
 * Эмулятор Telegram WebApp API для тестирования в браузере
 * Создает полную имитацию Telegram WebApp окружения
 */

// Проверяем, есть ли уже Telegram объект
if (typeof window.Telegram === 'undefined') {
    console.log('🤖 [TELEGRAM-EMULATOR] Инициализация эмулятора Telegram WebApp...');
    
    // Создаем эмулятор Telegram WebApp
    window.Telegram = {
        WebApp: {
            // Основные свойства
            initData: 'query_id=AAHdF6IQAAAAAN0XohDhrOrc&user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22last_name%22%3A%22User%22%2C%22username%22%3A%22testuser%22%2C%22language_code%22%3A%22ru%22%7D&auth_date=1640995200&hash=test_hash_12345',
            initDataUnsafe: {
                query_id: 'AAHdF6IQAAAAAN0XohDhrOrc',
                user: {
                    id: 12345,
                    first_name: 'Test',
                    last_name: 'User',
                    username: 'testuser',
                    language_code: 'ru'
                },
                auth_date: 1640995200,
                hash: 'test_hash_12345'
            },
            version: '6.0',
            platform: 'web',
            colorScheme: 'dark',
            themeParams: {
                bg_color: '#1a1a1a',
                text_color: '#ffffff',
                hint_color: '#999999',
                link_color: '#ff6b35',
                button_color: '#ff6b35',
                button_text_color: '#ffffff'
            },
            isExpanded: true,
            viewportHeight: 600,
            viewportStableHeight: 600,
            headerColor: '#1a1a1a',
            backgroundColor: '#1a1a1a',
            isClosingConfirmationEnabled: false,
            
            // Методы для показа уведомлений
            showAlert: function(message) {
                console.log('🤖 [TELEGRAM-EMULATOR] Alert:', message);
                alert(`Telegram Alert: ${message}`);
            },
            
            showConfirm: function(message, callback) {
                console.log('🤖 [TELEGRAM-EMULATOR] Confirm:', message);
                const result = confirm(`Telegram Confirm: ${message}`);
                if (callback) callback(result);
                return result;
            },
            
            showPopup: function(params, callback) {
                console.log('🤖 [TELEGRAM-EMULATOR] Popup:', params);
                const message = params.message || params.title || 'Popup';
                alert(`Telegram Popup: ${message}`);
                if (callback) callback('ok');
            },
            
            // Методы для управления приложением
            ready: function() {
                console.log('🤖 [TELEGRAM-EMULATOR] WebApp ready');
            },
            
            expand: function() {
                console.log('🤖 [TELEGRAM-EMULATOR] WebApp expanded');
                this.isExpanded = true;
            },
            
            close: function() {
                console.log('🤖 [TELEGRAM-EMULATOR] WebApp closing');
                window.close();
            },
            
            // Методы для работы с данными
            sendData: function(data) {
                console.log('🤖 [TELEGRAM-EMULATOR] Sending data:', data);
            },
            
            openLink: function(url) {
                console.log('🤖 [TELEGRAM-EMULATOR] Opening link:', url);
                window.open(url, '_blank');
            },
            
            openTelegramLink: function(url) {
                console.log('🤖 [TELEGRAM-EMULATOR] Opening Telegram link:', url);
                window.open(url, '_blank');
            },
            
            // Haptic Feedback
            HapticFeedback: {
                impactOccurred: function(style) {
                    console.log(`🤖 [TELEGRAM-EMULATOR] Haptic impact: ${style}`);
                    // Можно добавить вибрацию если поддерживается
                    if (navigator.vibrate) {
                        const patterns = {
                            light: [10],
                            medium: [20],
                            heavy: [30]
                        };
                        navigator.vibrate(patterns[style] || patterns.medium);
                    }
                },
                
                notificationOccurred: function(type) {
                    console.log(`🤖 [TELEGRAM-EMULATOR] Haptic notification: ${type}`);
                    if (navigator.vibrate) {
                        const patterns = {
                            error: [100, 50, 100],
                            success: [50, 25, 50],
                            warning: [75, 25, 75]
                        };
                        navigator.vibrate(patterns[type] || patterns.success);
                    }
                },
                
                selectionChanged: function() {
                    console.log('🤖 [TELEGRAM-EMULATOR] Haptic selection changed');
                    if (navigator.vibrate) {
                        navigator.vibrate([5]);
                    }
                }
            },
            
            // Main Button (кнопка внизу экрана)
            MainButton: {
                text: '',
                color: '#ff6b35',
                textColor: '#ffffff',
                isVisible: false,
                isActive: true,
                isProgressVisible: false,
                
                setText: function(text) {
                    this.text = text;
                    console.log(`🤖 [TELEGRAM-EMULATOR] MainButton text: ${text}`);
                },
                
                show: function() {
                    this.isVisible = true;
                    console.log('🤖 [TELEGRAM-EMULATOR] MainButton shown');
                },
                
                hide: function() {
                    this.isVisible = false;
                    console.log('🤖 [TELEGRAM-EMULATOR] MainButton hidden');
                },
                
                enable: function() {
                    this.isActive = true;
                    console.log('🤖 [TELEGRAM-EMULATOR] MainButton enabled');
                },
                
                disable: function() {
                    this.isActive = false;
                    console.log('🤖 [TELEGRAM-EMULATOR] MainButton disabled');
                },
                
                showProgress: function() {
                    this.isProgressVisible = true;
                    console.log('🤖 [TELEGRAM-EMULATOR] MainButton progress shown');
                },
                
                hideProgress: function() {
                    this.isProgressVisible = false;
                    console.log('🤖 [TELEGRAM-EMULATOR] MainButton progress hidden');
                },
                
                onClick: function(callback) {
                    console.log('🤖 [TELEGRAM-EMULATOR] MainButton onClick set');
                    this._clickCallback = callback;
                },
                
                offClick: function(callback) {
                    console.log('🤖 [TELEGRAM-EMULATOR] MainButton onClick removed');
                    this._clickCallback = null;
                }
            },
            
            // Back Button
            BackButton: {
                isVisible: false,
                
                show: function() {
                    this.isVisible = true;
                    console.log('🤖 [TELEGRAM-EMULATOR] BackButton shown');
                },
                
                hide: function() {
                    this.isVisible = false;
                    console.log('🤖 [TELEGRAM-EMULATOR] BackButton hidden');
                },
                
                onClick: function(callback) {
                    console.log('🤖 [TELEGRAM-EMULATOR] BackButton onClick set');
                    this._clickCallback = callback;
                },
                
                offClick: function(callback) {
                    console.log('🤖 [TELEGRAM-EMULATOR] BackButton onClick removed');
                    this._clickCallback = null;
                }
            },
            
            // События
            onEvent: function(eventType, callback) {
                console.log(`🤖 [TELEGRAM-EMULATOR] Event listener added: ${eventType}`);
                // Можно добавить реальную обработку событий если нужно
            },
            
            offEvent: function(eventType, callback) {
                console.log(`🤖 [TELEGRAM-EMULATOR] Event listener removed: ${eventType}`);
            }
        }
    };
    
    // TelegramGameProxy эмуляция удалена - используется только реальная реклама
    
    console.log('✅ [TELEGRAM-EMULATOR] Эмулятор Telegram WebApp готов!');
    console.log('✅ [TELEGRAM-EMULATOR] Доступные объекты: window.Telegram.WebApp, window.TelegramGameProxy');
    
} else {
    console.log('ℹ️ [TELEGRAM-EMULATOR] Telegram WebApp уже доступен, эмулятор не нужен');
}
