<?php
/**
 * setup_webhook.php
 * Скрипт для установки webhook Telegram бота
 */

require_once __DIR__ . '/config.php';

echo "=== Установка Webhook для Telegram бота ===\n\n";

// Проверяем настройки
echo "1. Проверка настроек:\n";
echo "Токен бота: " . substr(BOT_TOKEN, 0, 10) . "...\n";
echo "URL webhook: " . WEBHOOK_URL . "\n";
echo "URL приложения: " . WEBAPP_URL . "\n\n";

// Проверяем доступность webhook URL
echo "2. Проверка доступности webhook:\n";
$webhookCheck = @file_get_contents(WEBHOOK_URL);
if ($webhookCheck !== false) {
    echo "✅ Webhook URL доступен\n";
} else {
    echo "❌ Webhook URL недоступен\n";
    echo "Убедитесь, что сервер работает и файл webhook.php доступен по указанному URL\n\n";
}

// Получаем информацию о боте
echo "3. Проверка подключения к Telegram API:\n";
$botInfo = telegramRequest('getMe');
if ($botInfo) {
    echo "✅ Подключение к API успешно\n";
    echo "Имя бота: " . $botInfo['first_name'] . "\n";
    echo "Username: @" . $botInfo['username'] . "\n";
    echo "ID: " . $botInfo['id'] . "\n\n";
} else {
    echo "❌ Ошибка подключения к Telegram API\n";
    echo "Проверьте токен бота и интернет-соединение\n\n";
    exit(1);
}

// Удаляем старый webhook (если есть)
echo "4. Удаление старого webhook:\n";
$deleteResult = telegramRequest('deleteWebhook');
if ($deleteResult) {
    echo "✅ Старый webhook удален\n";
} else {
    echo "⚠️ Не удалось удалить старый webhook (возможно, его не было)\n";
}

// Устанавливаем новый webhook
echo "\n5. Установка нового webhook:\n";
$webhookData = [
    'url' => WEBHOOK_URL,
    'allowed_updates' => ['message', 'callback_query'],
    'drop_pending_updates' => true // Удаляем старые обновления
];

$setResult = telegramRequest('setWebhook', $webhookData);
if ($setResult) {
    echo "✅ Webhook успешно установлен!\n";
} else {
    echo "❌ Ошибка установки webhook\n";
    exit(1);
}

// Проверяем установленный webhook
echo "\n6. Проверка установленного webhook:\n";
$webhookInfo = telegramRequest('getWebhookInfo');
if ($webhookInfo) {
    echo "URL: " . ($webhookInfo['url'] ?: 'не установлен') . "\n";
    echo "Статус: " . ($webhookInfo['has_custom_certificate'] ? 'с сертификатом' : 'без сертификата') . "\n";
    echo "Ожидающие обновления: " . ($webhookInfo['pending_update_count'] ?: 0) . "\n";
    echo "Последняя ошибка: " . ($webhookInfo['last_error_message'] ?: 'нет') . "\n";
    echo "Дата последней ошибки: " . ($webhookInfo['last_error_date'] ? date('Y-m-d H:i:s', $webhookInfo['last_error_date']) : 'нет') . "\n";
    
    if ($webhookInfo['url'] === WEBHOOK_URL) {
        echo "\n✅ Webhook настроен корректно!\n";
    } else {
        echo "\n❌ URL webhook не совпадает с ожидаемым\n";
    }
} else {
    echo "❌ Не удалось получить информацию о webhook\n";
}

// Тестируем webhook
echo "\n7. Тестирование webhook:\n";
echo "Отправьте боту сообщение /start для проверки работы\n";
echo "Бот: @" . $botInfo['username'] . "\n";
echo "Ссылка: https://t.me/" . $botInfo['username'] . "\n";

echo "\n=== Установка завершена ===\n";

// Логируем установку
botLog("INFO: Webhook установлен: " . WEBHOOK_URL);
?>
