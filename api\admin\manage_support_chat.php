<?php
/**
 * api/admin/manage_support_chat.php
 * API для управления чатами поддержки (изменение статуса, удаление)
 */

session_start();

// Проверяем авторизацию администратора
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

require_once __DIR__ . '/support_data.php';

// Устанавливаем заголовки для JSON ответа
header('Content-Type: application/json; charset=utf-8');

// Проверяем метод запроса
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

// Получаем данные из POST запроса
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    // Пробуем получить данные из $_POST
    $input = $_POST;
}

// Проверяем обязательные параметры
if (!isset($input['action']) || !isset($input['chat_id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Missing required parameters']);
    exit;
}

$action = $input['action'];
$chatId = $input['chat_id'];

// Проверяем существование чата
$chats = getSupportChats();
if (!isset($chats[$chatId])) {
    http_response_code(404);
    echo json_encode(['success' => false, 'error' => 'Chat not found']);
    exit;
}

try {
    switch ($action) {
        case 'update_status':
            if (!isset($input['status'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'error' => 'Status parameter required']);
                exit;
            }
            
            $status = $input['status'];
            $validStatuses = ['open', 'closed'];
            
            if (!in_array($status, $validStatuses)) {
                http_response_code(400);
                echo json_encode(['success' => false, 'error' => 'Invalid status']);
                exit;
            }
            
            $result = updateChatStatus($chatId, $status);
            
            if ($result) {
                // Получаем обновленную информацию о чате
                $updatedChats = getSupportChats();
                $chatInfo = $updatedChats[$chatId];
                
                echo json_encode([
                    'success' => true,
                    'message' => 'Chat status updated successfully',
                    'chat' => $chatInfo
                ]);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'error' => 'Failed to update chat status']);
            }
            break;
            
        case 'delete':
            // Получаем информацию о чате перед удалением для логирования
            $chatInfo = $chats[$chatId];
            $userName = $chatInfo['first_name'];
            if ($chatInfo['last_name']) {
                $userName .= ' ' . $chatInfo['last_name'];
            }
            
            $result = deleteSupportChat($chatId);
            
            if ($result) {
                // Логируем удаление чата
                $logMessage = date('Y-m-d H:i:s') . " - Chat deleted: {$chatId} (User: {$userName}, ID: {$chatInfo['user_id']})\n";
                file_put_contents(SUPPORT_LOG_FILE, $logMessage, FILE_APPEND | LOCK_EX);
                
                echo json_encode([
                    'success' => true,
                    'message' => 'Chat deleted successfully',
                    'deleted_chat_id' => $chatId
                ]);
            } else {
                http_response_code(500);
                echo json_encode(['success' => false, 'error' => 'Failed to delete chat']);
            }
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Invalid action']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Server error: ' . $e->getMessage()
    ]);
}
?>
